<template>
  <transition name="el-alert-fade" @after-leave="afterLeave">
    <div
      v-show="isVisible"
      class="el-alert"
      :class="[typeClass, center ? 'is-center' : '', 'is-' + effect, isDense]"
      role="alert"
    >
      <i v-if="showIcon" class="el-alert__icon" :class="[ iconClass, isBigIcon ]" />
      <div class="el-alert__content">
        <span v-if="title || $slots.title" class="el-alert__title" :class="[ isBoldTitle ]">
          <slot name="title">{{ title }}</slot>
        </span>
        <p v-if="$slots.default && !description" class="el-alert__description"><slot /></p>
        <p v-if="description && !$slots.default" class="el-alert__description">{{ description }}</p>
        <i v-show="closable" class="el-alert__closebtn" :class="{ 'is-customed': closeText !== '', 'el-icon-close': closeText === '' }" @click="close()">{{ closeText }}</i>
      </div>
    </div>
  </transition>
</template>

<script>
const TYPE_CLASSES_MAP = {
  'success': 'el-icon-success',
  'warning': 'el-icon-warning',
  'error': 'el-icon-error',
  'help': 'el-icon-info'
}
export default {
  name: 'Alert',

  props: {
    borderStyle: {
      type: String,
      default: 'solid'
    },
    title: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'info'
    },
    closable: {
      type: Boolean,
      default: true
    },
    closeText: {
      type: String,
      default: ''
    },
    showIcon: Boolean,
    center: Boolean,
    dense: Boolean,
    effect: {
      type: String,
      default: 'light',
      validator: function(value) {
        return ['light', 'dark'].indexOf(value) !== -1
      }
    },
    value: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      visible: this.value
    }
  },

  computed: {
    typeClass() {
      return `el-alert--${this.type}`
    },

    iconClass() {
      return TYPE_CLASSES_MAP[this.type] || 'el-icon-info'
    },

    isBigIcon() {
      return this.description || this.$slots.default ? 'is-big' : ''
    },

    isBoldTitle() {
      return this.description || this.$slots.default ? 'is-bold' : ''
    },

    isDense() {
      return this.dense ? 'is-dense' : ''
    },

    isVisible() {
      return this.value
    }
  },
  methods: {
    close() {
      this.visible = false
      this.$emit('input', this.visible)
      this.$emit('close')
    },
    afterLeave() {
      this.$emit('afterClose')
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~element-ui/packages/theme-chalk/src/mixins/mixins";
@include b(alert) {
  @include m(help) {
    &.is-light {
      background-color: #ecf5fa;
      color: #1890ff;
      border: 2px dashed #d0ecfb;

      .el-alert__description {
        color: #000;
      }
      .el-alert__title {
        color: #000
      }
    }

    &.is-dark {
      background-color: #1890ff;
      color: $--color-white;
    }
  }
  &.is-dense {
    padding: 5px 16px;

    .el-alert__closebtn {
      top: 9px;
    }
    .el-alert__title {
      font-size: 12px;
    }
  }
}
</style>

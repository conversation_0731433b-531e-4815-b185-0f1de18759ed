'use strict'
const path = require('path')
const defaultSettings = require('./src/settings.js')
const chalk = require('chalk')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = defaultSettings.title || 'vue Admin Template' // page title

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following methods:
// port = 9528 npm run dev OR npm run dev --port = 9528
const port = process.env.port || process.env.npm_config_port || 9528 // dev port
const VUE_APP_BASE_API = process.env.VUE_APP_BASE_API
console.log('ENV', process.env.ENV)
console.log(chalk.green('当前环境变量：' + process.env.NODE_ENV + '======'))
console.log('baseUrl:' + VUE_APP_BASE_API)
console.log('publicPath:' + process.env.VUE_APP_PUBLIC_PATH)
// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to '/bar/'.
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  publicPath: process.env.VUE_APP_PUBLIC_PATH,
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  devServer: {
    disableHostCheck: true,
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      // ---------------------------------------- 本地环境配置 ----------------------------------------
      '/api/acc/': {
        // target: process.env.VUE_APP_BASE_URL || 'https://codecloud.guoyaoplat.com', // 需要请求的目标接口
        // target: process.env.VUE_APP_BASE_URL || 'http://************:8090', // 需要请求的目标接口
        // target: 'http://127.0.0.1:8090/', // 需要请求的目标接口
        target: 'https://ztpat.guoyaoplat.com/', // 需要请求的目标接口
        // target: 'http://*************:8090/', // 需要请求的目标接口
        // target: process.env.VUE_APP_BASE_URL || 'http://*************:8081/', // 需要请求的目标接口
        pathRewrite: { '^/api/acc/': '/acc/' },
        changeOrigin: true
      },
      '/api/inner/acc/': {
        // target: 'http://127.0.0.1:8090/', // 需要请求的目标接口
        target: 'https://ztpat.guoyaoplat.com/', // 需要请求的目标接口
        // target: 'http://*************:8090/', // 需要请求的目标接口
        pathRewrite: { '^/api/inner/acc/': '/acc/' },
        changeOrigin: true
      }
      // ---------------------------------------- 测试境配置 ----------------------------------------
      // '/gateway/': {
      //   target: process.env.VUE_APP_BASE_URL || 'http://60.204.140.8/', // 需要请求的目标接口
      //   pathRewrite: { '^/gateway/': '/gateway/' },
      //   changeOrigin: true
      // },
      // '/api/': {
      //   target: process.env.VUE_APP_BASE_URL || 'http://60.204.140.8/', // 需要请求的目标接口
      //   pathRewrite: { '^/api/': '/api/' },
      //   changeOrigin: true
      // }
    }
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    }
  },
  chainWebpack(config) {
    // it can improve the speed of the first screen, it is recommended to turn on preload
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])
    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete('prefetch')

    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    config.when(process.env.NODE_ENV !== 'development', config => {
      config.plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [{
          // `runtime` must same as runtimeChunk name. default is `runtime`
          inline: /runtime\..*\.js$/
        }])
        .end()
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial' // only package third parties that are initially dependent
          },
          elementUI: {
            name: 'chunk-elementUI', // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
          },
          commons: {
            name: 'chunk-commons',
            test: resolve('src/components'), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true
          }
        }
      })
      // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
      config.optimization.runtimeChunk('single')
    })
  }
}

<template>
  <div class="design-main">
    <div class="design-left">
      <template-design-config ref="config" @clickEvent="clickEvent" />
    </div>
    <div class="design-center">
      <div class="template-render-wrapper">
        <template-render :product-id="productId" :render-list="renderList" />
      </div>
    </div>
    <div class="design-right">
      <template-design-prop />
    </div>
  </div>
</template>
<script>

import TemplateDesignConfig from './template-design-config.vue'
import TemplateDesignProp from './template-design-prop.vue'
import TemplateRender from './template-render.vue'

export default {
  name: 'TemplateDesign',
  components: { TemplateRender, TemplateDesignProp, TemplateDesignConfig },
  provide() {
    return {
      templateDesign: {
        renderList: this.renderList,
        active: this.active
      }
    }
  },
  data() {
    return {
      renderList: [],
      productId: '',
      active: { id: null, type: null, renderProps: null }
    }
  },
  mounted() {
  },
  methods: {
    back() {
      this.$router.replace({ path: 'productTemplate' })
    },
    getConfig(callback) {
      this.$refs.config.getForm((form) => {
        if (this.renderList.length > 0) {
          callback({
            ...form,
            config: JSON.stringify(this.renderList)
          })
        } else {
          this.$message.warning('请配置显示容器')
        }
      })
    },
    setConfig(design) {
      const { config, name, productId } = design
      const _config = JSON.parse(config)
      _config.forEach(item => {
        this.renderList.push(item)
      })

      this.$refs.config.setForm({
        name,
        productId
      })
    },
    clickEvent(val) {
      this.productId = val
    }
  }
}
</script>

<style scoped lang="scss">
  .design-main {
    display: flex;
    height: 100%;
  }
  .design-left, .design-right {
    flex: 0 0 430px;
  }
  .design-center {
    flex: 1;
    min-width: 360px;
  }
  .design-left, .design-center, .design-right {
    height: 100%;
    overflow: auto;
  }
  .design-center {
  display: flex;
    justify-content: center;
  }
  .template-render-wrapper {
    //background: url(../../../../assets/img/mobile.png) no-repeat;
    flex-shrink: 0;
    background-size: contain;
    //height: 750px;
    //width: 360px;
    //padding: 40px 13px 20px 13px
  }
</style>

<template>
  <section class="app-main">
    <div>
      <el-container class="sub-container" :style="subContainerStyle">
        <el-header height="auto" class="alert-container">
          <alert
            v-if="alert"
            type="help"
            :class="[helpClass]"
            :title="helpMessage"
            show-icon
            dense
            @close="onAlertClose"
          />
        </el-header>
        <el-main style="padding: 0; overflow: hidden">
          <div style="height: 100%">
            <transition
              name="fade-transform"
              mode="out-in"
            >
              <keep-alive :include="cachedViews" :exclude="['TagRule','TagDetail','TagGenerate','Record','Statistic','ProductBatch','Record','Test','test']">
                <router-view ref="routerView" />
              </keep-alive>
            </transition>
          </div>
        </el-main>
      </el-container>
    </div>
    <!-- <router-view :key="key" /> -->
  </section>
</template>

<script>
import { mapGetters } from 'vuex'
import Alert from '@/components/Alert'
import VueEvent from '@/utils/vue-event'
// import axios from 'axios'
export default {
  name: 'AppMain',
  components: {
    Alert
  },
  data() {
    return {
      alert: false,
      subContainerStyle: {
        marginTop: '10px',
        height: 'calc(100vh - 95px)'
      }
    }
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.name
    },
    ...mapGetters(['helpMessage', 'helpClass'])
  },
  watch: {
    $route(val) {
      this.subContainerStyle.marginTop = '0'
      this.subContainerStyle.height = 'calc(100vh - 85px)'
      this.$store.dispatch('app/setHelpMessage', null)
      if (val.name !== 'redirect') { this.$store.dispatch('tagsView/setCurrentRoute', { name: val.name, query: val.query, params: val.params }) }
    },
    helpMessage(val) {
      if (val) {
        this.alert = true
        this.subContainerStyle.marginTop = '10px'
        this.subContainerStyle.height = 'calc(100vh - 105px)'
      } else this.alert = false
    }
  },
  created() {
    this.subContainerStyle.marginTop = '0'
    this.subContainerStyle.height = 'calc(100vh - 85px)'
    const currentRoute = this.$store.state.tagsView.currentRoute
    if (currentRoute && currentRoute.name === this.$route.name) {
      Object.keys(currentRoute.params).forEach(key => {
        this.$route.params[key] = currentRoute.params[key]
      })
      Object.keys(currentRoute.query).forEach(key => {
        this.$route.query[key] = currentRoute.query[key]
      })
    }
  },
  methods: {
    onAlertClose() {
      this.subContainerStyle.marginTop = '0'
      this.subContainerStyle.height = 'calc(100vh - 85px)'
      this.$store.dispatch('app/setHelpMessage', null)
      this.$nextTick(() => {
        VueEvent.$emit('data.table.resize')
      })
    }
  }
}
</script>

<style scoped>
.app-main {
  /*50 = navbar  */
  min-height: calc(100vh - 93px);
  width: 100%;
  position: relative;
  overflow: hidden;
  background: #FFFFFF;
}
.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView .app-main {
  /* 84 = navbar + tags-view = 50 + 34 */
  min-height: calc(100vh - 97px);
}

.hasTagsView .fixed-header + .app-main {
  padding-top: 97px;
}
.el-breadcrumb{
  padding: 15px 15px 0px 15px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
.el-breadcrumb__separator{
  color: #000000;
}
.el-breadcrumb__item .el-breadcrumb__inner{
  color: #000000;
}
.el-breadcrumb__item:last-child .el-breadcrumb__inner{
  color: rgba(24,144,255,1);
}
.sub-container {
  .alert-container{
    padding: 0 10px
  }
}

</style>

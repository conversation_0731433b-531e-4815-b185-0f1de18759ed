<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="(data) => {searchHelper.search(data)}"
      @return-reset="searchHelper.reset"
    />
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="() => {searchHelper.handleQuery()}"
    />

  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect/index.vue'
import DataTable from '@/components/DataTable/index.vue'
import acc from '@/api/acc/acc'

export default {
  name: 'TagRule',
  components: { DataSelect, DataTable },
  data() {
    return {
      buttonData: [
        {
          label: '系统默认规则配置',
          action: () => {
            return this.onEditOneClick(1)
          },
          permission: 'all'
        },
        {
          label: '+新增',
          action: this.onAddClick,
          permission: 'all'
        }
      ],
      searchHelper: new this.$searchHelper({ api: acc.tagRuleDateListApi }),
      dataList: [],

      search: {
        searchText: {
          label: '规则名称/对象名称',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入规则名称/对象名称'
          }
        }
      },
      selection: [],
      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            sortable: false
          },
          {
            label: '规则名称',
            prop: 'ruleName'
          },
          {
            label: '对象名称',
            prop: 'productName'

          }, {
            label: '规则拼接',
            prop: 'ruleDetail'
          },
          {
            label: '编码预览',
            prop: 'codePreview'
          },

          {
            label: '创建人',
            prop: 'userName'
          },
          {
            label: '创建时间',
            prop: 'createTime'
          }
        ],
        operation: {
          label: '操作',
          width: '200px',

          data: [
            {
              label: '配置规则',
              action: this.onEditClick,
              permission: 'all'
            }, {
              label: '复制规则',
              action: this.copyEditClick,
              permission: 'all'
            }, {
              label: '删除',
              action: this.onDelClick,
              permission: 'all'
            }]
        }
      }
    }
  },
  mounted() {
    this.searchHelper.handleQuery()
  },
  methods: {

    onEditOneClick(type) {
      this.$router.replace({ path: '/accPortal/enterprise/tagMg/tagRuleDetail', query: { id: '1899642566419685377', addType: type, type: 1 }})
    },
    onAddClick() {
      this.$router.replace({ path: '/accPortal/enterprise/tagMg/tagRuleDetail' })
    },
    // 编辑
    onEditClick(row) {
      this.$router.replace({ path: '/accPortal/enterprise/tagMg/tagRuleDetail', query: { id: row.id, type: 1 }})
    },
    // 复制
    copyEditClick(row) {
      this.$router.replace({ path: '/accPortal/enterprise/tagMg/tagRuleDetail', query: { id: row.id }})
    },
    onDelClick(row) {
      this.$confirm('请确认是否删除?', '警告').then(() => {
        acc.delTagRuleApi({ id: row.id }).then(() => {
          this.$message.success('操作成功!')
          this.searchHelper.handleQuery()
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
</style>

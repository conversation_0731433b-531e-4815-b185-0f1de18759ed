<template>
  <div :id="idName" class="chart-box mt20 mb20" />
</template>

<script>
export default {
  name: 'Chart',
  components: {},
  props: {
    idName: {
      required: true,
      type: String
    },
    series: {
      type: Array
    },
    title: {
      type: String,
      default: ''
    },
    xAxis: {
      type: Array
    },
    resetOptions: {
      type: Object,
      default: () => ({})
    },
    // 是否监听点击事件
    isClick: {
      type: Boolean,
      default: false
    },
    // 下载图片
    // toolbox: {
    //   type: Object,
    //   default: () => ({
    //     feature: {
    //       saveAsImage: {},
    //     },
    //   }),
    // },
    // 颜色
    color: {
      type: Array,
      default: () => [
        '#4B6FED',
        '#00C3D1',
        '#FECC20',
        '#FF944C',
        '#FF7372',
        '#FA75C4',
        '#B368FF',
        '#b607f6',
        '#c10d70',
        '#5194cb'
      ]
    }, // 折线图颜色
    barColor: {
      type: Array,
      default: () => [
        '#5194cb',
        '#5dcff1',
        '#39c9e0',
        '#2aece5',
        '#6895ef',
        '#4052ee',
        '#3470ea',
        '#0743f6',
        '#8B78F6'
      ]
    }
  },
  data() {
    return {
      option: {},
      myChart: null
    }
  },
  watch: {
    series: {
      handler() {
        this.drawEcharts()
      },
      deep: true
    },
    resetOptions: {
      handler() {
        this.drawEcharts()
      },
      deep: true
    }
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.myChart = this.$echarts.init(document.getElementById(this.idName))
      this.drawEcharts()
    })

    // 监听系统窗口变化，自适应调整echarts图表
    window.addEventListener('resize', () => {
      this.myChart && this.myChart.resize()
    })
  },
  beforeDestroy() {
    this.myChart = null
  },
  methods: {
    // 绘画图表
    drawEcharts() {
      this.option = {
        color: this.color,
        title: {
          text: this.title,
          textStyle: {
            color: '#535253',
            fontWeight: 'normal',
            fontSize: 15
          }
        },
        tooltip: {
          trigger: 'axis',
          confine: true // 解决超出外部容器后被遮挡问题
        },
        legend: {
          show: true
        },
        grid: {
          left: '15px',
          right: '30px',
          bottom: '30px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: this.xAxis
        },
        yAxis: {
          type: 'value'
        },
        series: this.series,
        ...this.resetOptions
      }
      this.myChart.setOption(this.option)

      // 是否监听点击事件
      if (this.isClick) {
        this.myChart.off('click')
        this.myChart.on('click', (param) => {
          this.$emit('clickEvent', param, this.idName)
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.chart-box {
  width: 100%;
  height: 250px;
  //border-bottom: 1px solid #dedede;
}
</style>

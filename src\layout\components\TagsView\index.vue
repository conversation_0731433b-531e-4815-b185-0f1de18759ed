<template>
  <div
    id="tags-view-container"
    class="tags-view-container"
  >
    <el-tabs v-model="editableTabsValue" type="card" @tab-remove="closeSelectedTag">
      <el-tab-pane
        v-for="(tag) in visitedViews"
        :key="tag.name"
        :label="currentLang === 'zh' ? tag.title : tag.titleEn"
        :name="tag.name"
        closable
      >
        <router-link
          slot="label"
          :ref="'tag'"
          :key="tag.path"
          :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
          tag="span"
          class="tags-view-item"
          @contextmenu.prevent.native.stop="openMenu(tag,tag.name)"
        >

          <!--          @contextmenu.prevent.native="openMenu(tag,tag.name)"-->
          <!--          @contextmenu.prevent.native="openMenu(tag,$event)"-->
          <!--          @click.middle.native="!isAffix(tag)?closeSelectedTag(editableTabsValue):''"-->
          <el-popover
            :ref="'fadeInLinearTagsViewsOperation'+tag.name"
            placement="bottom-start"
            trigger="contextmenu"
            :transition="'fade-in-linear-tags-views-operation'"
            :popper-class="'table-action-down table-action-down-tab'"
          >
            <div>
              <span>
                <span class="table-action-down-span" @click.stop="refreshSelectedTag(tag, false)" v-html="$t('common.refresh')" />
                <el-divider />
                <span class="table-action-down-span" @click.stop="closeSelectedTag(tag.name)" v-html="$t('common.closeTitle')" />
                <el-divider />
                <span class="table-action-down-span" @click.stop="closeOthersTags(tag.name)" v-html="$t('common.closeOthers')" />
                <el-divider />
                <span class="table-action-down-span" @click.stop="closeAllTags(tag)" v-html="$t('common.closeAll')" />
              </span>
            </div>
            <span slot="reference" class="tag-span">{{ currentLang === 'zh' ? tag.title : tag.titleEn }}</span>
          </el-popover>
        </router-link>
      </el-tab-pane>
    </el-tabs>
    <el-popover
      :ref="'fadeInLinearTagsViewsOperationMenu'"
      placement="bottom-start"
      trigger="hover"
      :transition="'fade-in-linear-tags-views-operation'"
      :popper-class="'table-action-down table-action-down-tab'"
    >
      <div>
        <span>
          <span class="table-action-down-span" @click.stop="refreshSelectedTag(selectedTag, true)" v-html="$t('common.refresh')" />
          <el-divider />
          <span class="table-action-down-span" @click.stop="closeSelectedTag(editableTabsValue, true)" v-html="$t('common.closeTitle')" />
          <el-divider />
          <span class="table-action-down-span" @click.stop="closeOthersTags(editableTabsValue, true)" v-html="$t('common.closeOthers')" />
          <el-divider />
          <span class="table-action-down-span" @click.stop="closeAllTags(selectedTag, true)" v-html="$t('common.closeAll')" />
        </span>
      </div>
      <div slot="reference" class="right-menu">
        <span class="vab-tabs-more">
          <span class="vab-tabs-more-icon">
            <i class="box box-t" />
            <i class="box box-b" />
          </span>
        </span>
      </div>
    </el-popover>

    <!--    <ul-->
    <!--      v-show="visible"-->
    <!--      :style="{left:left+'px',top:top+'px'}"-->
    <!--      class="contextmenu"-->
    <!--    >-->
    <!--      <li @click="refreshSelectedTag(selectedTag)">Refresh</li>-->
    <!--      <li-->
    <!--        v-if="!isAffix(selectedTag)"-->
    <!--        @click="closeSelectedTag(selectedTag)"-->
    <!--      >Close</li>-->
    <!--      <li @click="closeOthersTags">Close Others</li>-->
    <!--      <li @click="closeAllTags(selectedTag)">Close All</li>-->
    <!--    </ul>-->
  </div>
</template>

<script>
import path from 'path'
import Cookies from 'js-cookie'
import VueEvent from '@/utils/vue-event'

export default {
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      editableTabsValue: '1'
    }
  },
  computed: {
    visitedViews() {
      return this.$store.state.tagsView.visitedViews
    },
    routes() {
      return this.$store.state.permission.routes
    },
    currentLang() {
      return Cookies.get('language') == null ? 'zh' : Cookies.get('language')
    }
  },
  watch: {
    $route() {
      this.addTags()
      this.moveToCurrentTag()
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  beforeMount() {
    const affixTags = this.initTags()
    this.addTags()
    this.$nextTick(() => {
      const { name } = this.$route
      if (!name && affixTags?.length > 0) this.$router.replace(affixTags[0])

      for (const index in this.visitedViews) {
        if (this.isActive(this.visitedViews[index])) {
          this.editableTabsValue = this.visitedViews[index].name
          this.selectedTag = this.visitedViews[index]
        }
      }
    })
    VueEvent.$on('layout.close.allTagViews', this.closeAllTags)
    VueEvent.$on('layout.close.closeOtherViewAndRefresh', this.closeOtherViewAndRefresh)
    VueEvent.$on('layout.refresh.currenViews', this.refreshSelectedTag)
  },
  beforeDestroy() {
    VueEvent.$off('layout.close.allTagViews')
    VueEvent.$off('layout.close.closeOtherViewAndRefresh')
    VueEvent.$off('layout.refresh.currenViews')
  },
  methods: {
    isActive(route) {
      return route.path === this.$route.path || route.subPath === this.$route.path
    },
    isAffix(tag) {
      return tag.meta && tag.meta.affix
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      routes.forEach((route) => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path)
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta }
          })
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path)
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags]
          }
        }
      })
      return tags
    },
    initTags() {
      const affixTags = (this.affixTags = this.filterAffixTags(this.routes))
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.$store.dispatch('tagsView/addVisitedView', tag)
        }
      }
      if (affixTags?.length > 0) this.editableTabsValue = affixTags[affixTags.length - 1].name
      return affixTags
    },
    addTags() {
      const { name } = this.$route
      if (name) {
        this.$store.dispatch('tagsView/addView', this.$route)
        this.editableTabsValue = name
      }
      return false
    },
    moveToCurrentTag() {
      this.$nextTick(() => {
        const tags = this.$refs.tag
        for (const tag of tags) {
          if (tag.to.path === this.$route.path) {
            // this.$refs.scrollPane.moveToTarget(tag)
            // when query is different then update
            if (tag.to.fullPath !== this.$route.fullPath) {
              this.$store.dispatch('tagsView/updateVisitedView', this.$route)
            }
            break
          }
        }
        this.$nextTick(() => {
          for (const index in this.visitedViews) {
            if (this.isActive(this.visitedViews[index])) {
              this.editableTabsValue = this.visitedViews[index].name
              this.selectedTag = this.visitedViews[index]
            }
          }
        })
      })
    },
    refreshSelectedTag(view, isRightMenu) {
      this.$store.dispatch('tagsView/delCachedView', view).then(() => {
        const { fullPath } = view
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect' + fullPath
          })
        })
        if (!isRightMenu) {
          isRightMenu !== undefined && this.$refs[`fadeInLinearTagsViewsOperation${view.name}`][0].doToggle()
        } else {
          this.$refs[`fadeInLinearTagsViewsOperationMenu`].doToggle()
        }
      })
    },
    closeSelectedTag(name) {
      const view = this.visitedViews.filter(value => value.name === name)[0]
      this.$store
        .dispatch('tagsView/delView', view)
        .then(({ visitedViews }) => {
          if (this.isActive(view)) {
            this.toLastView(visitedViews, view)
          }
          if (view.subName) {
            this.$store.dispatch('tagsView/delCachedView', { name: view.subName })
          }
          // 关闭是当前点击的标签，ref已为undefined，不需要切换popove
          // this.$refs[`fadeInLinearTagsViewsOperation${name}`][0].doToggle()
        })
    },
    closeOthersTags(name, isRightMenu) {
      const view = this.visitedViews.filter(value => value.name === name)[0]
      this.selectedTag = view
      this.editableTabsValue = view.name
      this.$router.push(this.selectedTag)
      this.$store
        .dispatch('tagsView/delOthersViews', this.selectedTag)
        .then(() => {
          this.moveToCurrentTag()
          if (isRightMenu) {
            this.$refs[`fadeInLinearTagsViewsOperationMenu`].doToggle()
          }
        })
    },
    closeAllTags(view, isRightMenu) {
      if (!view) view = this.selectedTag
      this.$store.dispatch('tagsView/delAllViews').then(({ visitedViews }) => {
        if (this.affixTags.some((tag) => tag.path === view.path)) {
          return
        }
        this.toLastView(visitedViews, view)
        if (!isRightMenu) {
          this.$refs[`fadeInLinearTagsViewsOperation${view.name}`][0]?.doToggle()
        } else {
          this.$refs[`fadeInLinearTagsViewsOperationMenu`].doToggle()
        }
      })
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView.fullPath)
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        if (view.name === 'Dashboard') {
          // to reload home page
          this.$router.replace({ path: '/redirect' + view.fullPath })
        } else {
          this.$router.push('/')
        }
      }
    },
    openMenu(tag, name) {
      const elementList = this.$refs['fadeInLinearTagsViewsOperation' + name]
      if (elementList?.length >= 0) {
        elementList.forEach(val => { val.doToggle() })
      } else if (elementList) {
        elementList.doToggle()
      }
    },
    closeMenu() {
      this.visible = false
    },
    handleScroll() {
      this.closeMenu()
    },
    // 关闭其他并且刷新当前
    closeOtherViewAndRefresh(name) {
      const view = this.visitedViews.filter(value => value.name === name)[0]
      this.$router.replace({
        path: view.subpath
      }, () => {
        this.selectedTag = view
        this.editableTabsValue = view.name
        this.$store
          .dispatch('tagsView/delOthersViews', this.selectedTag)
          .then(() => {
            this.moveToCurrentTag()
            this.$store.dispatch('tagsView/delCachedView', view).then(() => {
              const { fullPath } = view
              this.$nextTick(() => {
                this.$router.replace({
                  path: '/redirect' + fullPath
                })
              })
            })
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~element-ui/packages/theme-chalk/src/common/var.scss";
  .table-action-down .table-action-down-span {
    color: $--color-text-regular;
    padding: 5px 24px;
    display: inline-block;
  }
  .table-action-down .table-action-down-span:first-child {
    padding-top: 17px;
  }
  .table-action-down .table-action-down-span:last-child {
    padding-bottom: 17px;
  }
  .table-action-down .table-action-down-span:hover {
    background-color: $--color-primary-light-9;
    color: $--color-primary;
  }
  .tags-view-container {

    height: 26px;
    line-height: 26px;
    margin-top: 6px;
    background: rgba(73, 135, 229, 0);
    max-width: calc(100% - 120px);
    flex: 1;
    .contextmenu {
      margin: 0;
      background: #fff;
      z-index: 3000;
      position: absolute;
      list-style-type: none;
      height: 26px;
      line-height: 26px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 400;
      color: #333;
      box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
      li {
        margin: 0;
        //padding: 7px 16px;
        cursor: pointer;
        &:hover {
          background: #eee;
        }
      }
    }
  }
</style>

<style scoped lang="scss">
@import "../../../styles/variables";

::v-deep .el-tabs__item{

height: 26px !important;
  margin-right: 10px;
line-height: 26px !important;
  border-radius: 4px;
  background: #FFF;

  /* 下层投影 */
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
}
::v-deep .el-tabs__item.is-active{
  background: #ffffff;
  color: $primary-color;
  border-radius: 4px;
}
::v-deep .el-tabs__item.is-active:hover{
  background: #ffffff;
  color: $primary-color;
}
</style>

<style scoped>
  >>>.el-tabs--top.el-tabs--border-card > .el-tabs__header .el-tabs__item:last-child, .el-tabs--top.el-tabs--card > .el-tabs__header .el-tabs__item:last-child, .el-tabs--top .el-tabs--left > .el-tabs__header .el-tabs__item:last-child, .el-tabs--top .el-tabs--right > .el-tabs__header .el-tabs__item:last-child, .el-tabs--bottom.el-tabs--border-card > .el-tabs__header .el-tabs__item:last-child, .el-tabs--bottom.el-tabs--card > .el-tabs__header .el-tabs__item:last-child, .el-tabs--bottom .el-tabs--left > .el-tabs__header .el-tabs__item:last-child, .el-tabs--bottom .el-tabs--right > .el-tabs__header .el-tabs__item:last-child {
    padding-right: 0;
  }
  >>>.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable {
    padding-left: 0;
    padding-right: 0;
  }
  >>>.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable.is-active .tags-view-item{
    padding-right: 20px;
  }
  >>>.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable.is-active .tags-view-item:hover{
    padding-right: 20px;
  }
  >>>.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable .tags-view-item{
    padding-left: 0;
    padding-right: 0;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    display: inline-block;
    height: 100%;
  }
  >>>.el-tabs--card > .el-tabs__header .el-tabs__item.is-closable{
    padding-left: 20px;
    padding-right: 20px;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    height: 100%;
    display: inline-block;
  }
  >>>.el-tabs--card > .el-tabs__header .el-tabs__item:not(.is-active).is-closable:hover{
    padding-left: 20px;
    padding-right: 40px;
    height: 100%;
    display: inline-block;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
  >>>.el-tabs--card > .el-tabs__header .el-tabs__item .el-icon-close {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    line-height: 14px;
  }
  >>>.el-divider--horizontal {
    margin: 0;
    height: 0;
  }
  >>>.el-tabs--card > .el-tabs__header .el-tabs__item {
    border: 1px solid transparent;
  }
  >>>.el-tabs--card > .el-tabs__header .el-tabs__nav {
    border: 0 solid #ffffff;
  }
  >>>.el-tabs__item:hover{
    color: #515a6e;
    background: #dee1e6
  }
</style>

<style>
  .table-action-down-tab{
    padding: 0;
  }
  .right-menu {
    position: absolute;
    right: 20px;
    bottom: 0px;
    display: inline-block;
    color: #606266;
    font-size: 12px;
    transition: all .3s cubic-bezier(.645,.045,.355,1),border 0s,color .1s,font-size 0s;
  }
  .vab-tabs-more {
    position: relative;
  }
  .vab-tabs-more-icon {
    display: inline-block;
    color: #9a9a9a;
    cursor: pointer;
    transition: transform .3s ease-out;
  }
  .vab-tabs-more-icon .box {
    position: relative;
    display: block;
    width: 14px;
    height: 8px;
  }
  .vab-tabs-more-icon .box-t:before {
    transition: transform .3s ease-out .3s;
  }
  .vab-tabs-more-icon .box:before {
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 6px;
    content: "";
    background: #fff;
  }
  .vab-tabs-more-icon .box:after {
    position: absolute;
    top: 0;
    left: 8px;
    width: 6px;
    height: 6px;
    content: "";
    background: #fff;
  }

  .vab-tabs-more-active .vab-tabs-more-icon, .vab-tabs-more:hover .vab-tabs-more-icon {
    transform: rotate(90deg);
  }

  .vab-tabs-more-active .vab-tabs-more-icon .box:after, .vab-tabs-more-active .vab-tabs-more-icon .box:before, .vab-tabs-more:hover .vab-tabs-more-icon .box:after, .vab-tabs-more:hover .vab-tabs-more-icon .box:before {
    background: #1890ff;
  }
  .vab-tabs-more-active .vab-tabs-more-icon .box-t:before, .vab-tabs-more:hover .vab-tabs-more-icon .box-t:before {
    transform: rotate(45deg);
  }
  .vab-tabs-more-active .vab-tabs-more-icon .box:after, .vab-tabs-more-active .vab-tabs-more-icon .box:before, .vab-tabs-more:hover .vab-tabs-more-icon .box:after, .vab-tabs-more:hover .vab-tabs-more-icon .box:before {
    background: #1890ff;
  }
</style>

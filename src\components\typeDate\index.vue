<!-- 日期选择共通 -->
<template>
  <div class="car-date-container">
    <div slot="header" class="clearfix">
      <el-radio-group :value="type" @input="typeChange">
        <el-radio v-for="(radio, key) in dateType" :key="key" :label="key">{{ radio.label }}</el-radio>
      </el-radio-group>
    </div>
    <date-picker
      :value="value"
      v-bind="options"
      :picker-options="{firstDayOfWeek: 1 }"
      @input="valueChange"
    />
  </div>
</template>

<script>

const DATE_TYPE = {
  1: { label: '按日', valueFormat: 'yyyy-MM-dd', type: 'daterange' },
  2: { label: '按周', valueFormat: 'yyyy-MM-dd', type: 'weekrange' },
  3: { label: '按月', valueFormat: 'yyyy-MM', type: 'month' }
}

export default {
  name: 'TypeDate',
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    value: {
      type: [String, Date, Array],
      require: true,
      default: null
    },
    type: {
      type: String,
      // 1日 2周 3月
      default: '1'
    }
  },
  data() {
    // 这里存放数据
    return {
      dateType: DATE_TYPE
    }
  },
  // 监听属性 类似于data概念
  computed: {
    options() {
      const _options = {
        rangeSeparator: '至',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        placeholder: '选择月',
        unlinkPanels: true
      }
      _options.type = DATE_TYPE[this.type].type
      _options.valueFormat = DATE_TYPE[this.type].valueFormat
      return _options
    }
  },
  watch: {
  },
  created() {
  },
  // 方法集合
  methods: {
    valueChange(val) {
      this.$emit('input', val)
    },
    typeChange(val) {
      this.$emit('update:type', val)
    }
  } // 如果页面有keep-alive缓存功能，这个函数会触发
}
</script>
<style lang="scss" scoped>
</style>

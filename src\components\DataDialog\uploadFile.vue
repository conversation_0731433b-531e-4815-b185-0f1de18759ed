<template>
  <simple-data-dialog :visible="visible" :title="$t('common.uploadFile')" show-close @update:visible="(params)=>{$emit('update:visible',params)}">
    <el-upload
      class="upload-demo"
      multiple
      list-type="text"
      :limit="limit"
      :file-list="fileList"
      :delete-file="false"
      action="#"
      :auto-upload="true"
      :http-request="uploadRequest"
      :before-upload="beforeUpload"
      :show-file-list="false"
    >
      <el-button size="small" type="primary">{{ $t('common.uploadBtn') }}</el-button>
      <div slot="tip" class="el-upload__tip">
        {{ innerTip }}
        <a v-if="errorLink != '#'" class="el-button el-button--small" @click="exportErrorLink">{{ $t('common.downloadErrorFile') }}</a>
      </div>
    </el-upload>
    <template
      slot="footer"
      class="dialog_btn"
    >
      <el-button @click="closeFile">{{ $t('common.close') }}</el-button>
    </template>
  </simple-data-dialog>
</template>

<script>
import SimpleDataDialog from '@/components/SimpleDataDialog'
export default {
  name: 'UploadFile',
  components: {
    SimpleDataDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    limit: {
      type: Number,
      default: 5
    },
    tip: {
      type: String,
      default() {
        return this.$t('common.tip')
      }
    },
    uploadAction: {
      type: Function,
      default: null
    },
    errorFileName: {
      type: String,
      default: 'error'
    },
    typeList: {
      type: Array,
      default: () => {
        return ['xls', 'xlsx']
      }
    },
    beforeUploadFile: {
      type: Function,
      default: null
    }
  },
  data() {
    // 这里存放数据
    return {
      fileList: [],
      isError: false,
      errorLink: '#',
      innerTip: ''
    }
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {
    tip(val) {
      this.innerTip = val
      this.errorLink = '#'
    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {
  },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.innerTip = this.tip
  },
  // 方法集合
  methods: {
    exportErrorLink() {
      const that = this
      var x = new XMLHttpRequest()
      x.open('GET', this.errorLink, true)
      x.responseType = 'blob'
      if (!x.params) {
        x.params = {}
      }
      x.params.filename = this.errorFileName
      x.onload = function(e) {
        // 会创建一个 DOMString，其中包含一个表示参数中给出的对象的URL。这个 URL 的生命周期和创建它的窗口中的 document 绑定。这个新的URL 对象表示指定的 File 对象或 Blob 对象。
        var url = window.URL.createObjectURL(x.response)
        var a = document.createElement('a')
        a.href = url
        a.download = that.errorFileName
        a.click()
      }
      x.send()
    },
    beforeUpload(file) {
      const typeArr = file.name.split('.')
      const type = typeArr[typeArr.length - 1]
      const list = this.typeList
      if (!list.includes(type.toLowerCase()) && !list.includes(type)) {
        this.$message.error(this.tip)
        return false
      }
      if (this.beforeUploadFile && typeof this.beforeUploadFile === 'function') {
        return this.beforeUploadFile(file)
      }
    },
    uploadRequest(param) {
      this.errorLink = '#'
      if (!this.uploadAction) return
      this.uploadAction(param, () => {
        this.isError = false
        this.innerTip = this.$t('common.uploadSuccess')
      }, (errorLink) => {
        this.isError = true
        this.innerTip = this.$t('common.uploadFail')
        this.errorLink = errorLink
      })
    },
    closeFile() {
      this.$emit('closeFile')
    }
  } // 如果页面有keep-alive缓存功能，这个函数会触发
}
</script>

<style scoped>

</style>

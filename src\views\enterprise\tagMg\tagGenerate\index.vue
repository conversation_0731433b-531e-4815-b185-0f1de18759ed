<template>
  <div
    class="app-container"
  >
    <el-card>
      <!-- 搜索框 -->
      <data-select
        :search-data.sync="search"
        :button-data="buttonData"
        @return-search="(data) => {searchHelper.search(data)}"
        @return-reset="searchHelper.reset"
      />
      <!--      :table-option="{ option: { enableSelected: true, } }"-->
      <data-table
        ref="dataTable"
        show-summary
        :table-data="searchHelper.dataList"
        :column="column"
        :table-index="3"
        :pagination.sync="searchHelper.pagination"
        @search-event="() => {searchHelper.handleQuery()}"
      />
      <!---->
      <simple-data-dialog
        v-if="fileDialog"
        :title="['新增','修改'][dialogFileType]"
        :visible="true"
        size="small"
      >
        <el-form
          ref="baseForm"
          :model="baseFrom"
          size="mini"
          label-width="90px"
          :rules="baseFromRules"
          @submit.native.prevent
        >
          <el-row>
            <el-col v-if="dialogFileType === 0">
              <el-form-item
                prop="codeCount"
                label="生码数量："
              >
                <el-input
                  v-model="baseFrom.codeCount"
                  clearable
                  style="width: 100%"
                  placeholder="请输入生码数量"
                />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item
                prop="productId"
                label="绑定产品："
              >
                <el-select
                  v-model="baseFrom.productId"
                  filterable
                  clearable
                  style="width: 100%"
                  placeholder="请选择绑定产品"
                  @change="changeProduct(baseFrom.productId)"
                >
                  <el-option
                    v-for="item in productNameArr"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <div v-if="baseFrom.productId?((productNameArr.find(e=>e.id===baseFrom.productId)).category===99999):false ">
              <el-col>
                <el-form-item
                  prop="userCode"
                  label="瓜农："
                  class="el-form-item-width"
                >
                  <el-select
                    v-model="baseFrom.userCode"
                    filterable
                    clearable
                    style="width: 100%"
                    placeholder="请选择瓜农"
                  >
                    <el-option
                      v-for="item in farmerArr"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </div>
            <div v-else>
              <el-col>
                <el-form-item
                  prop="templateId"
                  label="绑定模板："
                  class="el-form-item-width"
                >
                  <el-select
                    v-model="baseFrom.templateId"
                    filterable
                    clearable
                    style="width: 100%"
                    placeholder="请选择绑定模板"
                  >
                    <el-option
                      v-for="item in productTemplateArr"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="dialogFileType === 0">
                <el-form-item
                  prop="ruleId"
                  label="生码规则："
                  class="el-form-item-width"
                >
                  <el-select
                    v-model="baseFrom.ruleId"
                    filterable
                    clearable
                    style="width: 100%"
                    placeholder="请选择生码规则"
                    @change="getInfo(baseFrom.ruleId)"
                  >
                    <el-option
                      v-for="item in ruleArr"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-for="(item,i) in tagRuleTableData" :key="i">
                <el-form-item
                  :label="item.keyCn"
                >
                  <el-input
                    :value="item.itemType===30?dayjs(new Date()).format(`${item.defaultValue.toUpperCase()}`):item.defaultValue"
                    readonly
                    clearable
                    style="width: 100%"
                    placeholder=""
                  />
                </el-form-item>
              </el-col>
            </div>
            <el-col>
              <el-form-item
                prop="batchId"
                label="绑定批次："
                class="el-form-item-width"
              >
                <el-select
                  v-model="baseFrom.batchId"
                  filterable
                  clearable
                  style="width: 100%"
                  placeholder="请选择绑定批次"
                >
                  <el-option
                    v-for="item in productBatchArr"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item
            prop="remark"
            label="备注："
          >
            <el-input
              v-model="baseFrom.remark"
              style="width: 100%"
              clearable
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-form>

        <el-footer class="button-container">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="saveClick()">保存</el-button>
        </el-footer>
      </simple-data-dialog>
      <simple-data-dialog
        v-if="downDialog"
        title="下载二维码"
        :visible="true"
        size="small"
      >

        <el-button @click="downLoad(1,'')">工业标识</el-button>
        <el-button @click="downLoad(2,'-G')">工业标识-G</el-button>
        <el-button @click="downLoad(3,'-S')">工业标识-S</el-button>
        <div style="margin-top: 20px" />
        <el-button @click="downLoad(4,'')">工业标识链接</el-button>
        <el-button @click="downLoad(5,'-G')">工业标识链接-G</el-button>
        <el-button @click="downLoad(6,'-S')">工业标识链接-S</el-button>
        <div v-if="clickIndex" style="width: 100%;max-height: 300px;overflow: auto;display: flex;flex-wrap: wrap;margin-top: 20px;align-items: center">
          <div v-for="(e,i) in codeFrom" :key="`${i}${clickIndex}${Date.now()}`">
            <vue-qr :ref="`qrcode${i}`" :key="`${i}${clickIndex}${Date.now()}`" :text="[1,2,3].includes(clickIndex)?(e.idisCode||''):(e.qrCodeLink||'')" :logo-margin="1" :logo-corner-radius="0" :size="160" :logo-src="logo" />
          </div>
        </div>

        <el-footer class="button-container">
          <el-button v-if="clickIndex" type="primary" @click="downLoadPic">下载二维码</el-button>
          <el-button @click="downDialog=false;clickIndex = 0">取消</el-button>
        </el-footer>
      </simple-data-dialog>
    </el-card>
  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import acc from '@/api/acc/acc'
import SimpleDataDialog from '@/components/SimpleDataDialog'
import accApi from '@/api/acc/acc'
import dayjs from 'dayjs'
import VueQr from 'vue-qr'
import html2canvas from 'html2canvas'
import JSZip from 'jszip'
import { saveAs } from 'file-saver'
export default {
  name: 'TagGenerate',
  components: {
    DataSelect, DataTable,
    SimpleDataDialog, VueQr
  },
  data() {
    return {
      config: {
        logo: null, // 默认二维码中间图片
        text: '' // 二维码内容,编码格式默认使用base64
      },
      logo: null, // 默认二维码中间图片
      clickIndex: 0, // 默认二维码中间图片1
      clickType: '',
      gPng: require('@/assets/img/g.png'),
      sPng: require('@/assets/img/s.png'),
      searchHelper: new this.$searchHelper({ api: acc.tagGenerateDateListApi }),
      dataList: [],
      fileList: [],
      tagRuleTableData: [],
      tagRuleTableDataCodePreview: undefined,
      productNameArr: [],
      productTemplateArr: [],
      productBatchArr: [],
      farmerArr: [],
      ruleArr: [],
      fileDialog: false,
      dialogFileType: 0,
      id: '',
      baseFrom: {},
      baseFromRules: {
        codeCount: [
          {
            required: true,
            message: `请输入生码数量`,
            trigger: `change`
          },
          { pattern: /^([1-9][0-9]*)$/, message: '只能输入大于0的数字' }
        ], productId: [
          {
            required: true,
            message: `请选择绑定产品`,
            trigger: `change`
          }
        ], userCode: [
          // {
          //   required: true,
          //   message: `请选择瓜农`,
          //   trigger: `change`
          // }
        ], templateId: [
          {
            required: true,
            message: `请选择绑定模板`,
            trigger: `change`
          }
        ], ruleId: [
          {
            required: true,
            message: `请选择生码规则`,
            trigger: `change`
          }
        ], batchId: [
          {
            required: true,
            message: `请选择绑定批次`,
            trigger: `change`
          }
        ], remark: [
          {
            message: `备注不能超过500`,
            max: 500
          }
        ]
      },
      search: {
        searchText: {
          label: '关联产品/产品批次',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入关联产品/产品批次'
          }
        },
        timeList: {
          label: '创建时间',
          value: null,
          type: 'date',
          option: {
            type: 'daterange',
            startPlaceholder: this.$t('common.startDate'),
            endPlaceholder: this.$t('common.endDate'),
            unlinkPanels: true,
            placeholder: '请选择创建时间',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          }
        }
      },
      downDialog: false,
      codeFrom: {},
      buttonData: [
        // {
        //   label: '导出标识码',
        //   action: this.exportWaterMelon,
        //   permission: 'templateConf:add'
        // },
        {
          label: '+添加',
          action: this.onAddClick,
          permission: 'all'
        }
      ],
      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            width: '60px',
            sortable: false
          },
          {
            label: '流水号(码量)',
            prop: 'serialNumber',
            sortable: false
          }, {
            label: '生码数',
            prop: 'codeCount',
            sortable: false
          }, {
            label: '同步数',
            prop: 'codeSuccess',
            sortable: false
          }, {
            label: '关联产品',
            prop: 'productName',
            sortable: false
          }, {
            label: '关联批次',
            prop: 'batchNo',
            sortable: false
          }, {
            label: '创建人',
            prop: 'userName'
          }, {
            label: '创建时间',
            prop: 'createTime'
          }
        ],
        // 操作
        operation: {
          label: '操作',
          width: '200px',
          data: (row) => {
            return [
              {
                label: '赋值',
                action: this.onEditClick,
                permission: 'all'
              }, {
                label: '下载码',
                action: this.onDownClick,
                permission: 'all'
              }, {
                label: '下载二维码',
                action: this.onDownClickSC,
                permission: 'all'
              }

            ]
          }
        }
      }
    }
  },
  watch: {
    'baseFrom.ruleId': {
      handler(val) {
        if (!val) {
          this.tagRuleTableData = []
          this.tagRuleTableDataCodePreview = undefined
        }
      }
    }
  },
  mounted() {
    this.searchHelper.handleQuery()
  },
  created() {
    // 产品
    accApi.productList().then(res => {
      this.productNameArr = res.data.map(item => {
        return {
          label: item.productName,
          value: item.id,
          ...item
        }
      })
    })
  },
  methods: {
    exportWaterMelon() {
      const selection = this.$refs?.dataTable.$refs.multipleTable.selection || []
      var arr = selection.map(item => item.batchNo)
      if (selection.length > 0) {
        accApi.exportExcelApi({ batchNos: arr })
      } else {
        this.$message.warning('请选择要导出的标识码')
      }
    },
    dayjs,
    // 获取标识规则的列表
    getInfo(val) {
      if (val) {
        accApi.detailTagRuleApi({ id: val }).then((res) => {
          this.tagRuleTableData = res.data.ruleItemList
          this.tagRuleTableDataCodePreview = res.data.tagRuleTableDataCodePreview
        })
      } else {
        this.tagRuleTableData = []
        this.tagRuleTableDataCodePreview = undefined
      }
    },
    changeProduct(val) {
      this.$set(this.baseFrom, 'templateId', undefined)
      this.$set(this.baseFrom, 'ruleId', undefined)
      this.$set(this.baseFrom, 'batchId', undefined)
      this.$set(this.baseFrom, 'userCode', undefined)
      this.$refs.baseForm.clearValidate()
      if (val) {
        // 批次
        accApi.productBatchSelectListApi({ productId: val }).then(res => {
          this.productBatchArr = res.data.map(item => {
            return {
              label: item.batchTitle,
              value: item.id,
              batchNo: item.batchNo
            }
          })
        })
        // 产品模板下拉框
        accApi.productTemplateSelectListApi({ productId: val }).then(res => {
          this.productTemplateArr = res.data.map(item => {
            return {
              label: item.name,
              value: item.id
            }
          })
        })
        // 规则下拉框
        accApi.ruleListApi({ productId: val }).then(res => {
          this.ruleArr = res.data.map(item => {
            return {
              label: item.ruleName,
              value: item.id
            }
          })
        }) // 瓜农
        // accApi.farmerProductRelationList({ productNo: (this.productNameArr.find(e => e.id === val)).productNo }).then(res => {
        //   this.farmerArr = res.data.map(item => {
        //     return {
        //       label: item.user,
        //       value: item.userCode
        //     }
        //   })
        // })
      } else {
        this.productBatchArr = []
        this.productTemplateArr = []
        this.farmerArr = []
        this.ruleArr = []
      }
    },
    onAddClick() {
      this.dialogFileType = 0
      this.fileDialog = true
    },

    cancel() {
      this.tagRuleTableData = []
      this.baseFrom = {}
      this.fileDialog = false
    },
    saveClick() {
      this.$refs.baseForm.validate(() => {
        const list = this.productNameArr.filter(e => e.value === this.baseFrom.productId)
        const batchNo = this.productBatchArr.find(e => e.value === this.baseFrom.batchId).batchNo
        this.$set(list[0], 'updateTime', undefined)
        this.$set(list[0], 'createTime', undefined)
        if (this.dialogFileType === 0) {
          accApi.addTagGenerate({ ...this.baseFrom, productList: list, batchNo, codePreview: this.tagRuleTableDataCodePreview }).then(() => {
            this.cancel()
            this.searchHelper.handleQuery()
          })
        } else {
          accApi.editTagGenerate({ ...this.baseFrom, productList: list, batchNo, codePreview: this.tagRuleTableDataCodePreview }).then(() => {
            this.cancel()
            this.searchHelper.handleQuery()
          })
        }
      })
    },
    // 编辑
    onEditClick(row) {
      this.dialogFileType = 1
      this.fileDialog = true
      this.$set(this.baseFrom, 'id', row.id)
      this.$set(this.baseFrom, 'codeCount', row.codeCount)
      this.$set(this.baseFrom, 'indexStart', row.indexStart)
      this.$set(this.baseFrom, 'indexEnd', row.indexEnd)
    },
    onDownClick(row) {
      accApi.exportTagGenerateApi({ id: row.id })
    },
    onDownClickSC(row) {
      this.downDialog = true
      this.clickIndex = 0
      accApi.downloadQRCode({ id: row.id }).then((res) => {
        this.codeFrom = res.data
      })
    },
    downLoadPic() {
      const zip = new JSZip()
      const imgFolder = zip.folder('images')
      let flag = 0
      this.$nextTick(() => {
        this.codeFrom.forEach((e, i) => {
          html2canvas(this.$refs[`qrcode${i}`][0].$el).then((canvas) => {
            let base64 = canvas.toDataURL('image/png')
            base64 = base64.split('base64,')[1]
            imgFolder.file(`${e.idisCode}` + '.png', base64, { base64: true }) // 文件名
            if (flag === this.codeFrom.length - 1) {
              zip.generateAsync({ type: 'blob' }).then((blob) => {
                saveAs(blob, `二维码图片${this.clickType}.zip`) // 压缩包名字
              })
            }
            flag = flag + 1
          })
        })
      })
    },
    downLoad(val, type) {
      this.clickIndex = val
      this.clickType = type
      if (val === 1 || val === 4) {
        this.logo = null
      } else if (val === 2 || val === 5) {
        this.logo = this.gPng
      } else if (val === 3 || val === 6) {
        this.logo = this.sPng
      }
    }
  }

}
</script>

<style scoped lang="scss">
.el-form-item {
  width: 100%;
}

::v-deep .dialog .el-form-item .el-form-item__label {
  text-align: right !important;
}

.button-container {
  line-height: 60px;
  text-align: center;
}
</style>

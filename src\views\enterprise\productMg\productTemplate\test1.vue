<template>
  <div class="homepage" style="font-size: 13.33vw;">
    <img src="@/assets/img/test1.jpg" style="width: 100%">
  </div></template>
<script>
import dayjs from 'dayjs'
import acc from '@/api/acc/acc'

export default {
  name: 'Test1',
  data() {
    return {
      picList: ['greenSign.png', 'redSign.png']
    }
  },
  mounted() {
  },
  methods: {
    getProductInfo(productId) {
      acc.queryTemplateProduct({ productId, idisCode: this.idisCode, paramCheck: true }).then((res) => {
        this.productInfo = res.data
        const day = this.idisCode ? dayjs(this.productInfo.beyondShelfLifeDate).diff(dayjs(this.productInfo.productionDate), 'days') : 0
        this.productData = [
          { attrNameCn: '产品名称', attrValue: this.productInfo.productName },
          { attrNameCn: '生产日期', attrValue: this.idisCode ? this.productInfo.productionDate : 'xxxx年xx月xx日' },
          // { attrNameCn: '保质期', attrValue: this.idisCode ? day > 180 ? `${Math.floor(day.divide(30))}个月` : `${day}天` : 'xxxx年xx月xx日' },
          { attrNameCn: '有效期至', attrValue: this.idisCode ? this.productInfo.beyondShelfLifeDate : 'xxxx年xx月xx日' },
          { attrNameCn: '产品批号', attrValue: this.productInfo.batchNo },
          ...res.data.productAttrList]
      })
    }
  }
}
</script>

<style>html {
  font-size: 13.33vw;
}</style>
<style  scoped lang="scss">

</style>

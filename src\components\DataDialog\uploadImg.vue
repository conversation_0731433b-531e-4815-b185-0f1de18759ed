<template>
  <div>
    <div
      class="img-container"
      :style="{
        height: height || 'auto',
        'padding-bottom': tipPosition === 'bottom' ? '25px' : '0',
      }"
    >
      <div v-for="(item, index) in imgList" :key="index" class="img-list">
        <el-image
          fit="cover"
          :src="item.url"
          :preview-src-list="srcList"
          :z-index="viewerZIndex"
        />
        <a v-if="!readonly"
          ><i class="el-icon-close delete-img" @click="clearPic(index)"
        /></a>
      </div>
      <div :class="{ hide: hideUpload, 'upload-list': true }">
        <el-upload
          ref="upload"
          action="#"
          :multiple="false"
          :auto-upload="true"
          :show-file-list="false"
          :disabled="readonly"
          :http-request="choosePic"
          :before-upload="beforeUpload"
          class="uploader"
        >
          <i :class="[uploadIcon, 'uploader-icon']" />
        </el-upload>
      </div>
      <div v-if="tipPosition" class="el-upload__right">
        <slot name="tip"> </slot>
      </div>
      <div v-else :class="{ hide: hideUpload }" class="el-upload__tip">
        <slot name="tip">
          {{ $t("common.uploadText") }}
        </slot>
      </div>
    </div>
    <!-- 选择图片   -->
    <simple-data-dialog
      :title="dialogCropper.title"
      :visible.sync="dialogCropper.visible"
    >
      <div class="img-cropper">
        <vue-cropper
          ref="cropper"
          v-bind="cropperOption"
          :src="dialogCropper.imgSrc"
        />
      </div>
      <template v-slot:footer>
        <div class="dialog_btn">
          <el-button @click="dialogCropper.visible = false">{{
            $t("common.cancel")
          }}</el-button>
          <el-button type="primary" @click="onCropClick">{{
            $t("common.ok")
          }}</el-button>
        </div>
      </template>
    </simple-data-dialog>
  </div>
</template>

<script>
import SimpleDataDialog from "@/components/SimpleDataDialog";
import VueCropper from "vue-cropperjs";
import "cropperjs/dist/cropper.css";
import commonApi from "@/api/common/common";
import { mapState } from "vuex";
import { PopupManager } from "element-ui/lib/utils/popup";
export default {
  name: "UploadImg",
  components: {
    VueCropper,
    SimpleDataDialog,
  },
  props: {
    imgList: {
      type: Array,
      default: null,
    },
    height: {
      type: String,
      default: null,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    limitCount: {
      type: Number,
      default: 999,
    },
    uploadIcon: {
      type: String,
      default: "el-icon-plus",
    },
    useCropper: {
      type: Boolean,
      default: false,
    },
    cropperOption: {
      type: Object,
      default: () => {},
    },
    beforeClear: {
      type: Function,
      default: null,
    },
    zIndex: {
      type: Number,
      default: 9999,
    },
    tipPosition: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      hideUpload: false,
      srcList: [],
      dialogCropper: {
        // 弹出框组件
        title: "",
        visible: false,
        imgSrc: "",
        httpOption: {},
      },
    };
  },
  computed: {
    ...mapState({
      merchantCode: (state) => state.user.merchantCode,
    }),
    viewerZIndex() {
      const nextZIndex = PopupManager.nextZIndex();
      return this.zIndex > nextZIndex ? this.zIndex : nextZIndex;
    },
  },
  watch: {
    imgList: {
      handler(newValue) {
        this.srcList = [];
        newValue.forEach((item) => {
          this.srcList.push(item.url);
        });
        if (newValue.length >= this.limitCount) {
          this.hideUpload = true;
        } else if (newValue.length < this.limitCount && !this.readonly) {
          this.hideUpload = false;
        }
      },
      immediate: true,
      deep: true,
    },
    readonly: {
      handler(newVal) {
        this.setReadOnly(newVal);
      },
      deep: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.setReadOnly(this.readonly);
    });
  },
  methods: {
    setReadOnly(newVal) {
      if (newVal) {
        this.hideUpload = newVal;
      }
    },
    setImage(file) {
      if (file.type.indexOf("image/") === -1) {
        alert("Please select an image file");
        return;
      }
      if (typeof FileReader === "function") {
        const reader = new FileReader();
        reader.onload = (event) => {
          this.dialogCropper.imgSrc = event.target.result;
          // rebuild cropperjs with the updated source
          this.$refs.cropper.replace(event.target.result);
        };
        reader.readAsDataURL(file);
      } else {
        alert("Sorry, FileReader API not supported");
      }
    },
    choosePic(param) {
      if (this.useCropper) {
        this.dialogCropper.visible = true;
        this.dialogCropper.httpOption = param;
        this.setImage(param.file);
      } else {
        this.uploadImg(param);
      }
    },
    dataURLtoFile(dataUrl, fileName) {
      const arr = dataUrl.split(",");
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], fileName, { type: mime });
    },
    onCropClick() {
      const cropImg = this.$refs.cropper.getCroppedCanvas().toDataURL();
      const param = this.dialogCropper.httpOption;
      const oldFile = param.file;
      param.file = this.dataURLtoFile(cropImg, oldFile.name);
      this.uploadImg(param);
    },
    uploadImg(param) {
      const formData = new FormData();
      formData.append("file", param.file);
      const isLt5M = param.file.size / 1024 / 1024;
      if (isLt5M > 4) {
        this.$message({
          message: this.$t("common.imgLimit"),
          type: "error",
        });
      }
      commonApi
        .uploadImg(formData)
        .then((res) => {
          // eslint-disable-next-line eqeqeq
          if (res.code === "SUCCESS") {
            this.imgList.push({ fileId: res.data.fileId, url: res.data.url });
            this.$emit("update:imgList", this.imgList);
            this.$emit("upload-success", this.imgList);
            if (this.useCropper) {
              this.dialogCropper.visible = false;
            }
          }
        })
        .catch(() => {});
    },
    clearPic(index) {
      if (this.beforeClear && this.beforeClear(this.imgList)) {
        return;
      }
      this.imgList.splice(index, 1);
      this.$emit("update:imgList", this.imgList);
      this.$emit("after-clear-img");
    },
    beforeUpload(file) {
      const typeArr = file.name.split(".");
      const type = typeArr[typeArr.length - 1];
      const list = ["jpg", "JPG", "png", "PNG", "gif", "GIF"];
      if (!list.includes(type)) {
        this.$message.error(this.$t("common.imgFileType"));
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 <= 4;
      if (!isLt2M) {
        this.$message.error(this.$t("common.imgLimit"));
      }
      return isLt2M;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../styles/element-variables";
.el-upload__tip {
  position: absolute;
  overflow: hidden;
  bottom: -26px;
  white-space: nowrap;
}
.el-upload__right {
  position: absolute;
  overflow: hidden;
  left: 100px;
  font-size: 12px;
  white-space: nowrap;
}
.uploader {
  width: 90px;
  height: 90px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
  display: block;
  &:hover {
    border-color: $--color-primary;
  }
  &.uploader-disabled {
    background: #f5f7fa;
    cursor: not-allowed;
    &:hover {
      border-color: #d9d9d9;
    }
    ::v-deep .el-upload {
      cursor: not-allowed;
    }
    .uploader-delete {
      display: none;
    }
  }
}
.uploader-icon {
  display: block;
  width: 90px;
  height: 90px;
  font-size: 22px;
  line-height: 90px;
  color: #828282;
  text-align: center;
}
.img-container {
  position: relative;
  float: left;
  height: 350px;
  padding-bottom: 25px;
  margin-right: 15px;
}
.img-list {
  position: relative;
  border-radius: 6px;
  border: 1px solid #b4bccc;
  width: 90px;
  height: 90px;
  margin: 5px 10px 5px 0;
  float: left;
  padding: 0 2px 2px 0;
  .el-image {
    border-radius: 6px;
    width: 85px;
    height: 85px;
    margin: 2px 0 0 2px;
  }
}
.upload-list {
  width: 90px;
  height: 90px;
  margin: 5px 10px 5px 0;
  float: left;
  padding: 0 2px 2px 0;
}
.hide {
  display: none;
}
.delete-img {
  display: block;
  position: absolute;
  top: 3px;
  right: 3px;
}
</style>
<style>
.hide .el-upload--picture-card {
  display: none !important;
}
.uploader-icon .el-image-viewer__close {
  color: #ffffff !important;
}
</style>

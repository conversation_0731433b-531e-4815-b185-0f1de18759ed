<template>
  <div>
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <div class="div-menu-level-1" @mouseenter="menuMouseover" @mouseleave="menuMouseleave">
        <el-menu
          class="menu-level-1"
          :default-active="activeMenu.activeLeftPath"
          background-color="rgba(0, 0, 0, 0)"
          :text-color="variables.leftMenuText"
          active-text-color="#ffffff"
          @select="onMenuSelect"
        >
          <el-menu-item @click="back2home">
            <item
              icon="el-icon-s-home"
              :title="$t('common.homeMainPage')"
            />
          </el-menu-item>
          <template v-for="route in permissionChildrenRoutes">
            <el-menu-item
              v-if="!route.hidden"
              :key="route.path"
              :index="route.path"
            >
              <item
                :icon="route.meta && route.meta.icon || 'el-icon-menu'"
                :title="currentLang === 'zh' ? route.meta.title :route.meta.titleEn"
              />
            </el-menu-item>
          </template>
        </el-menu>
      </div>

      <transition name="slide">
        <el-menu
          v-if="isCollapse"
          ref="menulevel2"
          class="menu-level-2"
          :default-active="activeMenu.activePath"
          :background-color="variables.menuBg"
          :text-color="variables.menuText"
          :unique-opened="true"
          :active-text-color="variables.menuActiveText"
          :collapse-transition="false"
          mode="vertical"
        >
          <span v-if="selectLeftMenuTitle !== ''" class="menu-title">{{ selectLeftMenuTitle }}</span>
          <sidebar-item
            v-for="route in rightMenus"
            :key="route.path"
            :item="route"
            :base-path="basePath"
          />
        </el-menu>
      </transition>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { constantRoutes } from '@/router'
import emitter from 'element-ui/src/mixins/emitter'
// import Logo from './Logo'
import Item from './Item'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'
import Cookies from 'js-cookie'
import { isExternal } from '@/utils/validate'
import path from 'path'

export default {
  components: {
    SidebarItem,
    Item
  },
  mixins: [emitter],
  data() {
    return {
      isCollapse: true,
      rightMenus: null,
      selectLeftMenuTitle: '',
      basePath: '',
      activeLeftMenu: '',
      level0MenuCode: this.$route?.query.currentMenuPath,
      permissionChildrenRoutes: []
    }
  },
  computed: {
    ...mapGetters(['permission_routes', 'sidebar', 'permission_routes_list', 'currentMenuPath']),
    activeMenu() {
      const route = this.$route
      const { meta, path, params } = route
      let preViewPath = null
      const isConstantRoutes = constantRoutes.some(value => value.name === route.name)
      if (!isConstantRoutes) {
        const allParams = this.permission_routes_list.filter(value => value.name === route.name)
        if (allParams?.length > 0) {
          if (allParams[0]?.hidden) params.hidden = allParams[0].hidden
          if (allParams[0].preViewName) {
            const item = this.permission_routes_list.filter(value => value.name === allParams[0].preViewName)
            if (item?.length > 0) preViewPath = item[0].fullMenuPath
          }
        }
      }
      // if set path, the sidebar will highlight the path you set
      const menuPath = { activePath: '', activeLeftPath: '/' }
      if (meta.activeMenu) {
        menuPath.activePath = meta.activeMenu
      } else {
        if (params && (params.hidden === 'true' || params.hidden)) {
          menuPath.activePath = preViewPath || params.preViewPath
        } else {
          menuPath.activePath = path
        }
      }
      if (!isExternal(menuPath.activePath)) {
        let tmpPath = menuPath.activePath
        if (meta.basePath) {
          tmpPath = tmpPath.substring(meta.basePath.length + 1)
        }
        menuPath.activeLeftPath = tmpPath.substring(0, tmpPath.indexOf('/', 1))
        if (menuPath.activeLeftPath === '') {
          for (const proute of this.permissionChildrenRoutes) {
            if (!proute.hidden && proute.level === '1') {
              menuPath.activeLeftPath = proute.path
              break
            }
          }
        }
        this.onMenuSelect(menuPath.activeLeftPath)
      }
      return menuPath
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    currentLang() {
      return Cookies.get('language') == null ? 'zh' : Cookies.get('language')
    },
    sidebar() {
      return this.$store.state.app.sidebar
    }
  },
  mounted() {
    const { meta } = this.$route
    const basePath = this.level0MenuCode || this.currentMenuPath || meta.basePath
    for (const route of this.permission_routes) {
      if (basePath === route.path) {
        this.permissionChildrenRoutes = route['children']
      }
    }
  },
  methods: {
    menuMouseover() {
      this.isCollapse = false
      if (!this.sidebar.opened) {
        this.$store.dispatch('app/toggleSideBar')
      }
    },
    menuMouseleave() {
      this.isCollapse = true
    },
    onMenuSelect(index) {
      if (index === '/dashboard' && this.permissionChildrenRoutes?.length > 0) {
        const route = this.permissionChildrenRoutes[0]
        this.rightMenus = route['children']
        this.isCollapse = true
        this.selectLeftMenuTitle = this.currentLang === 'zh' ? route.meta.title : route.meta.titleEn
        this.basePath = this.currentMenuPath + '/' + route.path
        return false
      }
      for (const route of this.permissionChildrenRoutes) {
        if (index === route['path']) {
          this.rightMenus = route['children']
          this.isCollapse = true
          this.selectLeftMenuTitle = this.currentLang === 'zh' ? route.meta.title : route.meta.titleEn
          this.basePath = route.meta.basePath + '/' + index
          return false
        }
      }
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    },
    back2home() {
      // this.dispatch('Layout', 'Layout.back2home')
      this.$router.push({ path: '/dashboard/dashboard' })
    }
  }
}
</script>
<style lang="scss" scoped>
@import 'src/styles/variables.scss';

.div-menu-level-1 {
  height: 100%;
  width: 100%;
  position: absolute;
  .menu-level-1 {
    height: 100%;
  }

  .el-menu{
    background: url(../../../assets/404_images/leftbg.png) 0 0 no-repeat;
    background-size: cover;
  }

  .el-menu-item {
    padding-left: 0 !important;
    i {
      margin: auto 16px;
      color: #FFFFFF;
    }
  }

  .el-menu-item.is-active {
    background-color: $subMenuHover !important;
  }
}

.menu-level-2 {
  width: calc(#{$sideBarWidth} - #{$leftMenuWidth}) !important;
  height: calc(100vh - 40px) !important;
  position: relative;
  top: 0;
  left: $leftMenuWidth;
}

.menu-title {
  font-size: 19px;
  padding-left: 20px;
  line-height: 65px;
}

.el-menu-item:hover {
  background-color: $subMenuHover !important;
}

.slide-enter-active, .slide-leave-active {
  transition: all .3s;
}

.slide-enter, .slide-leave-to {
  transform: translateX(200px);
}
</style>

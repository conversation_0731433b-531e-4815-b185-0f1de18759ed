<template>
  <div class="homepageXg" style="font-size: 13.33vw;">
    <section>
      <div class="imageBox">
        <img src="@/assets/img/orange_bg11.jpg" alt="">
        <header>
          东台市中国地理标志农产品追溯平台
        </header>
      </div>
      <div class="searchBox">
        <el-input v-model="antiCounterfeitingCode" clearable :maxlength="50" placeholder="请输入防伪码" />
        <el-button type="success" round @click="checkIfIn()">一键查询</el-button>
        <div class="tips">
          <p v-if="count===0" id="p1">您好，您所查询的产品为第一次防伪查询，防伪码为真，请放心购买。</p>
          <div v-if="count>0" id="p1">您好，该防伪码已第<span>{{ count+1 }}</span>次查询，<div style="white-space: nowrap">首次查询时间为"<span>{{ data }}</span>"。</div>请谨慎购买，谨防假冒。</div>
          <input id="front_z" type="file" accept="image/*" capture="camera" style="display: none" name="file">
          <el-button v-if="flagCode" style="width: 80%;background-color: rgba(255,255,255,0);border: 2px solid white" type="success" round @click="turnDetail()">查看详情</el-button>
        </div>
      </div>

    </section></div>

</template>
<script>
import accApi from '@/api/acc/acc'
export default {
  name: 'AntiCounterfeiting',
  data() {
    return {
      antiCounterfeitingCode: '',
      count: null,
      data: null,
      flagCode: null

    }
  },
  mounted() {
    document.title = ' '
  },
  methods: {
    checkIfIn() {
      accApi.checkCode({ securityCode: this.antiCounterfeitingCode }).then((res) => {
        if (res.data.flagCode) {
          this.count = res.data.count
          this.data = res.data.time
          this.flagCode = res.data.flagCode
          this.$nextTick(() => {
            const dom = document.querySelector('.homepageXg')
            dom.scrollTo({ top: dom.scrollHeight, behavior: 'smooth' })
          })
        } else {
          this.$message.error('商品不存在，请检查防伪码')
        }
      })
    }, turnDetail() {
      this.$router.push({ path: '/test', query: { flagCode: this.flagCode }})
    }
  }
}
</script>

<style>html {
  font-size: 13.33vw;
}</style>
<style scoped lang="scss">
.homepageXg{
  height: 100vh;
  background-color: #9ECDA3;
  overflow:auto;
  ::v-deep .el-input {
    position: relative;
    font-size: 12px;
    display: inline-block;
    width: 80%;
  }
  ::v-deep .el-input--small .el-input__inner {
    height: 0.98rem;
    font-size: 0.31rem;
    text-align: center;
    border-radius: 0.45rem
  }
  ::v-deep .el-button--small.is-round {
    width: 80%;
    height: 0.98rem;
    font-size: 0.31rem;
    margin-top: 0.4rem;
    padding: 0px !important;
    background: #3CC656;
    font-weight: bolder;
    border-radius: 0.45rem

  }
  section header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 0.9rem;
    font-size: 0.32rem;
    color: #fff;
    line-height: 0.9rem;
    text-align: center;
  }
  section .imageBox {
    width: 100%;
    height: 8rem;
    position: relative;
    img {
      width: 100%;
      height: 8rem;
    }
  }

  section .searchBox {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 0.7rem;
    //background: url(../../../../assets/img/download.jpg) 0 0 no-repeat;
    //background-size: 100% 100%;
    margin-top: -1px;
  }
  .tips {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0.4rem 0;
    width: 4.4rem;
    text-align: center;
    p,div {
      font-size: 0.24rem;
      color: #fff;
      line-height: 0.4rem;
      span {
        color: #ffff00;
      }
    }
  }
}

</style>

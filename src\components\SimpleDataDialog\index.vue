<template>
  <el-dialog
    ref="dialog"
    :title="title"
    :visible.sync="dialogVisible"
    :modal="false"
    :show-close="showClose"
    top="0"
    :custom-class="sizeClass"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleBeforeClose"
  >
    <template
      slot="title"
    >
      <slot name="title" />
    </template>
    <slot />
    <template
      slot="footer"
    >
      <slot name="footer" />
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'SimpleDataDialog',
  provide() {
    return {
      simpleDataDialog: this.simpleDataDialogProvide
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: null
    },
    showClose: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'small',
      validator: value => ['small', 'middle', 'big', 'large'].indexOf(value) > -1
    },
    beforeClose: {
      type: Function,
      default: null
    },
    // 是否开启页面关闭检测
    checkDirty: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      simpleDataDialogProvide: { visible: false },
      dialogVisible: true,
      width: 400
    }
  },
  computed: {
    sizeClass() {
      return this.getSize(this.size)
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
      },
      deep: true
    },
    dialogVisible: {
      handler(val) {
        this.simpleDataDialogProvide.visible = val
        this.$emit('update:visible', val)
      },
      deep: true
    }
  },
  created() {
    this.dialogVisible = this.visible
  },
  methods: {
    getSize(name) {
      return `dialog dialog-${name}`
    },
    handleBeforeClose(done) {
      if (this.beforeClose && typeof this.beforeClose === 'function' && this.checkDirty) {
        const result = this.beforeClose();
        if (result instanceof Promise) {
          result.then(done).catch(() => {});
        } else if (result !== false) {
          done();
        }
      } else {
        done();
      }
    }
  }
}
</script>

<style>
@media screen and (max-width: 1280px) {
  .dialog-large{
    width: 85%;
  }
  .dialog-big{
    width: 70%;
  }
  .dialog-middle{
    width: 60%;
  }
  .dialog-small{
    width: 40%;
  }
}
@media screen and (min-width: 1681px) {
  .dialog-large{
    width: 85%;
  }
  .dialog-big{
    width: 60%;
  }
  .dialog-middle{
    width: 50%;
  }
  .dialog-small{
    width: 30%;
  }
}
@media screen and (max-width: 1680px) and (min-width: 1281px){
  .dialog-large{
    width: 85%;
  }
  .dialog-big{
    width: 65%;
  }
  .dialog-middle{
    width: 55%;
  }
  .dialog-small{
    width: 35%;
  }
}
</style>

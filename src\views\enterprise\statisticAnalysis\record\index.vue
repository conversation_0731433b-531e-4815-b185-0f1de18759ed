<template>
  <div
    class="app-container record"
  >
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="列表" name="1">
        <!-- 搜索框 -->
        <data-select
          :search-data.sync="search"
          @return-search="(data) => {searchHelper.search(data)}"
          @return-reset="searchHelper.reset"
        />
        <data-table
          ref="dataTable"
          show-summary
          auto-height
          :table-data="searchHelper.dataList"
          :column="column"
          :table-index="3"
          :pagination.sync="searchHelper.pagination"
          @search-event="() => {searchHelper.handleQuery()}"
        />
      </el-tab-pane>
      <el-tab-pane label="微信扫码地图" name="2">
        <div id="gisMap" :style="`height: ${mapHeight}; width: 100%`" />
        <div ref="maps" style="width: 300px">
          <tbody>
            <tr>
              <th width="80px">产品名称：</th>
              <td>{{ info.productName }}</td>
            </tr>
            <tr>
              <th width="80px">工业标识：</th>
              <td style="white-space: nowrap;text-overflow:ellipsis;overflow:hidden;">{{ info.idisCode }}</td>
            </tr>
            <tr>
              <th width="80px">产品编码：</th>
              <td>{{ info.productNo }}</td>
            </tr>
            <tr>
              <th width="80px">防伪码：</th>
              <td>{{ info.checkCode }}</td>
            </tr>
            <tr>
              <th width="80px">扫码时间：</th>
              <td>{{ info.createTime }}</td>
            </tr>
          </tbody>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import acc from '@/api/acc/acc'
import AMapLoader from '@amap/amap-jsapi-loader'
import icon from '@/assets/img/map_icon.png'
// import data from './data'
export default {
  name: 'Record',
  components: {
    DataSelect, DataTable

  },
  data() {
    return {
      style: [],
      activeName: '1',
      mapHeight: null,
      aMap: null,
      map: null,
      allMakers: null,
      infoWindow: null,
      info: {},
      icon,
      // data,
      center: [118.744613, 31.997981], // 试点
      searchHelper: new this.$searchHelper({ api: acc.recordPageList }),
      dataList: [],
      search: {
        condition: {
          label: '标识名称',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入标识名称'
          }
        },
        timeList: {
          label: '时间筛选',
          value: null,
          type: 'date',
          option: {
            type: 'daterange',
            startPlaceholder: this.$t('common.startDate'),
            endPlaceholder: this.$t('common.endDate'),
            unlinkPanels: true,
            placeholder: '请选择时间',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
          }
        }
      },
      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            width: '60px',
            sortable: false
          },
          {
            label: '工业标识',
            prop: 'idisCode',
            sortable: false
          }, {
            label: '产品名称',
            prop: 'productName',
            sortable: false
          }, {
            label: '扫码地址',
            prop: 'accessAddress'
          }, {
            label: 'IP地址',
            prop: 'accessIp'
          }, {
            label: '扫码时间',
            prop: 'createTime'
          }
        ]

      }
    }
  },
  watch: {},
  mounted() {
    this.searchHelper.handleQuery()
    window.onresize = () => {
      this.mapHeight = document.documentElement.clientHeight + 'px'
    }
    this.mapHeight = document.documentElement.clientHeight + 'px'
    acc.scanMapList().then((res) => {
      this.initMap(res.data)
    })
  },
  created() {

  },
  methods: {
    handleClick() {
    },
    initMap(data) {
      var points = data.length > 0 && data.map(e => { return { ...e, lnglat: [e.longitude, e.latitude] } })
      AMapLoader.load({
        key: 'd06ebbe2f13ce56c6d9eb9fab249290b',
        version: '2.0'
      })
        .then((AMap) => {
          this.aMap = AMap
          this.map = new AMap.Map('gisMap', {
            resizeEnable: true,
            // mapStyle: 'amap://styles/7450599add06e665e7fd5eb6c2b05098',
            viewMode: '2D',
            pitch: 45, // 地图俯仰角度，有效范围 0 度- 83 度 2D效果下无效
            zoomEnable: true, // 开启缩放功能zoomEnable: true, // 开启缩放功能
            zoom: 5, // 设置地图显示的缩放级别
            center: this.center // 设置地图中心点坐标
            // features: ["bg", "point", "road", "building"],
          })
          this.map.setZooms([4, 20])
          let MarkerCluster = null
          this.map.plugin(['AMap.MarkerCluster'], () => {
            MarkerCluster = new AMap.MarkerCluster(
              this.map, // 地图实例
              points, // 海量点数据，数据中需包含经纬度信息字段 lnglat
              {
                gridSize: 60, // 数据聚合计算时网格的像素大小
                // renderClusterMarker: this.renderClusterMarker, // 上述步骤的自定义聚合点样式
                renderMarker: this.renderMarker // 上述步骤的自定义非聚合点样式
              }
            )
          })
          MarkerCluster.on('click', (e) => {
            console.log(e.lnglat)
            this.map.setZoomAndCenter(this.map.getZoom() + 2, [e.lnglat.lng, e.lnglat.lat])
          })
          // 地图加载完成
          this.map.on('complete', () => {
            // 给地图绑定点击事件
            this.map.on('click', this.closeMap)
            // 自定义信息窗体
            this.infoWindow = new AMap.InfoWindow({
              anchor: 'top-left',
              autoMove: false, // 不改成false 的话 选择不在视图内的标记点并设置为中心点，会出现偏移现象
              offset: [5, 20], // 偏移量
              content: this.$refs.maps
            })
            // this.initMarker(AMap, this.map)
            // this.getAllSiteData()
          })
        })
    },
    getAllSiteData() {
      // this.map.remove(this.allMakers)
      this.allMakers = []
      this.data.forEach((item) => {
        const marker = new this.aMap.Marker({
          // map: that.map,
          // icon: item.icon, //自定义标记图片
          icon: this.icon,
          position: [item.lng, item.lat], // 坐标位置
          anchor: 'bottom-center'
        })
        // 添加标记点
        this.map.add(marker)
        // 给标记点绑定点击事件
        marker.on('click', this.clickMarker)
        // 存储所有的标记点对象
        this.allMakers.push(marker)
      })
    },
    clickMarker(e) {
      console.log(e.target._position)
      // 同时设置中心点和缩放级别
      this.map.setZoomAndCenter(this.map.getZoom(), e.target._position)
      this.infoWindow.open(this.map, e.target._position)
    },
    closeMap(e) {
      // 关闭信息窗体
      this.infoWindow.close()
      // 清空当前点击的站点对象信息
    },
    renderClusterMarker(context) {
      var count = 15
      // context 为回调参数，
      // 包含如下属性 marker:当前聚合点，count:当前聚合点内的点数量
      var factor = Math.pow(context.count / count, 1 / 18)
      var div = document.createElement('div')
      var Hue = 180 - factor * 180
      var bgColor = 'hsla(' + Hue + ',100%,40%,0.7)'
      var fontColor = 'hsla(' + Hue + ',100%,90%,1)'
      var borderColor = 'hsla(' + Hue + ',100%,40%,1)'
      var shadowColor = 'hsla(' + Hue + ',100%,90%,1)'
      div.style.backgroundColor = bgColor
      var size = Math.round(30 + Math.pow(context.count / count, 1 / 5) * 20)
      div.style.width = div.style.height = size + 'px'
      div.style.border = 'solid 1px ' + borderColor
      div.style.borderRadius = size / 2 + 'px'
      div.style.boxShadow = '0 0 5px ' + shadowColor
      div.innerHTML = context.count
      div.style.lineHeight = size + 'px'
      div.style.color = fontColor
      div.style.fontSize = '14px'
      div.style.textAlign = 'center'
      context.marker.setOffset(new this.aMap.Pixel(-size / 2, -size / 2))
      context.marker.setContent(div)
    },
    renderMarker(context) {
      const img = require('@/assets/img/1.png')
      const content = `<img style="width: 35px;height: 44px" src="${img}"/>`
      context.marker.on('click', () => {
        acc.getProductInfo({ id: context.data[0].codeId }).then((res) => {
          this.info = res.data
          this.infoWindow.open(this.map, context.marker.getPosition())
        })
      })
      var offset = new this.aMap.Pixel(-9, -9)
      context.marker.setContent(content)
      context.marker.setOffset(offset)
    }
  }
}
</script>

<style scoped lang="scss">
.record{
  overflow: auto;
}
.el-form-item {
  width: 100%;
}

::v-deep .dialog .el-form-item .el-form-item__label {
  text-align: right !important;
}

.button-container {
  line-height: 60px;
  text-align: center;
}
</style>

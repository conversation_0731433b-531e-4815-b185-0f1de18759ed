
function adaptDPR(canvas, ctx2d) { // 在初始化 canvas 的时候就要调用该方法
  const dpr = window.devicePixelRatio
  const { width, height } = canvas
  // 重新设置 canvas 自身宽高大小和 css 大小。放大 canvas；css 保持不变，因为我们需要那么多的点
  canvas.width = Math.round(width * dpr)
  canvas.height = Math.round(height * dpr)
  canvas.style.width = width + 'px'
  canvas.style.height = height + 'px'
  // 直接用 scale 放大整个坐标系，相对来说就是放大了每个绘制操作
  ctx2d.scale(dpr, dpr)
  return { width, height }
  // 接下来的绘制操作和往常一样，比如画个矩形 ctx2d.strokeRect(x, y, w, h);原来该怎么算就怎么算，该怎么调用还是怎么调用
}

export function drawxg(url) {
  const qeeue = []
  const draw = () => {
    qeeue.length === 4 && qeeue.forEach(async cb => {
      await cb()
    })
  }

  console.log(url)
  var title = '产品防伪溯源标识' // 自定义文本内容
  var companyTitle = '中国地理标志保护'
  const leftImg = require('@/assets/img/greenpic.png')
  const rightImg = require('@/assets/img/redpic.png')
  const xiguaImg = require('@/assets/img/xigua.png')
  var canvas = document.getElementById('canvas')
  var ctx = canvas.getContext('2d')

  const { width, height } = adaptDPR(canvas, ctx)

  var whColor = '#7EB095'
  var fontColor = 'rgb(255,255,255)'
  var bigCircleColor = '#3C6344'

  if (canvas.getContext) {
    // 开始绘制路径
    /**
         ctx.arc(x, y, radius, startAngle, endAngle, Boolean)
         圆心坐标: (x, y)
         半径: radius
         起始角度: startAngle
         结束角度: endAngle
         是否逆时针旋转: false 代表顺时针旋转
         */
    // 外圈蓝色圆形
    ctx.beginPath()
    ctx.lineWidth = 6
    ctx.strokeStyle = bigCircleColor
    // 绘制圆的路径**
    ctx.arc(130, 130, 117, 0, Math.PI * 2, false)
    ctx.fillStyle = bigCircleColor
    ctx.fill()
    // 描边路径
    ctx.stroke()

    ctx.beginPath()
    // ctx.lineWidth = 6;

    // 绘制圆的路径**
    ctx.arc(130, 130, 90, 0, Math.PI * 2, false)
    ctx.fillStyle = whColor
    ctx.fill()
    var circle = {
      x: 130,
      y: 130,
      radius: 104,
      whColor: whColor,
      fontColor: fontColor
    }
    // 平台标识
    drawCircularText(circle, title, rads(-50), rads(50), ctx)
    // 设置文本样式
    ctx.fillStyle = 'black'
    ctx.font = 'bold 12px 宋体'
    ctx.textAlign = 'center'

    // 绘制文本
    ctx.fillText('东台西瓜', (width / 2) - 35, (height / 4) + 8)
    var imgXG = new Image()
    imgXG.src = xiguaImg
    imgXG.onload = function() {
      qeeue.push(async() => { await ctx.drawImage(imgXG, (width / 2) - 4, (height / 4) - 2, 10, 10) })
      draw()
    }
    ctx.fillText('品冠天下', (width / 2) + 35, (height / 4) + 8)
    ctx.save()
    ctx.fillStyle = 'black'
    ctx.font = '4px Arial'
    ctx.textAlign = 'center'

    // 绘制文本
    ctx.fillText('DONGTAI WATERMELON,THE BEST QUALITY IN THE WORLD', width / 2, (height / 4) + 15)
    // 设置文本样式
    ctx.fillStyle = 'black'
    ctx.font = 'bold 8px Arial'
    ctx.textAlign = 'center'

    // 绘制文本
    ctx.fillText(`标识编号:`, (width / 2) - 46, (height / 4) * 3 - 5)

    ctx.fillText('东台国家现代农业产业园', width / 2, (height / 4) * 3 + 8)
    ctx.fillStyle = 'black'
    ctx.font = 'bold 9px 宋体'
    ctx.textAlign = 'center'
    ctx.fillText(`20240402193338904144`, (width / 2) + 18, (height / 4) * 3 - 5)
    ctx.imageSmoothingEnabled = true
    ctx.imageSmoothingQuality = 'high'
    var companyCircle = {
      x: 130,
      y: 130,
      radius: 102,
      whColor: whColor,
      fontColor: fontColor
    }
    // 企业标识
    drawCompanyCircularText(companyCircle, companyTitle, ctx)

    // 左图
    var imgLeft = new Image()
    imgLeft.src = leftImg
    imgLeft.onload = function() {
      qeeue.push(async() => { await ctx.drawImage(imgLeft, 90, 18, 35, 35) })
      draw()
    }
    ctx.save()
    // 右图
    var imgRight = new Image()
    imgRight.src = rightImg
    imgRight.onload = function() {
      qeeue.push(async() => { await ctx.drawImage(imgRight, 135, 18, 35, 35) })
      draw()
    }
    ctx.save()
    // 画二维码
    var img = new Image()
    img.src = url
    img.onload = function() {
      qeeue.push(async() => {
        await roundedRect((width / 2) - 45, height / 3, 90, 90, 10, ctx)
        await ctx.clip()
        await ctx.drawImage(img, (width / 2) - 45, height / 3, 90, 90)
      })
      draw()
    }
  }
}
// 绘制圆角矩形
function roundedRect(x, y, width, height, radius, ctx) {
  ctx.beginPath()
  ctx.moveTo(x + radius, y)
  ctx.lineTo(x + width - radius, y)
  ctx.arcTo(x + width, y, x + width, y + radius, radius)
  ctx.lineTo(x + width, y + height - radius)
  ctx.arcTo(x + width, y + height, x + width - radius, y + height, radius)
  ctx.lineTo(x + radius, y + height)
  ctx.arcTo(x, y + height, x, y + height - radius, radius)
  ctx.lineTo(x, y + radius)
  ctx.arcTo(x, y, x + radius, y, radius)
  ctx.closePath()
}

// 转换弧度
function rads(x) {
  return Math.PI * x / 180
}

function drawCompanyCircularText(s, string, ctx) {
  var radius = s.radius // 圆的半径
  // angleDecrement = (startAngle - endAngle) / (string.length - 1), //每个字母占的弧度
  var count = string.length / 2

  // 单个字符弧度
  var angleDecrement = 0.25
  // 计算初始位置
  var angle = parseFloat(rads(180)) - angleDecrement * (count - 1)
  var index = 0
  var character

  ctx.save()
  ctx.fillStyle = s.fontColor
  ctx.font = 'normal 15px 宋体'
  ctx.textAlign = 'right'
  ctx.textBaseline = 'middle'
  while (index < string.length) {
    character = string.charAt(index)
    ctx.save()
    ctx.beginPath()
    ctx.translate(s.x + Math.cos(angle) * radius,
      s.y + Math.sin(angle) * radius)
    ctx.rotate(Math.PI / 1.9 + angle)
    // 文字调转180
    ctx.fillText(character, 0, 0)
    angle += angleDecrement
    index++
    ctx.restore()
  }
  ctx.restore()
}

function drawCircularText(s, string, startAngle, endAngle, ctx) {
  var radius = s.radius // 圆的半径
  var angleDecrement = (startAngle - endAngle) / (string.length - 1) // 每个字母占的弧度
  var angle = parseFloat(startAngle)
  var index = 0
  var character
  ctx.save()
  ctx.fillStyle = s.fontColor
  ctx.font = 'normal 15px 宋体'
  ctx.textAlign = 'right'
  ctx.textBaseline = 'middle'
  while (index < string.length) {
    character = string.charAt(index)
    ctx.save()
    ctx.beginPath()
    ctx.translate(s.x + Math.cos(angle) * radius,
      s.y + Math.sin(angle) * radius)
    ctx.rotate(Math.PI / 1.9 + angle)
    // 文字调转180度
    // ctx.rotate(Math.PI)
    ctx.fillText(character, 0, 0)
    angle -= angleDecrement
    index++
    ctx.restore()
  }
  ctx.restore()
}

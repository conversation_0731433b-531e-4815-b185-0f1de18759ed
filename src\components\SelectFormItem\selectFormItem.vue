<template>
  <div>
    <template v-for="(item,index) in formData">
      <el-form-item
        v-show="item.show"
        :key="index"
        :prop="index"
        :for="item.for"
        :label="isShowLable?item.label+'：':null"
        :rules="item.rules"
        :required="item.required"
        :style="{width:auto ? null : item.fullWidth ? 'calc(100%)':item.width?item.width:'calc(50% - 10px)'}"
      >
        <el-input
          v-if="item.type=='input'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          @blur="(event) => {if(item&&item.event&&typeof item.event.blur == 'function') item.event.blur(event)}"
          @focus="(event) => {if(item&&item.event&&typeof item.event.focus == 'function') item.event.focus(event)}"
          @change="(value) => {if(item&&item.event&&typeof item.event.change == 'function') item.event.change(value)}"
          @input="(value) => {if(item&&item.event&&typeof item.event.input == 'function') item.event.input(value)}"
          @clear="() => {if(item&&item.event&&typeof item.event.clear == 'function') item.event.clear()}"
        >
          <template v-if="item.option && item.option.prefix" slot="prefix"><span v-html="item.option.prefix" /></template>
          <template v-if="item.option && item.option.suffix" slot="suffix"><span v-html="item.option.suffix" /></template>
        </el-input> <!-- clearable -->

        <el-input-number
          v-if="item.type=='number'"
          :ref="index"
          v-model="item.value"
          :class="['date-width',item.popperClass]"
          v-bind="item.option"
          @blur="(event) => {if(typeof item.event.blur == 'function') item.event.blur(event)}"
          @focus="(event) => {if(typeof item.event.focus == 'function') item.event.focus(event)}"
          @change="(value) => {if(typeof item.event.change == 'function') item.event.change(value)}"
        >
          <template v-if="item.option && item.option.prefix" slot="prefix"><span v-html="item.option.prefix" /></template>
          <template v-if="item.option && item.option.suffix" slot="suffix"><span v-html="item.option.suffix" /></template>
        </el-input-number>
        <select-check-box
          v-if="item.type=='select'"
          v-model="item.value"
          :multiple="item.option.multiple"
          :list="item.option.selectOptions"
          @change="(value)=>{if (typeof item.event.change === 'function') item.event.change(value)}"
          @select-all="(value)=>{if (typeof item.event.selectAll === 'function') item.event.selectAll(value)}"
        />
        <chosen
          v-if="item.type === 'chosen'"
          :id="item.option.id"
          :ref="index"
          v-model="item.value"
          :value-label="item.valueLabel"
          :class="['date-width',item.popperClass]"
          v-bind="item.option"
          :options.sync="item.option.options"
          @change="(value) =>{if (typeof item.event.change == 'function') item.event.change(value)}"
          @visible-change="(show) => {if (typeof item.event.visibleChange == 'function') item.event.visibleChange(show)}"
          @remove-tag="(tag) => {if (typeof item.event.removeTag == 'function') item.event.removeTag(tag)}"
          @clear="() => {if (typeof item.event.clear == 'function') item.event.clear()}"
          @blur="(event) => {if (typeof item.event.blur == 'function') item.event.blur(event)}"
          @focus="(event) => {if (typeof item.event.focus == 'function') item.event.focus(event)}"
          @update:valueLabel="(valueLabel) => item.valueLabel=valueLabel"
        />
        <tree-picker
          v-if="item.type === 'tree'"
          :ref="index"
          v-model="item.value"
          :value-label="item.valueLabel"
          v-bind="item.option"
          :tree-option="item.treeOption"
          @update:valueLabel="(valueLabel) => item.valueLabel=valueLabel"
        />
        <date-picker
          v-if="item.type=='date'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          :class="['date-width',item.popperClass]"
          @change="(value) => {if (typeof item.event.change == 'function') item.event.change(value)}"
          @blur="() => {if (typeof item.event.blur == 'function') item.event.blur()}"
          @focus="() => {if (typeof item.event.focus == 'function') item.event.focus()}"
        />
        <time-picker
          v-if="item.type=='time'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          :class="['date-width',item.popperClass]"
          :format="item.option.format"
          @change="(value) => {if (typeof item.event.change == 'function') item.event.change(value)}"
          @blur="() => {if (typeof item.event.blur == 'function') item.event.blur()}"
          @focus="() => {if (typeof item.event.focus == 'function') item.event.focus()}"
        />
        <time-select
          v-if="item.type=='time-select'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          :class="['date-width',item.popperClass]"
          @change="(value) => {if (typeof item.event.change == 'function') item.event.change(value)}"
          @blur="() => {if (typeof item.event.blur == 'function') item.event.blur()}"
          @focus="() => {if (typeof item.event.focus == 'function') item.event.focus()}"
        />
        <el-radio-group
          v-if="item.type=='radio'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          @change="(value)=>{if (typeof item.event.change == 'function' ) item.event.change(value)}"
        >
          <template v-if="!item.option.isButton">
            <el-radio
              v-for="(radioItem,radioIndex) in item.option.radioList"
              :key="radioIndex"
              v-model="item.value"
              v-bind="item.option"
            >{{ radioItem.name }}</el-radio> </template>
          <template v-if="item.option.isButton">
            <el-radio-button
              v-for="(radioItem,radioIndex) in item.option.radioList"
              :key="radioIndex"
              :label="radioItem.label"
              :disabled="radioItem.disabled"
              :name="radioItem.name"
            >{{ radioItem.name }}</el-radio-button>
          </template>
        </el-radio-group>
        <el-switch
          v-if="item.type=='switch'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          @change="(value) => {if (typeof item.event.change == 'function') item.event.change(value)}"
        />
        <j-cascader
          v-if="item.type === 'j-cascader'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          :value-label.sync="item.valueLabel"
          v-on="$listeners"
        />
        <cascader
          v-if="item.type=='cascader'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          @change="(value) => {if (typeof item.event.change == 'function') item.event.change(value)}"
          @expand-change="(value) => {if (typeof item.event.expandChange == 'function') item.event.expandChange(value)}"
          @blur="(value) => {if (typeof item.event.blur == 'function') item.event.blur(value)}"
          @focus="(value) => {if (typeof item.event.focus == 'function') item.event.focus(value)}"
          @visible-change="(value) => {if (typeof item.event.visibleChange == 'function') item.event.visibleChange(value)}"
          @remove-tag="(value) => {if (typeof item.event.removeTag == 'function') item.event.removeTag(value)}"
          @show="show"
          @after-enter="afterEnter"
          @after-leave="afterLeave"
        />
        <!-- 上传图片 -->
        <slot
          v-if="item.type=='slot' && (!showSpecialSlot || item.notPop)"
          :data="item"
        />

        <template v-if="item.type=='slot' &&(showSpecialSlot && !item.notPop)">

          <el-popover
            :ref="'fadeInLinearFormItemRef'+index"
            placement="bottom-start"
            trigger="click"
            scroll-hide
            :transition="'fade-in-linear-select-from-item-'+index"
            :popper-class="'el-popper-owner max-height-1'"
          >
            <div slot="reference" class="el-input el-input--small el-input--suffix">
              <input v-if="item.option&&item.inputId" :id="item.inputId" type="text" autocomplete="off" rows="2" readonly="readonly" :placeholder="item.option.placeholder" class="el-input__inner">
            </div>
            <slot
              :data="item"
            />
          </el-popover>
        </template>
      </el-form-item>
    </template>
    <slot name="end" />
  </div>
</template>
<script>
import Chosen from '@/components/Chosen'
import TreePicker from '@/components/TreePicker/TreePicker'
import SelectCheckBox from './DataSelectCheckBox/selectCheckBox'
import cascader from './DataCascader/cascader'
import formItemMixins from './form-item-mixins'
export default {
  name: 'SelectFromItem',
  components: { Chosen, SelectCheckBox, cascader, TreePicker },
  mixins: [formItemMixins]
}
</script>

<style lang="scss" scoped>
@import "~element-ui/packages/theme-chalk/src/mixins/mixins";
.el-popover{
  max-height: 50vh;
}
.date-width{
  width: 100%;
}
@include b(select-dropdown) {
  .all-select-header {
    margin: 3px 0;

    &:hover {
      background-color: unset;
    }
    &.selected {
      .el-checkbox__inner {
        background-color: $--checkbox-checked-background-color;
        border-color: $--checkbox-checked-input-border-color;

        &::after {
          transform: rotate(45deg) scaleY(1);
        }
      }
    }
  }
  & .el-select-dropdown__item {
    &.selected {
      .el-checkbox__inner {
        background-color: $--checkbox-checked-background-color;
        border-color: $--checkbox-checked-input-border-color;

        &::after {
          transform: rotate(45deg) scaleY(1);
        }
      }
    }
  }
}
</style>

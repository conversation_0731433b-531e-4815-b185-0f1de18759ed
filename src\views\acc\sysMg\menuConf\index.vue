<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      @return-search="handleSearch"
      @return-reset="reset"
    />
    <!-- 表格 -->
    <data-table
      ref="dataTable"
      :table-data="menus"
      :column="column"
      :table-option="{
        option: {
          treeProps: {children: 'menuInfoVOList'},
          rowKey: 'menuCode',
          stripe: false
        }
      }"
    >
      <template
        v-slot:menuType="{row}"
      >
        <el-tag
          :type="row.menuType === '1' ? null: 'success'"
          size="medium"
        >{{ row.menuType === '1' ? '页面' : '按钮' }}</el-tag>
      </template>
      <template v-slot:menuName="{row}">
        <span v-html="renderSearch(row.menuName)" />
      </template>
    </data-table>
    <edit-dialog ref="dialog" @confirm="confirm" />
  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import accApi from '@/api/acc/acc'
import EditDialog from './editDialog'

export default {
  name: 'MenuConf',
  components: {
    EditDialog,
    DataSelect,
    DataTable
  },
  data() {
    return {
      searchVal: null,
      search: {
        menuName: {
          label: '菜单名称',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入菜单名称'
          }
        }
      },
      tableData: [],
      column: {
        // 表头
        data: [
          {
            label: '菜单名称',
            prop: 'menuName',
            slotName: 'menuName',
            sortable: false
          },
          {
            label: '菜单编码',
            prop: 'menuCode',
            sortable: false
          },
          {
            label: '菜单URL',
            prop: 'menuUrl',
            sortable: false,
            width: 120
          }, {
            label: '排序号',
            prop: 'sort',
            sortable: false,
            width: 80
          }, {
            label: '菜单类型',
            prop: 'menuType',
            sortable: false,
            slotName: 'menuType',
            width: 80
          }
        ],
        // 操作
        operation: {
          label: '操作',
          width: '150px',
          data: (row) => {
            const button = []
            button.push({
              label: '编辑',
              action: this.onEditClick,
              permission: 'menuConf:edit'
            })
            if (row.menuType === '1') {
              button.push({
                label: '新增',
                action: this.onAddClick,
                permission: 'menuConf:add'
              })
            }
            button.push({
              label: '删除',
              action: this.onDeleteClick,
              permission: 'menuConf:delete'
            })
            return button
          }
        }
      }
    }
  },
  computed: {
    menus() {
      if (this.searchVal && this.searchVal.trim !== '') {
        return this.menusFilter(this.$util.cloneDeep(this.tableData), this.searchVal)
      } else {
        return this.tableData
      }
    }
  },
  mounted() {
    this.queryList()
  },
  methods: {
    queryList() {
      accApi.queryMenuConfList().then(res => {
        if (res.data) {
          this.tableData = res.data
        }
      })
    },
    handleSearch(data) {
      this.searchVal = data.menuName
    },
    reset() {
      this.searchVal = null
    },
    onAddClick(data) {
      this.$refs.dialog.open('add', data)
    }, onEditClick(data) {
      this.$refs.dialog.open('edit', data)
    }, onDeleteClick(data) {
      this.$confirm(this.$t('common.confirmDel'), this.$t('common.prompt')).then(() => {
        accApi.deleteMenuConf(data.menuId).then(res => {
          this.$message.success(res.msg)
          this.queryList()
        })
      })
    },
    confirm(data) {
      accApi.saveOrUpdateMenu(data).then(res => {
        this.$refs.dialog.close()
        this.$message.success(res.msg)
        this.queryList()
      })
    },
    menusFilter(menus, name) {
      return menus.filter(menu => {
        if (menu.menuInfoVOList) {
          menu.menuInfoVOList = this.menusFilter(menu.menuInfoVOList, name)
        }
        return menu.menuName.includes(name) || menu.menuInfoVOList.length > 0
      })
    },
    renderSearch(val) {
      if (this.searchVal && val && val.toString().includes(this.searchVal.trim())) {
        return val.toString().replace(this.searchVal.trim(), `<font class="search-color">${this.searchVal.trim()}</font>`)
      } else {
        return val
      }
    }
  }
}
</script>

<style scoped>

</style>

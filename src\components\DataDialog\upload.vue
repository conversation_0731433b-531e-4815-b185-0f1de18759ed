<template>
  <div>
    <el-upload
      ref="upload"
      action
      :multiple="false"
      :auto-upload="true"
      :show-file-list="false"
      :http-request="choosePic"
      :before-upload="beforeUpload"
      :disabled="imgUrl != ''"
      class="uploader"
      :class="{'uploader-haspic': imgUrl != '', 'uploader-disabled': disabled}"
    >
      <el-image
        v-if="imgUrl"
        :src="imgUrl"
        :preview-src-list="[imgUrl]"
        class="uploader-icon image"
      />
      <i
        v-else
        class="el-icon-plus uploader-icon"
      />
      <i
        v-if="imgUrl"
        class="el-icon-close uploader-delete"
        @click.stop="clearPic"
      />
    </el-upload>
    <div
      slot="tip"
      class="el-upload__tip"
    >{{ $t('common.uploadText') }}</div>
  </div>
</template>

<script>
import commonApi from '@/api/common/common'
import { mapState } from 'vuex'
export default {
  name: 'UploadPic',
  props: {
    value: {
      type: String,
      default: null
    },
    param: {
      type: [String, Number, Boolean, Array, Object, Function, Promise],
      default: null
    },
    disabled: {
      type: Boolean,
      default: null
    }
  },
  data() {
    return {
      imgUrl: ''
    }
  },
  computed: {
    ...mapState({
      merchantCode: (state) => state.user.merchantCode
    })
  },
  watch: {
    value(newVal) {
      this.imgUrl = newVal || ''
    }
  },
  mounted() {
    this.imgUrl = this.value ? this.value : ''
  },
  methods: {
    choosePic(param) {
      const formData = new FormData()
      formData.append('file', param.file)
      formData.append('merchantId', this.merchantCode)
      formData.append('fileSource', 'LOCAL')
      formData.append('filePath', this.param)
      const isLt5M = param.file.size / 1024 / 1024
      if (isLt5M > 4) {
        this.$message({
          message: this.$t('common.imgLimit'),
          type: 'error'
        })
      }
      commonApi
        .uploadImg(formData)
        .then((res) => {
          // eslint-disable-next-line eqeqeq
          if (res.code == 'SUCCESS') {
            this.imgUrl = res.data.url
            this.$nextTick(() => {
              this.$emit('update:value', res.data.url)
            })
          } else {
            this.$emit('update:value', '')
          }
        })
        .catch(() => {})
    },
    clearPic() {
      if (this.disabled) {
        return
      }
      this.$refs.upload.clearFiles()
      this.imgUrl = ''
      this.$emit('update:value', this.imgUrl)
    },
    beforeUpload(file) {
      const typeArr = file.name.split('.')
      const type = typeArr[typeArr.length - 1]
      const list = ['jpg', 'JPG', 'png', 'PNG', 'gif', 'GIF']
      if (!list.includes(type)) {
        this.$message.error(this.$t('common.imgFileType'))
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 8
      if (!isLt2M) {
        this.$message.error(this.$t('common.imgLimit'))
      }
      return isLt2M
    }
  }
}
</script>

<style lang="scss" scoped>
.uploader {
  width: 90px;
  height: 90px;
  position: relative;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
  &:hover {
    border-color: #409eff;
  }
  &.uploader-disabled {
    background: #f5f7fa;
    cursor: not-allowed;
    &:hover {
      border-color: #d9d9d9;
    }
    ::v-deep .el-upload {
      cursor: not-allowed;
    }
    .uploader-delete {
      display: none;
    }
  }
}
.uploader-icon {
  display: block;
  width: 90px;
  height: 90px;
  font-size: 22px;
  line-height: 90px;
  color: #828282;
  text-align: center;
}
.uploader-delete {
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: 16px;
  background: #fff;
}
.image {
  position: inherit;
}

.image img {
  height: auto;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 90px;
  height: 90px;
  transform: translate(-50%, -50%);
}
</style>
<style>
.uploader-icon .el-image-viewer__close {
  color: #ffffff !important;
}
</style>

import store from '@/store'

/**
 * @param {Array} value
 * @returns {Boolean}
 * @example see @/views/permission/directive.vue
 */

export default function checkBtnPermission(value) {
  // eslint-disable-next-line eqeqeq
  if (store.getters.userType == '1' || value == 'all') {
    return true
  }
  if (!value) {
    return false
  }
  // 获取用户按钮权限
  const dynamicButtons = store.getters.buttons
  if (
    dynamicButtons === undefined ||
    dynamicButtons === null ||
    dynamicButtons.length < 1
  ) {
    return false
  }

  if (value instanceof Array && value.length > 0) {
    const hasPermission = dynamicButtons.some(button => {
      return value.includes(button.code)
    })
    return !!hasPermission
  } else {
    const hasPermission = dynamicButtons.some(button => {
      return button.code === value
    })
    return !!hasPermission
  }
}

import acc from '@/api/acc/acc'
const dictionary = {
  state: {
    allDictionary: {}
  },
  mutations: {
    'ALL_DICTIONARY': (state, pararm) => {
      state.allDictionary = pararm
    }
  },
  actions: {
    getAllDictList({ commit }) {
      acc.dictionaryAllListApi().then((res) => {
        // 按type分组
        const map = new Map()
        res.data.forEach((item, index, arr) => {
          if (!map.has(item.typeCode)) {
            map.set(
              item.typeCode,
              arr.filter(a => a.typeCode === item.typeCode)
            )
          }
        })
        // 转字典对象
        var obj = {}
        Array.from(map).forEach((e) => {
          obj[e[0]] = e[1].map(item => {
            return {
              label: item.dicName,
              value: item.dicCode
            }
          })
        })
        commit('ALL_DICTIONARY', obj)
      })
    }
  }
}
export default dictionary

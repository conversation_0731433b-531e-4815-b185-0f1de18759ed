<template>
  <div class="j-cascader">
    <el-cascader
      ref="cascader"
      v-bind="$props"
      :options="innerOptions"
      v-on="$listeners"
      @change="onChange"
      @visible-change="onVisibleChange"
      @expand-change="onExpandChange"
    />
    <slot name="wrapper" :on="wrapperEvent" />
  </div>
</template>

<script>
/* eslint-disable */
function pushNotExit(arr, item) {
  if (arr.indexOf(item) < 0 && item) {
    arr.push(item)
  }
}

export default {
  name: 'JCascader',
  props: {
    valueLabel: {
      type: [String, Array],
      default: undefined
    },
    useWrapper: {
      type: Boolean,
      default: false
    },
    value: {},
    options: Array,
    props: Object,
    size: String,
    placeholder: {
      type: String,
      default: ''
    },
    disabled: Boolean,
    clearable: Boolean,
    filterable: Boolean,
    filterMethod: Function,
    separator: {
      type: String,
      default: ' / '
    },
    showAllLevels: {
      type: <PERSON><PERSON><PERSON>,
      default: true
    },
    collapseTags: <PERSON><PERSON><PERSON>,
    debounce: {
      type: Number,
      default: 300
    },
    beforeFilter: {
      type: Function,
      default: () => () => {
      }
    },
    popperClass: String
  },
  data() {
    return {
      innerOptions: [],
      innerValue: [],
      loadCount: 0,
      wrapperEvent: {
        click: this.onInputClick
      }
    }
  },
  created() {

  },
  mounted() {
    if (this.useWrapper) {
      this.$nextTick(() => {
        const elCascader = this.$refs.cascader.$el
        elCascader.querySelector('.el-cascader__tags').style.display = 'none'
        elCascader.querySelector('.el-input').style.display = 'none'
        elCascader.style.position = 'absolute'
        elCascader.style.height = '100%'
        elCascader.style.width = '0'
      })
    }
  },
  methods: {
    onInputClick() {
      this.initCascaderOptions()
    },
    onVisibleChange(visible) {
      if (!visible) {
        this.$emit('after-leave')
      } else {
        this.$emit('after-enter')
      }
    },
    onExpandChange(pathValues) {
      pathValues.forEach(item => {
        const node = this.$refs.cascader.panel.getNodeByValue(item)
        if (node.checked) {
          this.innerValue = pathValues
        }
      })
    },
    onChange(values) {
      if (this.props.multiple) {
        const valueLabel = []
        values.forEach(items => {
          if (Array.isArray(items)) {
            const emits = []
            items.forEach(item => {
              emits.push(this.$refs.cascader.panel.getNodeByValue(item).label)
            })
            valueLabel.push(emits.join(this.separator))
          } else {
            const label = this.$refs.cascader.panel.getNodeByValue(items).label
            valueLabel.push(label)
          }
        })
        this.$emit('update:valueLabel', valueLabel)
      }
    },
    lazyLoad(node) {
      const panel = this.$refs.cascader.panel
      panel.lazyLoad(node, dataList => {
        const valueKey = panel.config.value
        const level = node?.level || 0
        const checkedValue = []
        const isChecked = []
        this.value.forEach(checkValue => {
          if (Array.isArray(checkValue) && checkValue.length > level) {
            pushNotExit(checkedValue, checkValue[level])
            if (checkValue.length - 1 === level) {
              pushNotExit(isChecked, checkValue[level])
            }
          }
          if (!Array.isArray(checkValue) && level === 0) {
            pushNotExit(checkedValue, checkValue)
            pushNotExit(isChecked, checkValue)
          }
        })
        checkedValue.forEach(nodeValue => {
          if (
            Array.isArray(dataList) && dataList.filter(item => item[valueKey] === nodeValue).length > 0
          ) {
            const checkedNode = this.$refs.cascader.panel.getNodeByValue(nodeValue)
            if (isChecked.includes(nodeValue)) {
              this.loadCount++
            }
            this.lazyLoad(checkedNode)
          }
        })
      })
      if (this.value.length === this.loadCount) {
        panel.$parent.computePresentContent()
        panel.syncMenuState()
      }
    },
    initCascaderOptions() {
      if (this.props.lazy && this.props.multiple) {
        if (this.value.length > 0) {
          this.loadCount = 0
          this.lazyLoad()
        }
        if (this.useWrapper) {
          this.$refs.cascader.toggleDropDownVisible(true)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.j-cascader {
  position: relative;
}

.el-cascader {
  width: 100%;
}

::v-deep .el-cascader-menu__wrap {
  overflow-x: auto !important;
  background-color: red;
}
</style>

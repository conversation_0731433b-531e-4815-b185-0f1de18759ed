<template>
  <div
    class="app-container applicationProject"
  >

    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      @return-search="(data) => {searchHelper.search(data)}"
      @return-reset="searchHelper.reset"
    />
    <data-table
      ref="dataTable"
      show-summary
      :table-data="searchHelper.dataList"
      :column="column"
      :table-index="3"
      :pagination.sync="searchHelper.pagination"
      @search-event="() => {searchHelper.handleQuery()}"
    >
      <template v-slot:code="{ row }">
        <el-link type="primary" @click="onDetailClick(row)">{{ row.code }}</el-link>
      </template>
    </data-table>

  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import acc from '@/api/acc/acc'

export default {
  name: 'DictMaintenance',
  components: { DataSelect, DataTable },
  data() {
    return {
      searchHelper: new this.$searchHelper({ api: acc.dictionaryListApi }),
      dataList: [],
      search: {
        code: {
          label: '字典类型编码',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入字典类型编码'
          }
        },
        name: {
          label: '字典类型名称',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入字典类型名称'
          }
        }
      },
      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            sortable: false
          },
          {
            label: '字典类型编码',
            prop: 'code',
            slotName: 'code',
            sortable: false
          },
          {
            label: '字典类型名称',
            prop: 'name',
            sortable: false
          },
          {
            label: '描述',
            prop: 'description',
            sortable: false
          }
        ]
      }
    }
  },
  mounted() {
    this.searchHelper.handleQuery()
  },
  methods: {
    onDetailClick(row) {
      this.$router.replace({ path: '/accPortal/acc/sysMg/dictMaintenanceDetail', query: { id: row.id }})
    }

  }
}
</script>

<style scoped lang="scss">
.applicationProject {
  //margin-left: 10px;
  //margin-top: 10px;
  //overflow: auto;
  .title {
    color: #0D93F4;
    border-bottom: 2px solid #0D93F4;
    padding: 5px 0;
    font-weight: bolder
  }

  .cardOne {
    margin-bottom: 20px;

    .box {
      cursor: pointer;
      width: 100%;
      height: 60px;
      display: flex;
      border: 1px solid #dedede;
      border-radius: 5px;

      .left {
        width: 60px;
        height: 60px;
        background-color: #4B7EE0;
        border-bottom-left-radius: 5px;
        border-top-left-radius: 5px;
        text-align: center;
        line-height: 60px;

        img {
          width: 20px;
          height: 20px;
          margin-top: 20px
        }
      }

      .right {
        margin-left: 10px;

        .title1 {
          font-size: 14px;
          margin-top: 12px;
          margin-bottom: 5px
        }

        .title2 {
          transform: scale(0.9) translateX(-6px);
          color: #8a8989
        }
      }
    }
  }
}
</style>

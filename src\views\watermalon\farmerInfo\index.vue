<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="(data) => {searchHelper.search(data)}"
      @return-reset="searchHelper.reset"
    />
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="() => {searchHelper.handleQuery()}"
    />
  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import acc from '@/api/acc/acc'
import accApi from '@/api/acc/acc'
import query from '@/components/mixin/query'
const StatusMapping = {
  'Y': '公司',
  'N': '瓜农'
}
export default {
  name: 'FarmerInfo',
  components: { DataSelect, DataTable },
  mixins: [query],
  data() {
    return {
      buttonData: [
        {
          label: '添加',
          action: this.onAddClick,
          permission: 'all'
        }
      ],
      searchHelper: new this.$searchHelper({ api: acc.farmerPageList }),
      dataList: [],
      search: {
        searchText: {
          label: '姓名',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入姓名'
          }
        }
      },
      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            sortable: false
          },
          {
            label: '村别',
            prop: 'village',
            sortable: false
          },
          {
            label: '姓名',
            prop: 'user',
            sortable: false
          },
          {
            label: '编码',
            prop: 'userCode',
            sortable: false
          },
          {
            label: '电话',
            prop: 'tel',
            sortable: false
          },
          {
            label: '类型',
            prop: 'isCompany',
            format(row) {
              return StatusMapping[row.isCompany]
            },
            sortable: false
          }
        ],
        operation: {
          label: '操作',
          width: '120px',
          data: [
            {
              label: '编辑',
              action: this.onEditClick,
              permission: 'all'
            }, {
              label: '删除',
              action: this.onDeleteClick,
              permission: 'all'
            }]
        }
      }
    }
  },
  methods: {
    onAddClick() {
      this.$router.replace({ path: 'farmerDetail', query: { action: 'add' }})
    },
    onDeleteClick(row) {
      this.$confirm('请确认是否删除?', '提示').then(() => {
        accApi.deleteFarmer(row).then(res => {
          this.$message.success(res.message)
          this.searchHelper.handleQuery()
        })
      })
    },
    onEditClick(row) {
      this.$router.replace({ path: 'farmerDetail', query: { action: 'edit', id: row.id }})
    }
  }
}
</script>

<style scoped lang="scss">
</style>

<template>
  <div class="app-container statistic">
    <div class="statistic_left">
      <el-card class="card_box">
        <div slot="header" class="clearfix">
          <span class="title">扫码数量展示图</span>
        </div>
        <el-button-group>
          <el-button :type="clickIndex===7?'success':'primary'" @click="changeTime(7)">近7天</el-button>
          <el-button :type="clickIndex===15?'success':'primary'" @click="changeTime(15)">近15天</el-button>
          <el-button :type="clickIndex===30?'success':'primary'" @click="changeTime(30)">近30天</el-button>
        </el-button-group>
        <div style="height: 260px">
          <div>
            <chart v-if="chartOne.xAxis.data&&chartOne.xAxis.data.length>0" id-name="chartBarTwo" :reset-options="chartOne" type="line" />
            <el-empty v-else class="empty" description="暂无数据" />
          </div>
        </div>
      </el-card>
      <div class="flex-row">
        <el-card class="card_box title-count-card">
          <div slot="header" class="clearfix">
            <span class="title">区域统计扫码数</span>
          </div>
          <div style="height: 360px">
            <chart
              v-if="chartTwo.xAxis.data&&chartTwo.xAxis.data.length>0"
              style="height: 360px"
              id-name="chartBarOne1"
              :reset-options="chartTwo"
              type="bar"
            />
            <el-empty v-else class="empty" description="暂无数据" />
          </div>
        </el-card>
        <el-card class="card_box title-count-card">
          <div slot="header" class="clearfix">
            <span class="title">近一周标识生成量</span>
          </div>
          <div style="height: 360px">
            <chart
              v-if="chartThree.xAxis.data&&chartThree.xAxis.data.length>0"
              style="height: 360px"
              id-name="chartBarOne11"
              :reset-options="chartThree"
              type="bar"
            />
            <el-empty v-else class="empty" description="暂无数据" />
          </div>
        </el-card>
      </div>

    </div>
    <div class="statistic_right">
      <el-card class="card_box title-count-card">
        <div slot="header" class="clearfix">
          <span class="title">标识排行</span>
        </div>
        <div v-if="scanCodeRankingList.length>0" style="height: 305px">
          <div v-for="(e,i) in scanCodeRankingList" :key="i" class="title-count-box">
            <div
              v-if="i<3"
              class="icon"
              :style="{'background-image':`url(${require(`../../../../assets/img/icon_${i+1}.png`)})`}"
            >{{ i + 1 }}
            </div>
            <div v-else  class="iconLast">{{ i+1 }}</div>
            <div><img src="@/assets/icon/logo.png"></div>
            <div class="text">
              <p>{{ e.idisCode }}</p>
              <p>{{ e.productName }}</p>
            </div>
            <div class="count" :style="{'color':i<4?'#e88316':'#666'}">{{ e.scanCount }}次</div>
          </div>
        </div>
        <el-empty v-else style="height: 305px" class="empty" description="暂无数据" />
      </el-card>
      <el-card class="card_box title-count-card">
        <div slot="header" class="clearfix">
          <span class="title">产品排行</span>
        </div>
        <div v-if="scanCodeRankingList.length>0" style="height: 119px">
          <div v-for="(e,i) in productRankingList" :key="i" class="product-sort-box">
            <div class="icon" :style="{'background-color':i<3?'#e88316':'#b5b5b5'}">{{ i + 1 }}</div>
            <div class="text">
              {{ e.productName }}
            </div>
            <div class="count">{{ e.scanQuantity }}次</div>
          </div>
        </div>
        <div v-else style="height: 120px;">
          <el-empty style="height: 140px;width: 87px ;padding-top: 0px" class="empty" description="暂无数据" />
        </div>
      </el-card>
      <el-card class="card_box title-count-card">
        <div slot="header" class="clearfix">
          <span class="title">客户端类型</span>
        </div>
        <div style="height: 169px">
          <chart v-if="chartFour.series[0].data&&chartFour.series[0].data.length>0" style="height: 169px" id-name="chartFour" :reset-options="chartFour" type="bar" />
          <el-empty v-else style="padding-top: 0 !important;" class="empty" description="暂无数据" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>

import Chart from '@/components/Chart/Chart.vue'
import acc from '@/api/acc/acc'

export default {
  name: 'Statistic',
  components: {
    Chart

  },
  data() {
    return {
      scanCodeRankingList: [],
      productRankingList: [],
      chartOne: {
        xAxis: {
          type: 'category',
          axisLabel: {
            color: '#4BB4AA'// x轴字
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#4BB4AA' // x轴线条
            }
          },
          boundaryGap: false, // 不保留边界间隙，确保x轴从头开始显示数据
          data: []
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#4BB4AA'// x轴字
          },
          axisTick: {
            show: true
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#4BB4AA' // y轴线条
            }
          }

        },
        grid: {
          left: '30px',
          right: '40px',
          bottom: '10px',
          top: '40px',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          formatter: `
            {b} <br />
             <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#4BB4AA;"></span>数量：{c}`
        },
        series: [
          {
            data: [],
            type: 'line',
            smooth: true,
            areaStyle: {
              color: '#4BB4AA'
            },
            lineStyle: {
              color: '#4BB4AA' // 折线颜色
            },
            itemStyle: {
              normal: {
                color: '#4BB4AA' // 折线点颜色
              }
            }
          }
        ]
      },
      clickIndex: 7,
      chartTwo: {
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: true
          },
          axisLine: {
            show: true
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: `
            {b} <br />
             <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#67B2FF;"></span>扫码数量：{c}`
        },
        grid: {
          left: '30px',
          right: '30px',
          bottom: '10px',
          top: '30px',
          containLabel: true
        },
        series: [
          {
            data: [],
            showBackground: true,
            type: 'bar',
            barWidth: '40%', // 这里设置了宽度为容器宽度的50%
            barMaxWidth: 30, // 设置柱子的最大宽度为30
            color: '#67B2FF'
          }
        ]
      },
      chartThree: {
        xAxis: {
          type: 'category',
          axisLine: {
            show: true
          },
          boundaryGap: false, // 不保留边界间隙，确保x轴从头开始显示数据
          data: []
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: true
          },
          axisLine: {
            show: true
          }

        },
        grid: {
          left: '30px',
          right: '30px',
          bottom: '10px',
          top: '30px',
          containLabel: true
        },
        series: [
          {
            data: [],
            type: 'line',
            smooth: true,
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(32,137,229, 1)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(32,137,229, 0.5)' // 100% 处的颜色
                  }
                ]
              }
            },
            lineStyle: {
              color: '#228AE6' // 折线颜色
            },
            itemStyle: {
              normal: {
                color: '#228AE6' // 折线点颜色
              }
            }
          }
        ]
      },
      chartFour: {
        tooltip: {
          trigger: 'item',
          confine: true, // 解决超出外部容器后被遮挡问题
          formatter: `
            客户端类型<br />
            {b} ：{c}({d}%)`
        },
        legend: {
          right: 'right',
          orient: 'vertical'

        },
        xAxis: {
          show: false
        },
        yAxis: {
          show: false
        },
        series: [
          {
            type: 'pie',
            center: ['30%', '50%'],
            radius: ['40%', '70%'],
            minAngle: 15, // 最小角度
            startAngle: 270, // 起始角度
            avoidLabelOverlap: false,
            labelLine: {
              normal: {
                length: 5
              }
            },
            itemStyle: {},
            emphasis: {
              label: {
                show: true
              }
            },
            data: []
          }
        ]
      }
    }
  },
  watch: {},
  mounted() {

  },
  created() {
    this.getChartOne()
    this.getChartTwo()
    this.getCharThree()
    this.getChartFour()
    this.getScanCodeRanking()
    this.getProductRanking()
  },
  methods: {
    changeTime(val) {
      this.clickIndex = val
      this.getChartOne()
    },
    getChartOne() {
      acc.quantityDisplayListApi({ queryDays: this.clickIndex }).then((res) => {
        this.$set(this.chartOne.xAxis, 'data', res.data.xaxis)
        this.$set(this.chartOne.series[0], 'data', res.data.yaxis)
      })
    },
    getChartTwo() {
      acc.areaScanningCodeList().then((res) => {
        this.$set(this.chartTwo.xAxis, 'data', res.data.xaxis)
        this.$set(this.chartTwo.series[0], 'data', res.data.yaxis)
      })
    },
    getCharThree() {
      acc.weeklyCodeList().then((res) => {
        this.$set(this.chartThree.xAxis, 'data', res.data.xaxis)
        this.$set(this.chartThree.series[0], 'data', res.data.yaxis)
      })
    },
    getChartFour() {
      acc.clientTypeList({ percentage: true }).then((res) => {
        this.$set(this.chartFour.series[0], 'data', res.data)
      })
    },
    getScanCodeRanking() {
      acc.scanCodeRankingList().then((res) => {
        this.scanCodeRankingList = res.data
      })
    },
    getProductRanking() {
      acc.productRankingList().then((res) => {
        this.productRankingList = res.data
      })
    }
  }

}
</script>

<style scoped lang="scss">
.statistic {
  overflow: auto;
  display: flex;

  .title {
    font-size: 14px;
    font-weight: bold;
  }

  ::v-deep .el-button {
    border-radius: 0;
  }

  ::v-deep .chart-box {
    margin-top: 0 !important;
    border-bottom: none !important;
  }

  .card_box {
    margin-bottom: 10px;
    flex: 1;
    margin-right: 10px;
  }

  .empty {
    text-align: center;
    width: 100px;
    margin-left: calc(50% - 50px);
    padding-top: 50px;
    overflow: hidden;
  }

  .statistic_left {
    flex: 1;
    width: 0;
  }

  .statistic_right {
    width: 33.3%;

    .title-count-card {
      ::v-deep .el-card__body {
        padding: 12px 20px !important;
      }
    }

    .title-count-box {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #dedede;
      padding: 2px 0;

      .icon {
        flex-shrink: 0;
        width: 16.5px;
        height: 27.7px;
        padding-top: 13px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        text-align: center;
        color: #FFFFFF;
        vertical-align: bottom;
      }

      .iconLast {
        width: 18.5px;
        height: 18.5px;
        border-radius: 50%;
        flex-shrink: 0;
        color: #FFFFFF;
        background-color: #b5b5b5;
        padding-top: 3px;
        text-align: center;
      }

      img {
        width: 40px;
        margin: 0 10px
      }

      .text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        min-width: 100px;
        p {
          margin: 8px;
          font-size: 14px;
          color: #666;
        }
      }

      .count {
        flex-shrink: 0;
        width: 30px;
        font-size: 16px;
        color: orange;
      }
    }

    .title-count-box:last-child {
      border-bottom: 0px
    }

    .product-sort-box {
      display: flex;
      align-items: center;
      color: #666;

      .icon {
        width: 18.5px;
        height: 18.5px;
        border-radius: 50%;
        color: #FFFFFF;
        padding-top: 3px;
        text-align: center;
      }

      .text {
        flex: 1;
        font-size: 14px;
        margin: 4px 10px;
      }

      .count {
        font-size: 16px;
      }

      ::v-deep .el-card__body {
        padding: 0 20px;
      }
    }
  }
}
</style>

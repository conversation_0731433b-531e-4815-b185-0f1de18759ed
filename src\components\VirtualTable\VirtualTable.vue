<template>
  <div class="table-container">
    <div v-if="enableSearch" class="table-search">
      <span class="search-input">
        <el-input
          ref="tableSearch"
          v-model="tableSearchValue"
          :placeholder="$t('common.enter')+$t('common.keyword')"
          :suffix-icon="'el-icon-search'"
          clearable
        />
      </span>
      <span class="search-slot"><slot name="search" /></span>
    </div>
    <vxe-table
      ref="vxeTable"
      size="small"
      border="inner"
      :height="!autoHeight ? `${height ? height: (tableHeight + 'px')}` : void 0"
      :data="filterTableData"
      show-overflow="tooltip"
      :merge-cells="mergeCells"
      :row-config="{height: rowHeight}"
      v-bind="$attrs"
    >
      <template v-for="item in columns">
        <vxe-column :key="item.field || item.type" v-bind="{...item}" :field="item.field">
          <template v-if="item.type === 'expand'" v-slot:content="scope">
            <div class="vxe-table-expand__cell">
              <slot
                name="content"
                :$index="scope._rowIndex"
                :row="scope.row"
                :scope="scope"
              />
            </div>
          </template>
          <template slot-scope="scope">
            <slot
              v-if="item.slotName"
              :name="item.slotName"
              :$index="scope._rowIndex"
              :row="scope.row"
              :scope="scope"
            />
            <span v-else-if="item.type === 'seq'">{{ scope.seq }}</span>
            <template v-else-if="scope.column.field === imgProp.name">
              <img-link
                :html-text="showData(scope.row[scope.column.field])"
                :url-list="scope.row[imgProp.urlList]"
              />
            </template>
            <span v-else-if="item.format" v-html="showData(item.format(scope.row, scope.row[item.field]))" />
            <span v-else v-html="showData(scope.row[item.field])" />
          </template>
        </vxe-column>
      </template>
    </vxe-table>
  </div>
</template>

<script>
// 打印样式
const printStyle = `
        .title-qrcode-container{
          display: flex;
          justify-content: space-between;
        }
        .qrcode-image {
          width: 100px;
          height: 100px
        }
        .title {
          text-align: center;
        }
        .print-list-row {
          display: inline-block;
          width: 100%;
        }
        .print-list-col {
          float: left;
          min-width: 33.33%;
          height: 28px;
          line-height: 28px;
        }
        .print-top,
        .print-bottom {
          font-size: 12px;
        }
        .print-top {
          margin-bottom: 5px;
        }
        .print-bottom {
          margin-top: 30px;
          text-align: right;
        }
        .print-bottom .print-list-col {
          float: right
        }
        `

import ImgLink from '@/components/ImgLink'
import { debounce } from 'lodash'
import QrCode from 'qrcode'

export default {
  name: 'VirtualTable',
  components: { ImgLink },
  props: {
    printStyle: {
      type: String,
      default: ''
    },
    printFields: {
      type: Array,
      default: () => { return [] }
    },
    qrCodeText: {
      type: String,
      default: null
    },
    tableData: {
      type: Array,
      default() {
        return []
      }
    },
    columns: {
      type: Array,
      default() {
        return []
      }
    },
    height: {
      type: String,
      default: null
    },
    rowHeight: {
      type: [String, Number],
      default: 48
    },
    autoHeight: {
      type: Boolean,
      default: false
    },
    // [field1, field2]
    autoSpan: {
      type: [Array],
      default() { return [] }
    },
    printConfig: {
      type: Object,
      default() {
        return {
          title: '',
          topContent: [],
          bottomContent: []
        }
      }
    },
    imgProp: {
      type: Object,
      default() {
        return {
          name: 'materialName',
          urlList: 'previewSrcList'
        }
      }
    },
    enableSearch: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      src: null,
      tableHeight: 500,
      tableSearchValue: null,
      debounceResize: debounce(this.handleResize, 200)
    }
  },
  computed: {
    mergeCells() {
      const _mergeCellsObj = {}
      // 一般业务只需要合并行
      if (this.autoSpan && this.autoSpan.length > 0) {
        // 上一行的数据
        let _preRow

        // 记录连续行的第一个index
        const _consIndex = {}
        this.autoSpan.forEach(field => {
          _consIndex[field] = 0
        })
        this.tableData.forEach((row, index) => {
          if (index !== 0) {
            this.autoSpan.forEach(field => {
              if (row[field] === _preRow[field]) {
                if (_mergeCellsObj[_consIndex[field]] && _mergeCellsObj[_consIndex[field]][field] !== undefined) {
                  _mergeCellsObj[_consIndex[field]][field]++
                } else {
                  _mergeCellsObj[_consIndex[field]] = Object.assign({}, _mergeCellsObj[_consIndex[field]], { [field]: 2 })
                }
              } else {
                _consIndex[field] = index
              }
            })
          }
          _preRow = row
        })
      } else {
        return []
      }

      const fieldColumnIndex = {}
      this.columns.forEach((column, index) => {
        column.field && (fieldColumnIndex[column.field] = index)
      })
      const _mergeCells = []
      Object.keys(_mergeCellsObj).forEach(key => {
        Object.keys(_mergeCellsObj[key]).forEach(field => {
          _mergeCells.push({
            row: parseInt(key),
            col: fieldColumnIndex[field],
            rowspan: _mergeCellsObj[key][field],
            colspan: 1
          })
        })
      })
      return _mergeCells
    },
    filterTableData() {
      if (this.tableSearchValue && this.tableSearchValue.trim() !== '') {
        return this.tableData.filter((row) => {
          return this.columns.some(column => {
            if (!row[column.field]) return false
            if (column.formatter && typeof column.formatter === 'function') {
              return column.formatter({ cellValue: row[column.field], row: row, column }).toString().includes(this.tableSearchValue.trim())
            }
            return row[column.field].toString().includes(this.tableSearchValue.trim())
          })
        })
      } else {
        return this.tableData
      }
    }
  },
  watch: {
    qrCodeText: {
      handler(val) {
        if (val) {
          QrCode.toDataURL(val).then(url => {
            this.src = url
          })
        }
      },
      immediate: true
    }
  },
  created() {
  },
  mounted() {
    this.handleResize()
    window.addEventListener('resize', this.debounceResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.debounceResize)
  },
  methods: {
    handleResize() {
      this.$nextTick(() => {
        if (!this.height && !this.autoHeight) {
          this.resizeTableHeight()
        }
      })
    },
    print() {
      const columns = this.printFields.length ? this.columns.filter(item => this.printFields.indexOf(item.field || item.type) !== -1) : this.columns
      this.$refs.vxeTable.print({
        columns: columns,
        style: printStyle + this.printStyle,
        beforePrintMethod: ({ content }) => {
          // 拦截打印之前，返回自定义的 html 内容
          return this.renderTopHtml() + content + this.renderBottomHtml()
        }
      })
    },
    updateStatus(slotParams) {
      this.$refs.vxeTable.updateStatus(slotParams)
    },
    // 如果有emptyMsg  代表列表不能为空
    validate(cb, emptyMsg) {
      this.tableSearchValue = null
      this.$nextTick(() => {
        const $table = this.$refs.vxeTable
        if (emptyMsg && this.tableData.length === 0) {
          this.$message.warning(emptyMsg)
          cb(false)
          return
        }
        $table.validate(true).then(
          // 有errInfo校验失败 否则校验通过
          (errInfo) => {
            cb(!errInfo)
          }
        )
      })
    },
    getCheckboxRecords() {
      return this.$refs.vxeTable.getCheckboxRecords()
    },
    renderTopHtml() {
      return ` <div class="title-qrcode-container">
          <div></div>
          <h1 class="title">${this.printConfig.title}</h1>
          ${this.src ? `<img class="qrcode-image" src=${this.src} />` : `<div></div>`}
        </div>
        <div class="print-top">
          <div class="print-list-row">
          ${(this.printConfig.topContent || []).map(row => { return `<div class="print-list-col">${row.label}：${row.value}</div>` }).join('')}
          </div>
        </div>
      `
    },
    renderBottomHtml() {
      return `<div class="print-bottom">
          <div class="print-list-row">
          ${(this.printConfig.bottomContent || []).reverse().map(row => { return `<div class="print-list-col">${row.label}：${row.value}</div>` }).join('')}
          </div>
        </div>`
    },
    resizeTableHeight() {
      this.$nextTick(() => {
        const searchContainer = document.getElementsByClassName('search-container')
        const transferSelect = document.getElementsByClassName('transfer-select')
        let searchContainerHeight = 0
        if (transferSelect?.length > 0) searchContainerHeight = transferSelect[0].offsetHeight
        else if (searchContainer?.length > 0) searchContainerHeight = searchContainer[0].offsetHeight

        const formContainer = document.getElementsByClassName('form-container')
        let formContainerHeight = 0
        if (formContainer?.length > 0) formContainerHeight = formContainer[0].offsetHeight
        const tableSearch = document.getElementsByClassName('table-search')
        let tableSearchHeight = 0
        if (tableSearch?.length > 0) tableSearchHeight = tableSearch[0].offsetHeight
        const elMainTable = document.getElementsByClassName('el-main-table')
        // debugger
        let elMainTableHeight = 0
        if (elMainTable?.length > 0) elMainTableHeight = 40
        const appInnerContainer = document.getElementsByClassName('app-inner-container')
        let appInnerContainerHeight = 0
        if (appInnerContainer?.length > 0) {
          for (const value of appInnerContainer) {
            if (value) {
              appInnerContainerHeight += value.offsetHeight
            }
          }
        }
        const fixHeight = 29

        this.tableHeight = document.getElementsByClassName('app-container')[0].offsetHeight - searchContainerHeight - formContainerHeight - tableSearchHeight - appInnerContainerHeight - fixHeight - elMainTableHeight
      })
    },
    showData(val) {
      if (this.tableSearchValue && val && val.toString().includes(this.tableSearchValue.trim())) {
        return val.toString().replace(this.tableSearchValue.trim(), `<font class="search-color">${this.tableSearchValue.trim()}</font>`)
      } else {
        return val
      }
    },
    clearValidate() {
      this.$refs.vxeTable.clearValidate()
    }
  }
}
</script>

<style scoped lang="scss">
.table-search {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.vxe-table-expand__cell {
  padding: 20px 50px;
}
</style>

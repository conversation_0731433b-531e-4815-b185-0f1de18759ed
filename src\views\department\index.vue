<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="(data) => {searchHelper.search(data)}"
      @return-reset="searchHelper.reset"
    />
    <!-- 表格 -->
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="() => {searchHelper.handleQuery()}"
    />
    <simple-data-dialog
      v-if="dialogVisible"
      :title="['新增','编辑'][dialogType]"
      :visible="true"
      size="middle"
    >
      <el-form
        ref="detailForm"
        label-position="top"
        :model="detailForm"
        :rules="detailFormRules"
        :inline="true"
      >
        <el-form-item
          prop="deptCode"
          label="部门编码："
          class="el-form-item-width"
        >
          <el-input v-model="detailForm.deptCode" :disabled="dialogType===1" clearable :maxlength="4" placeholder="请输入部门编码" />
        </el-form-item>
        <el-form-item
          prop="deptName"
          label="部门名称："
          class="el-form-item-width"
        >
          <el-input v-model="detailForm.deptName" clearable :maxlength="50" placeholder="请输入部门名称" />
        </el-form-item>
        <!--        <el-form-item-->
        <!--          prop="status"-->
        <!--          label="状态："-->
        <!--          class="el-form-item-width"-->
        <!--        >-->
        <!--          <el-select-->
        <!--            v-model="detailForm.status"-->
        <!--            filterable-->
        <!--            clearable-->
        <!--            :placeholder="dialogType===2?'':'请选择状态'"-->
        <!--          >-->
        <!--            <el-option label="启用" value="0" />-->
        <!--            <el-option label="停用" value="1" />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
      </el-form>
      <el-footer class="button-container">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="saveDetail(false)">保存</el-button>
      </el-footer>
    </simple-data-dialog>
  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import acc from '@/api/acc/acc'
import SimpleDataDialog from '@/components/SimpleDataDialog'

export default {
  name: 'DictMaintenanceDetail',
  components: {
    DataSelect,
    SimpleDataDialog,
    DataTable
  },
  data() {
    return {
      searchHelper: new this.$searchHelper({ api: acc.departmentListApi }),
      dialogVisible: false,
      dialogType: 0,
      detailForm: {},
      detailFormRules: {
        deptName: [
          { required: true, message: '请输入部门名称', trigger: 'blur' }
        ],
        deptCode: [
          { required: true, message: '请输入部门编码', trigger: 'blur' },
          {
            pattern: /^([0-9]{4})$/,
            message: `请输入4位数值！`,
            trigger: `blur`
          }
        ]
      },
      search: {
        deptCode: {
          label: '部门编码',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入部门编码'
          }
        },
        deptName: {
          label: '部门名称',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入部门名称'
          }
        }
      },
      buttonData: [
        {
          label: '新增',
          action: this.onAddClick,
          permission: 'department:add'
        }
      ],
      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            sortable: false
          },
          {
            label: '部门编码',
            prop: 'deptCode',
            sortable: false
          },
          {
            label: '部门名称',
            prop: 'deptName',
            sortable: false
          }
          // {
          //   label: '状态',
          //   prop: 'status',
          //   statusMapping: [{
          //     label: '启用',
          //     value: '0'
          //
          //   }, {
          //     label: '停用',
          //     value: '1'
          //
          //   }],
          //   type: 'tag',
          //   sortable: false
          // }
        ],
        // 操作
        operation: {
          label: '操作',
          width: '150px',
          data: [
            {
              label: '编辑',
              action: this.onEditClick,
              permission: 'department:edit'
            }, {
              label: '删除',
              action: this.onDelClick,
              permission: 'department:del'
            }
          ]
        }
      }
    }
  },
  mounted() {
    this.searchHelper.handleQuery()
  },
  methods: {
    // 投入明细表
    onAddClick() {
      this.detailForm = {}
      this.dialogType = 0
      this.dialogVisible = true
    },
    onEditClick(row) {
      this.dialogVisible = true
      this.dialogType = 1
      acc.detailDepartmentApi({ id: row.id }).then((res) => {
        this.detailForm = res.data
      })
    },
    onDelClick(row) {
      this.$confirm('请确认是否删除?', '警告').then(() => {
        acc.deleteDepartmentApi({ id: row.id }).then(() => {
          this.$message.success('操作成功!')
          this.searchHelper.handleQuery()
        })
      })
    },
    cancel() {
      this.detailForm = {}
      this.dialogVisible = false
    },
    saveDetail() {
      this.$refs.detailForm.validate((valid) => {
        if (valid) {
          if (this.dialogType === 0) {
            acc.addDepartmentApi({ ...this.detailForm, typeCode: this.typeCode }).then(() => {
              this.$message.success('操作成功!')
              this.searchHelper.handleQuery()
              this.cancel()
            })
          } else {
            acc.editDepartmentApi({ ...this.detailForm, typeCode: this.typeCode }).then(() => {
              this.$message.success('操作成功!')
              this.searchHelper.handleQuery()
              this.cancel()
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.button-container {
  line-height: 60px;
  text-align: center;
}
</style>

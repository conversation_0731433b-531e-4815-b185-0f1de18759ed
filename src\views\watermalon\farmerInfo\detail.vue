<template>
  <div class="app-container">
    <el-container :style="{ height: '100%'}">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item title="基本信息" :name="1">
            <el-form
              ref="basic"
              label-position="top"
              :model="basicFormModel"
              :rules="basicFormRules"
              :inline="true"
            >
              <el-form-item
                prop="isCompany"
                label="类型："
                class="el-form-item-width"
              >
                <el-select
                  v-model="basicFormModel.isCompany"
                  placeholder="请选择类型"
                  @change="changeType"
                >
                  <el-option label="公司" value="Y" />
                  <el-option label="瓜农" value="N" />
                </el-select>
              </el-form-item>
              <el-form-item
                prop="village"
                label="村别："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.village" placeholder="请输入村别" />
              </el-form-item>
              <el-form-item
                v-if="basicFormModel.isCompany==='Y'"
                prop="companyAddress"
                label="公司地址："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.companyAddress" placeholder="请输入公司地址" />
              </el-form-item>
              <el-form-item
                prop="user"
                label="姓名："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.user" placeholder="请输入姓名" />
              </el-form-item>

              <el-form-item
                prop="userCode"
                label="编码："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.userCode" placeholder="请输入编码" />
              </el-form-item>
              <el-form-item
                prop="tel"
                label="电话："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.tel" placeholder="请输入电话" />
              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="西瓜属性" :name="2">
            <virtual-table
              ref="attr-table"
              :enable-search="false"
              auto-height
              :columns="specColumns"
              :table-data="specTableData"
              :edit-rules="specRules"
            >
              <template v-slot:productNo="{row, $index, scope}">
                <el-select
                  v-model="row.productNo"
                  placeholder="请选择西瓜类型"
                  @change="$refs.table.updateStatus(scope)"
                >
                  <el-option v-for="item in categoryList" :key="item.value" v-bind="item" />
                </el-select>
              </template>
              <template v-slot:area="{row, scope}">
                <el-input
                  v-model="row.area"
                  placeholder="请输入面积/亩"
                  @change="$refs.table.updateStatus(scope)"
                />
              </template>

              <template v-slot:days="{row, scope}">
                <el-input
                  v-model="row.days"
                  placeholder="请输入历时/天"
                  @change="$refs.table.updateStatus(scope)"
                />
              </template>

              <template v-slot:operate="{$index}">
                <span class="text_button" @click="deleteSpec($index)">删除</span>
              </template>
            </virtual-table>
            <demo-block
              message="添加属性"
              :icon-class="'el-icon-plus icon-class'"
              @click.native="addSpec"
            />
          </el-collapse-item>
        </el-collapse>
      </el-main>
      <el-footer class="button-container">
        <el-button @click="back">返回</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </el-footer>
    </el-container>

  </div>
</template>
<script>

import DemoBlock from '@/components/DemoBlock'
import VirtualTable from '@/components/VirtualTable/VirtualTable'
import accApi from '@/api/acc/acc'

export default {
  name: 'FarmerDetail',
  components: {
    DemoBlock,
    VirtualTable
  },
  data() {
    return {
      action: null,
      categoryList: [],
      dialogVisible: false,
      collapse: [1, 2, 3],
      // 表头
      specColumns: [
        {
          type: 'seq',
          title: '序号'
        },
        {
          title: '西瓜类型',
          field: 'productNo',
          slotName: 'productNo'
        },
        {
          title: '面积/亩',
          field: 'area',
          slotName: 'area'
        },
        {
          title: '历时/天',
          field: 'days',
          slotName: 'days'
        },
        {
          title: '操作',
          field: 'operate',
          slotName: 'operate',
          width: '50px'
        }
      ],
      specRules: {
        productNo: [
          { required: true, message: '请输入西瓜类型' }
        ],
        area: [
          { required: true, message: '请输入面积/亩' }
        ],
        days: [
          { required: true, message: '请输入历时/天' }
        ]
      },
      specTableData: [],

      basicFormModel: {
        isCompany: '',
        village: '',
        user: '',
        userCode: '',
        tel: ''
      },
      basicFormRules: {
        userCode: [
          { required: true, message: '请输入用户编码' }
        ], village: [
          { required: true, message: '请输入村别' }
        ], user: [
          { required: true, message: '请输入姓名' }
        ], isCompany: [
          { required: true, message: '请选择类型' }
        ], tel: [
          { required: true, message: '请输入电弧' }
        ]
      }
    }
  },
  computed: {},
  mounted() {
    this.getCategoryList()
    this.action = this.$route.query.action
    if (this.action === 'edit') {
      this.queryProduct()
    }
  },
  methods: {
    changeType() {
      this.$set(this.basicFormModel, 'village', undefined)
      this.$set(this.basicFormModel, 'companyAddress', undefined)
    },
    queryProduct() {
      accApi.getFarmerInfo({ id: this.$route.query.id }).then(res => {
        Object.keys(this.basicFormModel).forEach(key => {
          this.basicFormModel[key] = res.data[key]
        })
        this.specTableData = res.data.farmerProductRelationList
      })
    },
    addSpec() {
      this.specTableData.push({
        productNo: null,
        area: null,
        days: null
      })
    },
    deleteSpec(index) {
      this.specTableData.splice(index, 1)
    },
    dataEditClick() {
      this.queryBeian()
      this.beianDialogVisible = true
    },
    beianAddClick() {
      this.dialogVisible = true
    },
    confirm(data) {
      accApi.addProductAttr({
        ...data,
        productId: this.$route.query.id,
        attrType: 30
      }).then(res => {
        this.$message.success(res.message)
        this.dialogVisible = false
        this.queryBeian()
      })
    },
    getCategoryList() {
      accApi.productListAll({ category: 99999 }).then(res => {
        this.categoryList = res.data.map(item => {
          return {
            label: item.productName,
            value: item.productNo
          }
        })
        console.log(this.categoryList, 1211)
      })
    },
    back() {
      this.$router.replace('farmerInfo')
    },
    submit() {
      this.$refs.basic.validate((valid) => {
        if (valid) {
          if (this.specTableData.length === 0) {
            this.$message.error('请至少添加一条西瓜属性')
            return false
          }
          // if(this.specTableData.map(e=>e.))
          this.$refs['attr-table'].validate(attrValid => {
            if (attrValid) {
              const api = this.action === 'edit' ? 'editFarmer' : 'addFarmer'
              accApi[api]({
                ...this.basicFormModel,
                id: this.$route.query.id,
                farmerProductRelationList: this.specTableData
              }).then(res => {
                this.$message.success(res.message)
                this.back()
              })
            }
          })
        }
      })
    }

  }
}
</script>

<style scoped lang="scss">
.el-select--small, .el-cascader--small, .el-input-number--small {
  width: 100%
}

.button-container {
  line-height: 60px;
  text-align: center;
}
</style>

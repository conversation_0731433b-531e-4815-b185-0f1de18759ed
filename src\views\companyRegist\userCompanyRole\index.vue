<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="searchAction"
      @return-reset="reset"
    />
    <!-- 表格 -->
    <data-table
      :table-index="tableIndex"
      :table-data="tableData"
      :column="column"
      :pagination.sync="pagination"
      @search-event="getListBySearch"
    />
    <!--新增编辑弹窗-->
    <data-dialog
      :dialog-visible.sync="dialogVisible"
      :dialog-data.sync="dialogData"
      :dialog-title="dialogTitle"
      :dialog-rule="dialogRule"
      :confirm-func="confirm"
    />
    <simple-data-dialog
      class="dialog-style"
      :visible="userDialog.dialogVisible"
      :size="userDialog.size"
    >
      <template slot="title">
        <span class="el-dialog__title">{{ $t('acc.role.setUserByRole') }}</span>
      </template>
      <data-select
        :search-data.sync="userDialog.search"
        @return-search="dialogSearchAction"
        @return-reset="dialogReset"
      />
      <data-table
        ref="userTable"
        :table-data="userDialog.tableData"
        :column="userDialog.column"
        :table-option="userDialog.tableOption"
        :pagination.sync="userDialog.pagination"
        @search-event="getUserListBySearch"
      />
      <template
        slot="footer"
        class="dialog_btn"
      >
        <el-button @click="userDialogClose">{{ $t('common.cancel') }}</el-button>
        <el-button
          type="primary"
          @click="saveUserRole"
        >{{ $t('common.ok') }}</el-button>
      </template>
    </simple-data-dialog>
    <simple-data-dialog
      v-if="funDialog.dialogVisible"
      :visible="funDialog.fakeDialogVisible"
      :size="funDialog.size"
    >
      <template slot="title">
        <span class="el-dialog__title">{{ $t('acc.role.setFunByRole') }}</span>
      </template>
      <div class="tree-container">
        <tree-index
          ref="tree"
          :props="funDialog.props"
          :node-key="funDialog.nodeKey"
          :tree-data="funDialog.treeData"
          :single-check="false"
          :check-strictly="true"
        />
      </div>
      <template
        slot="footer"
        class="dialog_btn"
      >
        <el-button @click="funDialogClose">{{ $t('common.cancel') }}</el-button>
        <el-button
          type="primary"
          @click="saveFunRole"
        >{{ $t('common.ok') }}</el-button>
      </template>
    </simple-data-dialog>
  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import DataDialog from '@/components/DataDialog'
import SimpleDataDialog from '@/components/SimpleDataDialog'
import accApi from '@/api/acc/acc'
import TreeIndex from '@/views/acc/sysMg/userRole/tree.vue'
import { isEmpty } from 'element-ui/src/utils/util'
export default {
  name: 'UserRole',
  components: {
    DataSelect,
    DataTable,
    DataDialog,
    SimpleDataDialog,
    TreeIndex
  },
  data() {
    // 新增/编辑校验
    this.dialogRule = {
      roleName: {
        required: true,
        validator: (rule, value, callback) => {
          if (isEmpty(value)) {
            return callback(new Error(this.$t('common.enter') + this.$t('acc.role.roleName')))
          } else {
            value = value.trim()
            const data = {
              guid: this.currentRoleGuid,
              roleName: value,
              paramCheck: true
            }
            accApi.checkRoleName(data).then(res => {
              if (res.code !== 'SUCCESS') {
                return callback(new Error(res.msg))
              } else {
                callback()
              }
            })
          }
        },
        trigger: 'blur'
      }
    }
    return {
      tableIndex: 10,
      // 弹窗参数
      dialogVisible: false,
      dialogTitle: '',
      dialogRule: {
        roleName: [
          { required: true, message: '请输入角色名称', trigger: 'blur' }
        ]
      },
      dialogData: { },
      initDialogData: {
        guid: {
          type: 'input',
          value: null,
          show: false
        },
        roleCode: {
          label: this.$t('acc.role.roleCode'),
          type: 'input',
          value: null,
          option: {
            placeholder: this.$t('acc.role.codeTip'),
            maxlength: 50,
            disabled: true
          }
        },
        roleName: {
          label: this.$t('acc.role.roleName'),
          type: 'input',
          value: null,
          option: {
            placeholder: this.$t('common.enter') + this.$t('acc.role.roleName'),
            maxlength: 50
          }
        }
      },
      // 搜索框参数
      search: {
        roleCode: {
          label: this.$t('acc.role.roleCode'),
          value: null, type: 'input',
          option: {
            placeholder: this.$t('common.enter') + this.$t('acc.role.roleCode')
          }
        },
        roleName: {
          label: this.$t('acc.role.roleName'),
          value: null, type: 'input',
          option: {
            placeholder: this.$t('common.enter') + this.$t('acc.role.roleName')
          }
        }
      },
      // 表格参数
      // 表格上方操作按钮，比如新增
      buttonData: [
        {
          label: this.$t('common.add'),
          action: this.add,
          permission: 'all'
        }
      ],
      column: {
        // 表头
        data: [
          {
            label: this.$t('acc.role.roleCode'),
            prop: 'roleCode',
            show: true,
            sortable: false
          },
          {
            label: this.$t('acc.role.roleName'),
            prop: 'roleName',
            show: true,
            sortable: false
          }
        ],
        // 表格中的操作列
        operation: {
          width: 350,
          label: this.$t('common.operation'),
          data: [
            {
              label: this.$t('acc.role.setFunByRole'),
              action: this.setFun,
              permission: 'all'
            },
            // {
            //   label: this.$t('acc.role.setUserByRole'),
            //   action: this.setUser,
            //   permission: 'all'
            // },
            {
              label: this.$t('common.edit'),
              action: this.edit,
              permission: 'all'
            },
            {
              label: this.$t('common.delete'),
              action: this.del,
              permission: 'all'
            }
          ]
        }
      },
      // 分页
      pagination: {
        pageNum: 1,
        roleType: 3,
        pageSize: this.$constant.initPaginationOption.pageSize,
        total: 0
      },
      searchData: {},
      tableData: [],
      // 增编辑时记录当前记录的Guid
      currentRoleGuid: null,
      // 设置用户时记录当前记录的RoleCode
      currentRoleCode: null,
      userDialog: {
        dialogVisible: false,
        size: 'middle',
        search: {
          emplCode: {
            label: this.$t('acc.role.empCode'),
            value: null, type: 'input',
            option: {
              placeholder: this.$t('common.enter') + this.$t('acc.role.empCode')
            }
          },
          userName: {
            label: this.$t('acc.role.userName'),
            value: null, type: 'input',
            option: {
              placeholder: this.$t('common.enter') + this.$t('acc.role.userName')
            }
          }
        },
        tableData: [],
        searchData: {},
        column: {
          // 表头
          data: [
            {
              label: this.$t('acc.role.empCode'),
              prop: 'emplCode',
              show: true,
              sortable: false
            },
            {
              label: this.$t('acc.role.userName'),
              prop: 'userName',
              show: true,
              sortable: false
            }
          ]
        },
        tableOption: {
          option: {
            enableSelected: true,
            cellClickSelected: false
          },
          event: {
            select: this.selectionChange,
            selectAll: this.selectionChange
          }
        },
        // 分页
        pagination: {
          pageNum: 1,
          pageSize: this.$constant.initPaginationOption.pageSize,
          total: 0
        },
        // 用于记录用户勾选的复选框
        selection: []
      },
      funDialog: {
        dialogVisible: false,
        fakeDialogVisible: true,
        size: 'small',
        treeData: [],
        nodeKey: 'menuCode',
        props: {
          label: 'menuName',
          children: 'menuInfoVOList'
        }
      }
    }
  },
  mounted() {
    //  初始化弹窗参数，获取单位列表
    this.dialogData = this.$util.cloneDeep(this.initDialogData)
    this.getListBySearch()
  },
  methods: {
    // 新增货品单位
    add() {
      this.currentRoleGuid = null
      Object.keys(this.dialogData).forEach((key) => {
        this.dialogData[key].value = null
      })
      this.dialogTitle = this.$t('common.add')
      this.dialogVisible = true
    },
    // 修改货品单位
    edit(data) {
      this.currentRoleGuid = data.guid
      const that = this
      Object.keys(that.dialogData).forEach((key) => {
        that.dialogData[key].value = data[key]
      })
      this.dialogTitle = this.$t('common.edit')
      this.dialogVisible = true
    },
    // 删除货品单位
    del(data) {
      this.$confirm(this.$t('common.confirmDel'), this.$t('common.prompt'), {
        confirmButtonText: this.$t('common.ok'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      }).then(() => {
        accApi.deleteRole(data.guid).then(res => {
          // eslint-disable-next-line eqeqeq
          if (res.code == 'SUCCESS') {
            this.$message({
              type: 'success',
              message: res.msg
            })
            this.getListBySearch()
          } else {
            this.$message({
              type: 'error',
              message: res.msg
            })
          }
        })
      })
    },
    // 设置功能
    setFun(data) {
      this.currentRoleCode = data.roleCode
      this.getFunTreeData()
    },
    // 设置用户
    setUser(data) {
      this.userDialog.pagination.pageNum = 1
      this.userDialog.searchData = {}
      this.currentRoleCode = data.roleCode
      this.getSelected()
    },
    // 获取列表数据
    getListBySearch() {
      const prarm = Object.assign(this.searchData, this.pagination)
      accApi.getRoleList(prarm).then((res) => {
        this.tableData = res.data.list
        this.pagination = res.data.pagination
      })
    },
    // 弹出框  新增/修改保存确定触发的事件
    confirm(data) {
      // eslint-disable-next-line eqeqeq
      if (this.dialogTitle == this.$t('common.add')) {
        accApi.addRole({ ...data, roleType: 3 }).then(res => {
          // eslint-disable-next-line eqeqeq
          if (res.code == 'SUCCESS') {
            this.$message({
              type: 'success',
              message: res.msg
            })
            this.dialogVisible = false
            this.getListBySearch()
          } else {
            this.$message({
              type: 'error',
              message: res.msg
            })
          }
        })
        // eslint-disable-next-line eqeqeq
      } else if (this.dialogTitle == this.$t('common.edit')) {
        accApi.editRole(data).then(res => {
          this.$message.success(res.msg)
          this.dialogVisible = false
          this.getListBySearch()
        })
      }
    },
    // 重置
    reset() {
      this.pagination.pageNum = 1
      this.pagination.pageSize = 20
      this.searchData = {}
      this.getListBySearch()
    },
    searchAction(data) {
      this.pagination.pageNum = 1
      this.searchData = data
      this.getListBySearch()
    },
    dialogSearchAction(data) {
      this.userDialog.pagination.pageNum = 1
      this.userDialog.searchData = data
      this.getUserListBySearch()
    },
    dialogReset() {
      this.userDialog.pagination.pageNum = 1
      this.userDialog.pagination.pageSize = 20
      this.userDialog.searchData = {}
      this.getUserListBySearch()
    },
    // 获取用户并反选
    getUserListBySearch() {
      const prarm = Object.assign(this.userDialog.searchData, this.userDialog.pagination, { accountType: 2 })
      accApi.queryUserInfoForRole(prarm).then((res) => {
        this.userDialog.dialogVisible = true
        this.userDialog.tableData = res.data.list
        this.userDialog.pagination = res.data.pagination
        this.$nextTick(() => {
          this.showSelect()
        })
      })
    },
    getFunTreeData() {
      accApi.queryMenuInfoWithRole({ roleCode: this.currentRoleCode, relationType: 3 }).then((selectRes) => {
        accApi.queryMenuInfos().then((res) => {
          this.funDialog.treeData = res.data
          this.funDialog.dialogVisible = true
          const selection = []
          selectRes.data.forEach((item) => {
            selection.push(item.menuCode)
          })
          this.$nextTick(() => {
            this.showFunSelected(selection)
          })
        })
      })
    },
    saveUserRole() {
      const userCodeList = this.userDialog.selection
      const param = { roleCode: this.currentRoleCode, userCodeList: userCodeList, roleType: 3 }
      accApi.saveUserRole(param).then((res) => {
        // eslint-disable-next-line eqeqeq
        if (res.code == 'SUCCESS') {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.userDialogClose()
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          })
        }
      })
    },
    // 勾选后台数据中已经默认被勾选的下拉框
    showSelect() {
      // 根据条件进行页面checkbox回显
      const rows = this.userDialog.tableData
      const selection = this.userDialog.selection
      const items = rows.filter((row) => {
        return selection.some((val) => {
          // eslint-disable-next-line
          return row.emplCode == val
        })
        // for (let i = 0; i < selection.length; i++) {
        //   // eslint-disable-next-line
        //   if (row.emplCode == selection[i]){
        //     return true
        //   }
        // }
        // return false
      })
      this.$nextTick(() => {
        this.$refs.userTable.toggleRowSelection(items)
      })
    },
    // 回显角色功能复选框
    showFunSelected(selection) {
      this.$nextTick(() => {
        const parentNodes = this.getParentNodes(selection)
        if (parentNodes.length > 0) {
          selection = selection.concat(parentNodes)
        }
        this.$refs.tree.$refs.treeForm.setCheckedKeys(selection)
        this.$refs.tree.initHalfTree(this.funDialog.treeData)
      })
    },
    // 通过是否有row值判断是  全选还是单独勾选
    selectionChange(selection, row) {
      if (row) {
        // 判断当前勾选或取消的行在已选择的列表中是否存在 已经存在那么删除  不存在添加(length为0或没找到)
        for (let i = 0; i < this.userDialog.selection.length; i++) {
          // eslint-disable-next-line
          if (row.emplCode == this.userDialog.selection[i]) {
            this.userDialog.selection.splice(i, 1)
            return
          }
        }
        this.userDialog.selection.push(row.emplCode)
      } else {
        const that = this
        selection.forEach((item) => {
          let isExist = false
          for (let i = 0; i < that.userDialog.selection.length; i++) {
            // eslint-disable-next-line
            if (item.emplCode == that.userDialog.selection[i]) {
              that.userDialog.selection.splice(i, 1)
              isExist = true
              break
            }
          }
          if (!isExist) {
            that.userDialog.selection.push(item.emplCode)
          }
        })
      }
    },
    getSelected() {
      accApi.queryUserInfoWithRole(this.currentRoleCode).then((res) => {
        this.userDialog.selection = []
        res.data.forEach((item) => {
          this.userDialog.selection.push(item.code)
        })
        this.getUserListBySearch()
      })
    },
    // 保存角色菜单
    saveFunRole() {
      const checked = this.$refs.tree.$refs.treeForm.getCheckedNodes()
      checked.forEach((item) => {
        item.roleCode = this.currentRoleCode
      })
      const param = { roleCode: this.currentRoleCode, roleMenuInfoVOList: checked, relationType: 3 }
      accApi.saveRoleMenu(param).then(res => {
        // eslint-disable-next-line eqeqeq
        if (res.code == 'SUCCESS') {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.funDialog.dialogVisible = false
        } else {
          this.$message({
            type: 'error',
            message: res.msg
          })
        }
      })
    },
    funDialogClose() {
      this.funDialog.dialogVisible = false
    },
    userDialogClose() {
      this.userDialog.dialogVisible = false
      this.$refs.userTable.clearSelection()
      this.userDialog.selection = []
      this.userDialog.searchData = {}
      this.clearUserDialogSearch()
    },
    clearUserDialogSearch() {
      this.userDialog.search.emplCode.value = null
      this.userDialog.search.userName.value = null
      this.$set(this.userDialog, 'search', this.userDialog.search)
    },
    getParentNodes(selection) {
      const parentNodes = []
      for (let i = 0; i < selection.length; i++) {
        const parentNode = this.$refs.tree.$refs.treeForm.getNode(selection[i])?.parent
        if (parentNode && parentNode.data.menuCode && !selection.includes(parentNode.data.menuCode)) {
          parentNodes.push(parentNode.data.menuCode)
          selection.push(parentNode.data.menuCode)
        }
      }
      return parentNodes
    }
  }
}
</script>

<style lang="scss">
  .dialog-style .el-table{
    height: calc(100vh - 340px) !important;
  }
  .tree-container{
    padding-right: 20px;
  }
</style>

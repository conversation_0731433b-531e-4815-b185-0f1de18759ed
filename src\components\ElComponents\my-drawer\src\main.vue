<template>
  <div
    v-if="visible"
    ref="drawer"
    aria-modal="true"
    aria-labelledby="el-drawer__title"
    :aria-label="title"
    class="el-drawer"
    :class="[direction, customClass]"
    role="dialog"
    :style="`bottom: ${bottom};maxHeight:100%`"
    tabindex="-1"
  >
    <header v-if="withHeader" id="el-drawer__title" class="el-drawer__header" style="padding: 10px 20px;margin-bottom: 0;">
      <slot name="title">
        <span role="heading" :title="title">{{ title }}</span>
      </slot>
      <button
        :aria-label="`up ${title || 'drawer'}`"
        class="el-drawer__close-btn"
        style="margin-right: 10px"
        type="button"
        @click="setInnerDrawer"
      >
        <i class="el-icon-arrow-down el-icon" :class="{'up':innerVisible,'down':!innerVisible}" />
      </button>
      <button
        v-if="showClose"
        :aria-label="`close ${title || 'drawer'}`"
        class="el-drawer__close-btn"
        type="button"
        @click="closeDrawer"
      >
        <i class="el-dialog__close el-icon el-icon-close" />
      </button>
    </header>

    <section
      v-if="rendered"
      id="myDrawerSection"
      style="width: 100%;max-height:100%;overflow:auto"
      class="my-drawer el-drawer__body"
    >
      <slot />
    </section>
  </div>
</template>

<script>
import Popup from 'element-ui/src/utils/popup'
import emitter from 'element-ui/src/mixins/emitter'
import VueEvent from '@/utils/vue-event'
export default {
  name: 'ElMyDrawer',
  mixins: [Popup, emitter],
  props: {
    appendToBody: {
      type: Boolean,
      default: false
    },
    beforeClose: {
      type: Function,
      default: null
    },
    customClass: {
      type: String,
      default: ''
    },
    closeOnPressEscape: {
      type: Boolean,
      default: true
    },
    destroyOnClose: {
      type: Boolean,
      default: false
    },
    modal: {
      type: Boolean,
      default: true
    },
    direction: {
      type: String,
      default: 'rtl',
      validator(val) {
        return ['ltr', 'rtl', 'ttb', 'btt'].indexOf(val) !== -1
      }
    },
    modalAppendToBody: {
      type: Boolean,
      default: true
    },
    showClose: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean
    },
    wrapperClosable: {
      type: Boolean,
      default: true
    },
    withHeader: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      closed: false,
      innerVisible: true,
      prevActiveElement: null
    }
  },
  computed: {
    bottom() {
      const elm = document.getElementById('myDrawerSection')
      if (!this.innerVisible && elm) {
        return `-${elm.offsetHeight}px`
      }
      return '0px'
    }
  },
  watch: {
    visible: {
      handler(val) {
        // debugger
        if (val) {
          this.closed = false
          this.$emit('open')
          if (this.appendToBody) {
            document.body.appendChild(this.$el)
          }
          this.prevActiveElement = document.activeElement
        } else {
          if (!this.closed) this.$emit('close')
          this.$nextTick(() => {
            if (this.prevActiveElement) {
              this.prevActiveElement.focus()
            }
          })
        }
      }
    }
  },
  mounted() {
    VueEvent.$on('refreshInnerVisible', this.refreshInnerVisible)
    if (this.visible) {
      this.rendered = true
      this.open()
    }
  },
  beforeDestroy() {
    VueEvent.$off('refreshInnerVisible')
  },
  destroyed() {
    // if appendToBody is true, remove DOM node after destroy
    if (this.appendToBody && this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el)
    }
  },
  methods: {
    setInnerDrawer() {
      this.innerVisible = !this.innerVisible
    },
    afterEnter() {
      this.$emit('opened')
    },
    afterLeave() {
      this.$emit('closed')
    },
    hide(cancel) {
      if (cancel !== false) {
        this.$emit('update:visible', false)
        this.$emit('close')
        if (this.destroyOnClose === true) {
          this.rendered = false
        }
        this.closed = true
      }
    },
    handleWrapperClick() {
      if (this.wrapperClosable) {
        this.closeDrawer()
      }
    },
    closeDrawer() {
      if (typeof this.beforeClose === 'function') {
        this.beforeClose(this.hide)
      } else {
        this.hide()
      }
    },
    handleClose() {
      // This method here will be called by PopupManger, when the `closeOnPressEscape` was set to true
      // pressing `ESC` will call this method, and also close the drawer.
      // This method also calls `beforeClose` if there was one.
      this.closeDrawer()
    },
    refreshInnerVisible() {
      this.innerVisible = true
    }
  }
}
</script>

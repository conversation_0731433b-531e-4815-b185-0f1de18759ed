/* eslint-disable vue/require-default-prop */
<!--  -->
<template>
  <div class="tree-index">
    <el-input
      v-model="treeInputValue"
      v-bind="initInputOption.option"
      :placeholder="$t('common.filiter')"
      suffix-icon="el-icon-search"
      :warp-class="'el-input-tree'"
    />
    <el-scrollbar
      ref="scrollbar"
      tag="div"
      :wrap-class="wrapClass"
    >
      <el-tree
        ref="treeForm"
        :data="treeData"
        :props="props"
        v-bind="$attrs"
        :node-key="nodeKey"
        :show-checkbox="showCheckBox"
        :check-strictly="checkStrictly"
        :default-expand-all="defaultExpandAll"
        :filter-node-method="filterNode"
        :check-on-click-node="checkOnClickNode"
        :expand-on-click-node="expandOnClickNode"
        v-on="$listeners"
        @check="handleChecked"
        @check-change="onTreeCheckChange"
      />
    </el-scrollbar>

  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import emitter from 'element-ui/src/mixins/emitter'

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  mixins: [emitter],
  props: {
    treeData: {
      type: Array,
      default: () => {
        return []
      }
    },
    defaultExpandAll: {
      type: Boolean,
      default: true
    },
    treeRow: {
      type: Array,
      default: () => {
        return []
      }
    },
    nodeKey: {
      type: String,
      default: () => {
        return null
      }
    },
    wrapClass: {
      type: String,
      default: () => {
        return 'max-height'
      }
    },
    props: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          children: 'children'
        }
      }
    },
    expandOnClickNode: {
      type: Boolean,
      default: true
    },
    checkOnClickNode: {
      type: Boolean,
      default: true
    },
    singleCheck: {
      type: Boolean,
      default: true
    },
    checkStrictly: {
      type: Boolean,
      default: true
    },
    filterNodeMethod: {
      type: Function,
      default: null
    }, showCheckBox: {
      type: Boolean,
      default: true
    }
  },
  data() {
    // 这里存放数据
    return {
      initInputOption: this.$constant.initInputOption,
      // 输入框的名字
      treeInputValue: '',
      // 树形选中的id
      checkedId: '',
      // 树形选中的数据
      selectTreeData: [],
      checkKeys: [],
      visibleKeys: []
    }
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {
    treeInputValue: {
      handler(val) {
        this.filterMethod(val)
      },
      deep: true
    },
    treeRow(val) {
      // debugger
      const keys = val ? val.map(value => value[this.nodeKey]) : []
      if (this.singleCheck) {
        this.selectTreeData = val
        this.$refs.treeForm.setCheckedKeys(keys)
      } else {
        this.checkKeys = keys
        this.filterMethod(this.treeInputValue)
      }
    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    if (!this.singleCheck) {
      this.visibleKeys = []
    }
    this.dispatch('DataSelect', 'el.dataSelect.addField', [this])
  },
  beforeCreate() {}, // 生命周期 - 创建之前
  beforeMount() {}, // 生命周期 - 挂载之前
  beforeUpdate() {}, // 生命周期 - 更新之前
  updated() {
  }, // 生命周期 - 更新之后
  beforeDestroy() {
    this.dispatch('DataSelect', 'el.dataSelect.removeField', [this])
  }, // 生命周期 - 销毁之前
  destroyed() {}, // 生命周期 - 销毁完成
  activated() {
  },
  // 方法集合
  methods: {
    // 树形结构点击
    handleNodeClickUp(data, checked, node) {
      if (checked === true) {
        this.selectTreeData = []
        this.checkedId = data.id
        this.$refs.treeForm.setCheckedKeys([data[this.nodeKey]])
        data.value = data[this.props.label]
        this.selectTreeData.push(data)
        this.$emit('treeClick', this.treeRow, this.selectTreeData)
        this.$emit('update:treeRow', this.selectTreeData)
        this.$emit('treeRowChange', this.selectTreeData)
      }
    },
    getCheckedKeys() {
      const tree = this.$refs.treeForm
      const checkKeys = tree.getCheckedKeys()
      this.visibleKeys.forEach(key => {
        const isVisible = checkKeys.includes(key)
        const i = this.checkKeys.indexOf(key)
        if (isVisible && i === -1) {
          this.checkKeys.push(key)
        }
        if (!isVisible && i !== -1) {
          this.checkKeys.splice(i, 1)
        }
      })
      if ((this.visibleKeys.length === 0 && this.checkKeys.length === 0) || this.treeInputValue === '') {
        this.checkKeys = checkKeys
      }
    },
    onTreeCheckChange(nodeData, checked, childChecked) {
      if (this.singleCheck) {
        this.handleNodeClickUp(nodeData, checked, childChecked)
      } else {
        const tree = this.$refs.treeForm
        const currentNode = tree.getNode(nodeData)
        if (currentNode) {
          if (currentNode.visible && currentNode.isLeaf && checked) {
            this.onTreeCheckChange(currentNode.parent.data, false, true)
          }
        }
        this.$nextTick(() => {
          if (currentNode) {
            if (childChecked && !currentNode.isLeaf) {
              let allCheck = true
              for (const childNode of currentNode.childNodes) {
                if (childNode.visible) {
                  if (!allCheck) {
                    // break
                  } else {
                    allCheck = childNode.checked
                  }
                }
              }
              if (allCheck) {
                if (!currentNode.checked) {
                  tree.setChecked(currentNode, true, false)
                }
                if (currentNode.parent) {
                  this.onTreeCheckChange(currentNode.parent.data, false, true)
                }
              }
            }
          }
        })
      }
    },
    // 树形结构点击
    handleChecked(data, { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys }) {
      this.selectTreeData = checkedNodes
      if (!this.singleCheck) {
        this.getCheckedKeys()

        const checkNodes = []
        this.checkKeys.forEach(key => {
          const node = this.$refs.treeForm.getNode(key)
          checkNodes.push(node.data)
        })
        this.selectTreeData = [...checkNodes]
        this.$emit('treeClick', this.treeRow, this.selectTreeData)
      }
      this.$emit('update:treeRow', this.selectTreeData)
      this.$emit('treeRowChange', this.selectTreeData)
    },
    setCheckNodes(keys) {
      const nodes = []
      keys.forEach(value => {
        const node = this.$refs.treeForm.getNode(value)
        if (!node) return
        const newNode = {}
        newNode[this.nodeKey] = value
        newNode[this.props.label] = node.label
        nodes.push(newNode)
      })
      this.$emit('update:treeRow', nodes)
      this.$emit('treeRowChange', nodes)
    },
    resetSearch() {
      this.treeInputValue = null
    },
    // 过滤
    filterNode(value, data) {
      if (!value) return true
      if (this.filterNodeMethod && typeof this.filterNodeMethod === 'function') {
        return this.filterNodeMethod(value, data)
      } else {
        return data[this.props.label].toUpperCase().indexOf(value.toUpperCase()) !== -1
      }
    },
    treeSelectRest() {
      this.selectTreeData = []
      this.treeRow.forEach((value, index, array) => { array[index].value = '' })
      this.$refs.treeForm.setCheckedKeys([])
    },
    toggleSelect(arr, flag) {
      arr.map(node => {
        this.$refs.treeForm.setChecked(node[this.nodeKey], flag)
      })

      this.$emit('update:treeRow', this.$refs.treeForm.getCheckedNodes())
      this.$emit('treeRowChange', this.$refs.treeForm.getCheckedNodes())
    },
    getCheckedNodes() {
      return this.$refs.treeForm.getCheckedNodes()
    },
    getHalfCheckedNodes() {
      return this.$refs.treeForm.getHalfCheckedNodes()
    },
    filterMethod(val) {
      const tree = this.$refs.treeForm
      tree.filter(val)
      this.$nextTick(() => {
        this.visibleKeys = this.getVisibleKeys()
        tree.setCheckedKeys([])
        this.checkKeys.forEach(key => {
          const node = tree.getNode(key)
          if (node && node.isLeaf) {
            tree.setChecked(key, true, true)
            this.onTreeCheckChange(node.parent.data, false, true)
          }
        })
      })
    },
    getVisibleKeys(leafOnly = false) {
      const visibleKeys = []
      this.$refs.treeForm.store._getAllNodes().forEach(node => {
        if (node.visible) {
          if (leafOnly && node && node.isLeaf) {
            visibleKeys.push(node.key)
          } else {
            visibleKeys.push(node.key)
          }
        }
      })
      return visibleKeys
    }
  } // 如果页面有keep-alive缓存功能，这个函数会触发
}
</script>
<style>
.el-input-tree{
  margin-bottom: 10px;
}
</style>

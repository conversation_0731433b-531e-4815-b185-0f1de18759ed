<template>
  <div class="app-container">
    <el-container :style="{ height: '100%'}">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item title="基本信息" :name="1">
            <el-form
              ref="basic"
              label-position="top"
              :model="basicFormModel"
              :rules="basicFormRules"
              :inline="true"
            >

              <el-form-item
                v-if="addType!==1"
                prop="productId"
                label="产品名称："
              >
                <el-select
                  v-model="basicFormModel.productId"
                  filterable
                  clearable
                  style="width: 100%"
                  placeholder="请选择产品名称"
                >
                  <el-option
                    v-for="item in productNameArr"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                v-if="addType!==1"
                prop="ruleName"
                label="规则名称："
              >
                <el-input v-model="basicFormModel.ruleName" clearable placeholder="" />
              </el-form-item>
              <el-form-item
                label="编码预览："
                class="el-form-item-width"
              >
                <div>{{ codePreview }}</div>
              </el-form-item>

            </el-form>
            <virtual-table ref="table" :enable-search="false" auto-height :columns="tagRuleColumns" :table-data="tagRuleTableData" :edit-rules="tagRuleRules">
              <template v-slot:itemType="{row, scope}">
                <el-select
                  v-model="row.itemType"
                  placeholder="请输入分类"
                  clearable
                  @change="$refs.table.updateStatus(scope)"
                >
                  <el-option
                    v-for="item in itemTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
              <template v-slot:keyEn="{row, scope}">
                <el-input
                  v-model="row.keyEn"
                  placeholder="请输入英文名称"
                  @change="$refs.table.updateStatus(scope)"
                />
              </template>
              <template v-slot:keyCn="{row, scope}">
                <el-input
                  v-model="row.keyCn"
                  placeholder="请输入中文名称"
                  @change="$refs.table.updateStatus(scope)"
                />
              </template>
              <template v-slot:itemDigit="{row, scope}">
                <el-input
                  v-model="row.itemDigit"
                  placeholder="请输入序号位位数"
                  @change="()=>{$refs.table.updateStatus(scope);setUuid(row)}"
                />
              </template>
              <template v-slot:defaultValue="{row, $index, scope}">

                <el-select
                  v-if="row.itemType===30"
                  v-model="row.defaultValue"
                  placeholder="请输入默认值"
                  clearable
                  @change="()=>{$refs.table.updateStatus(scope);row.itemDigit=row.defaultValue.length}"
                >
                  <el-option
                    v-for="item in timeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-input
                  v-else
                  v-model="row.defaultValue"
                  placeholder="请输入默认值"
                  @change="$refs.table.updateStatus(scope);setUuid(row)"
                />
              </template>
              <template v-slot:description="{row, $index, scope}">
                <el-input
                  v-model="row.description"
                  placeholder="请输入备注说明"
                  @change="$refs.table.updateStatus(scope)"
                />
              </template>
              <template v-slot:operate="{$index}">
                <span @click="deleteTagRule($index)">删除</span>
              </template>
            </virtual-table>
            <demo-block
              message="添加自定属性"
              :icon-class="'el-icon-plus icon-class'"
              @click.native="addTagRule"
            />
          </el-collapse-item>
        </el-collapse>
      </el-main>
      <el-footer class="button-container">
        <el-button @click="back">返回</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </el-footer>
    </el-container>

  </div>
</template>
<script>

import DemoBlock from '@/components/DemoBlock'
import VirtualTable from '@/components/VirtualTable/VirtualTable'
import dayjs from 'dayjs'
import accApi from '@/api/acc/acc'

export default {
  name: 'ProductDetail',
  components: {
    DemoBlock,
    VirtualTable
  },
  data() {
    const defaultValue = ({ cellValue, row }) => {
      if (cellValue) {
        if (cellValue.length > row.itemDigit) {
          return new Error(`默认值长度必须小于或者等于序号位位数`)
        }
      } else {
        return new Error(`请输入`)
      }
    }
    return {
      addType: '',
      itemTypeList: [{ label: '固定标识', value: 10 }, { label: '自定参数', value: 20 }, { label: '日期格式', value: 30 }, { label: '自增流水', value: 40 }],
      imgDialogVisible: false,
      collapse: [1],
      timeList: [
        { label: 'yyyy', value: 'yyyy' },
        { label: 'yyMMdd', value: 'yyMMdd' },
        { label: 'yyyyMMdd', value: 'yyyyMMdd' },
        { label: 'yyyy-MM-dd', value: 'yyyy-MM-dd' },
        { label: 'MMdd', value: 'MMdd' },
        { label: 'MM-dd', value: 'MM-dd' },
        { label: 'ddMM', value: 'ddMM' },
        { label: 'yyMM', value: 'yyMM' },
        { label: 'yyyyMM', value: 'yyyyMM' },
        { label: 'yyyy-MM', value: 'yyyy-MM' },
        { label: 'yy-MM-dd', value: 'yy-MM-dd' },
        { label: 'ddMMyy', value: 'ddMMyy' }
      ],
      // 表头
      tagRuleColumns: [
        {
          type: 'seq',
          width: '60px',
          title: '序号'
        },
        {
          title: '分类',
          field: 'itemType',
          slotName: 'itemType'
        },
        {
          title: '英文名称',
          field: 'keyEn',
          slotName: 'keyEn'
        }, {
          title: '中文名称',
          field: 'keyCn',
          slotName: 'keyCn'
        },
        {
          title: '序号位位数',
          field: 'itemDigit',
          slotName: 'itemDigit'
        },
        {
          title: '默认值',
          field: 'defaultValue',
          slotName: 'defaultValue'
        },
        {
          title: '备注说明',
          field: 'description',
          slotName: 'description'
        },
        {
          title: '操作',
          field: 'operate',
          slotName: 'operate',
          width: '50px'
        }
      ],
      tagRuleRules: {
        itemType: [
          { required: true, message: '请选择' }
        ], keyEn: [
          { pattern: /^[a-zA-Z1-9]+$/, message: '只能输入英文' },
          {
            max: 50,
            message: `最多输入50字`
          }
        ], keyCn: [
          { required: true, message: '请输入' },
          {
            max: 50,
            message: `最多输入50字`
          }
        ], itemDigit: [
          { required: true, message: '请输入' },
          { pattern: /^([0-9]|1[0-5])$/, message: '只能输入小于16的数字' }
        ], defaultValue: [
          { validator: defaultValue }
        ], description: [
          {
            max: 500,
            message: `最多输入500字`
          }
        ]
      },
      tagRuleTableData: [
        { itemType: 10, itemDigit: '1' },
        { itemType: 20, itemDigit: '1' },
        { itemType: 30, itemDigit: '1' },
        { itemType: 40, itemDigit: '1' }
      ],

      basicFormModel: {
        productName: '',
        templateName: ''
      },
      productNameArr: [],
      basicFormRules: {
        productId: [
          { required: true, message: '请选择产品名称' }
        ],
        ruleName: [
          { required: true, message: '请输入规则名称' },
          {
            max: 50,
            message: `最多输入50字`
          }
        ]
      }
    }
  },
  computed: {
    codePreview() {
      let str = ''
      this.tagRuleTableData.forEach(e => {
        let item = ''
        if (e.itemType === 30) {
          item = e.defaultValue ? dayjs(new Date()).format(`${e.defaultValue.toUpperCase()}`) : ''
        } else {
          item = e.defaultValue
        }
        str = str + (item || '')
      })
      return str
    }
  },
  created() {
    // 产品名称
    accApi.productList().then(res => {
      this.productNameArr = res.data.map(item => {
        return {
          label: item.productName,
          value: item.id
        }
      })
    })
    // 详情
    if (this.$route.query.id) {
      accApi.detailTagRuleApi({ id: this.$route.query.id }).then((res) => {
        if (this.$route.query.type) {
          this.basicFormModel = res.data
        }
        this.tagRuleTableData = res.data.ruleItemList
      })
    }
  },
  mounted() {
    this.addType = this.$route.query.addType
  },
  methods: {

    addTagRule() {
      this.$refs.table.validate((cb) => {
        if (cb) {
          this.tagRuleTableData.push({
            itemDigit: 1
          })
        }
      })
    },

    // 如果分类为自增流水 默认值改变时要根据序号位位数进行补0或者截取，并且开头不为0默认值的要进行比较大小
    setUuid(row) {
      if (row.itemType === 40 && row.itemDigit < 16) {
        if ((row.defaultValue?.length || 0) < row.itemDigit) {
          return this.$set(row, 'defaultValue', ((row.defaultValue || '1').padStart(row.itemDigit, '0')))
        } else {
          if (Number(row.defaultValue) > Number(`${Array(Number(row.itemDigit)).fill('9').join('')}`)) {
            return false
          } else this.$set(row, 'defaultValue', (row.defaultValue.slice(-row.itemDigit)))
        }
      }
    },
    deleteTagRule(index) {
      this.tagRuleTableData.splice(index, 1)
    },
    back() {
      this.basicFormModel = {}
      this.tagRuleTableData = []
      this.$router.replace('tagRule')
    },
    submit() {
      this.$refs.basic.validate((v) => {
        if (v) {
          this.$refs.table.validate((cb) => {
            if (cb) {
              // 编辑
              if (this.$route.query.type === 1) {
                accApi.editTagRuleDateApi({
                  ...this.basicFormModel,
                  ruleItemList: this.tagRuleTableData
                }).then(() => {
                  this.$message.success('操作成功')
                  this.back()
                })
              }
              // 新增
              else {
                accApi.addTagRuleDateApi({
                  ...this.basicFormModel,
                  id: undefined,
                  ruleItemList: this.tagRuleTableData
                }).then(() => {
                  this.$message.success('操作成功')
                  this.back()
                })
              }
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-select--small, .el-cascader--small, .el-input-number--small {
  width: 100%
}
.button-container {
  line-height: 60px;
  text-align: center;
}
</style>

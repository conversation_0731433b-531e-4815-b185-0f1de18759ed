<template>
  <el-link v-if="urlList.length >0" type="primary" @click="handleClick" v-html="htmlText" />
  <span v-else v-html="htmlText" />
</template>

<script>
export default {
  name: 'ImgLink',
  props: {
    urlList: {
      type: Array,
      default() { return [] }
    },
    // eslint-disable-next-line vue/require-default-prop,vue/require-prop-types
    htmlText: {}
  },
  computed: {
  }, methods: {
    handleClick() {
      this.$util.viewImage(this.urlList)
    }
  }
}
</script>

<style scoped lang="scss">
@import "~element-ui/packages/theme-chalk/src/common/var.scss";

::v-deep .el-link--inner {
  color: $--color-primary;
}
</style>

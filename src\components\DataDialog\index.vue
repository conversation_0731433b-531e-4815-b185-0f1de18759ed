<template>
  <div>
    <simple-data-dialog
      :title="dialogTitle"
      :visible.sync="visible"
      :size="size"
    >
      <data-form
        ref="form"
        :auto="false"
        :rules="dialogRule"
        :form-data="dialogData"
        @update:form-data="updateFormData"
      >
        <template slot-scope="scope">
          <slot
            :name="scope.data.slotName"
            :$index="scope.$index"
            :data="scope.data"
          />
          <slot
            :name="scope.data.appendRender"
            :$index="scope.$index"
            :data="scope.data"
          />
        </template>
      </data-form>
      <div
        slot="footer"
        class="dialog_btn"
      >
        <el-button @click="visible=false">{{ $t('common.cancel') }}</el-button>
        <!-- <el-button @click="reset()">{{$t('common.reset')}}</el-button> -->
        <el-button
          v-if="!notShowConfirm"
          type="primary"
          @click="confirm"
        >{{ $t('common.ok') }}</el-button>
      </div>
    </simple-data-dialog>
  </div>

</template>
<script>
import SimpleDataDialog from '@/components/SimpleDataDialog'
import DataForm from '@/components/DataForm'
export default {
  name: 'DataDialog',
  components: { SimpleDataDialog, DataForm },
  props: {
    dialogVisible: { // 表格弹出框是否显示
      type: Boolean,
      default: false
    }, // 显示
    dialogTitle: { // 表格弹出框标题
      type: String,
      default: null
    }, // 标题
    dialogData: { // 弹出框数据
      type: Object,
      default: null
    },
    dialogRule: { // 弹出框校验规则
      type: Object,
      default: null
    },
    confirmFunc: { // 确定回调函数
      type: Function,
      default: null
    },
    size: {
      type: String,
      default: 'small',
      validator: value => ['small', 'middle', 'big', 'large'].indexOf(value) > -1
    },
    notShowConfirm: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false
    }
  },
  watch: {
    dialogVisible(val) {
      this.visible = val
      if (val) {
        this.$nextTick(() => {
          this.$refs.form && this.$refs.form.clearValidate()
        })
      }
    },
    visible(val) {
      this.visibleHasChanged(val)
    }
  },
  create() {
  },
  mounted() {
  },
  methods: {
    updateFormData(val) {
      this.$emit('update:dialog-data', val)
    },
    visibleHasChanged(val) {
      this.$emit('update:dialog-visible', val)
    },
    getAllParams() {
      return this.$refs.form.getAllParams()
    },
    getFormItemAllRefs() {
      return this.$refs.form.getFormItemAllRefs()
    },
    setFormData(val) {
      return this.$refs.form.setFormData(val)
    },
    // 确定
    confirm() {
      if (this.confirmFunc) {
        this.$refs.form.validate(this.confirmFunc)
      }
    },
    // 保存校验
    validateFrom() {
      this.$refs.form.validate()
    }
  }

}
</script>
<style lang="scss" scoped>
</style>

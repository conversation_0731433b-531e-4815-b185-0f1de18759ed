
<script>
export default {
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      const { params, query } = this.$route
      const { path } = params
      const currentRoute = this.$store.state.tagsView.currentRoute
      if (currentRoute) {
        Object.keys(currentRoute.params).forEach(key => {
          params[key] = currentRoute.params[key]
        })
        Object.keys(currentRoute.query).forEach(key => {
          query[key] = currentRoute.query[key]
        })
      }
      this.$router.push({ path: '/' + path, name: currentRoute.name, query, params })
    })
  },
  render: function(h) {
    return h() // avoid warning message
  }
}
</script>

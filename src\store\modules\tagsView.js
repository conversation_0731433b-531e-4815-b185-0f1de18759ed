import storage from 'good-storage'
const state = {
  visitedViews: [],
  cachedViews: [],
  currentRoute: storage.session.get('currentRoute')
}
import permission from './permission'
import user from './user'
import app from './app'
const mutations = {
  ADD_VISITED_VIEW: (state, view) => {
    if (state.visitedViews.some(v => v.path === view.path) || view.name === 'redirect') return
    let preViewName = null
    const routesList = permission.state.routesList
    const flag = routesList.some(value => {
      if (value.name === view.name && value?.hidden) {
        preViewName = value.preViewName
        return true
      }
      return false
    })
    let parentView = null
    routesList.forEach(value => {
      if (value.name === preViewName) parentView = value
    })
    if (view?.params?.hidden === 'true' || flag) {
      if (state.visitedViews.length === 0 || (state.visitedViews.length === 1 && state.visitedViews[0].name === 'Dashboard')) {
        state.visitedViews.push(
          Object.assign({}, view, {
            title: parentView.meta.title || 'no-name',
            titleEn: parentView.meta.titleEn || 'no-name',
            subpath: parentView.fullMenuPath,
            subName: parentView.name
          })
        )
        return
      }
      state.visitedViews.some(visitedView => {
        if (visitedView.path === view.params.preViewPath || (view.params.preViewPath && visitedView.preViewPath === view.params.preViewPath) || (visitedView.name && visitedView.name === preViewName)) {
          visitedView.subpath = visitedView.path
          visitedView.subName = visitedView.name
          const title = visitedView.title
          const titleEn = visitedView.titleEn
          // visitedView.path = view.path
          // const index = state.cachedViews.indexOf(visitedView.name)
          // if (index > -1) {
          //   state.cachedViews.splice(index, 1)
          // }
          visitedView.name = view.name
          if (!state.cachedViews.includes(visitedView.name) && !visitedView.meta.noCache) {
            state.cachedViews.push(visitedView.name)
          }
          Object.assign(visitedView, view)
          visitedView.title = title
          visitedView.titleEn = titleEn
        }
      })
      return
    } else {
      const flag = state.visitedViews.some(visitedView => {
        if (visitedView.subName === view.name && visitedView.name !== view.name && visitedView.subpath === view.path && visitedView.path !== view.path) {
          visitedView.subpath = null
          visitedView.subName = null
          const title = visitedView.title
          const titleEn = visitedView.titleEn
          visitedView.path = view.path
          const index = state.cachedViews.indexOf(visitedView.name)
          if (index > -1) {
            state.cachedViews.splice(index, 1)
          }
          visitedView.name = view.name
          if (!state.cachedViews.includes(visitedView.name) && !visitedView.meta.noCache) {
            state.cachedViews.push(visitedView.name)
          }
          Object.assign(visitedView, view)
          visitedView.title = title
          visitedView.titleEn = titleEn
          return true
        }
        return false
      })
      if (flag) return
    }
    let userMenu = storage.get(user.state.userCode)
    if (!userMenu) {
      userMenu = {}
    }
    if (!userMenu[app.state.currentMenuPath]) {
      userMenu[app.state.currentMenuPath] = []
    }
    const visitCountFlag = userMenu[app.state.currentMenuPath].some(value => {
      if (value.name === view.name && view.name !== 'Dashboard') { value.visitCount = value.visitCount + 1; return true }
      return false
    })
    if (!visitCountFlag && view.name !== 'Dashboard') {
      userMenu[app.state.currentMenuPath].push({
        name: view.name, path: view.path, visitCount: 1, title: view.meta.title || 'no-name', titleEn: view.meta.titleEn || 'no-name'
      })
    }
    storage.set(user.state.userCode, userMenu)
    state.visitedViews.push(
      Object.assign({}, view, {
        title: view.meta.title || 'no-name',
        titleEn: view.meta.titleEn || 'no-name'
      })
    )
  },
  ADD_CACHED_VIEW: (state, view) => {
    if (state.cachedViews.includes(view.name)) return
    if (!view.meta.noCache) {
      state.cachedViews.push(view.name)
    }
  },

  DEL_VISITED_VIEW: (state, view) => {
    for (const [i, v] of state.visitedViews.entries()) {
      if (v.path === view.path) {
        state.visitedViews.splice(i, 1)
        break
      }
    }
  },

  DEL_CACHED_VIEW: (state, view) => {
    const index = state.cachedViews.indexOf(view.name)
    if (index > -1) {
      state.cachedViews.splice(index, 1)
    }
  },
  DEL_OTHERS_VISITED_VIEWS: (state, view) => {
    state.visitedViews = state.visitedViews.filter(v => {
      return v.meta.affix || v.path === view.path
    })
  },
  SET_CURRENT_ROUTE: (state, currentRoute) => {
    state.currentRoute = currentRoute
    storage.session.set('currentRoute', currentRoute)
  },
  DEL_OTHERS_CACHED_VIEWS: (state, view) => {
    const index = state.cachedViews.indexOf(view.name)
    if (index > -1) {
      state.cachedViews = state.cachedViews.slice(index, index + 1)
    } else {
      // if index = -1, there is no cached tags
      state.cachedViews = []
    }
  },

  DEL_ALL_VISITED_VIEWS: state => {
    // keep affix tags
    const affixTags = state.visitedViews.filter(tag => tag.meta.affix)
    state.visitedViews = affixTags
  },
  DEL_ALL_CACHED_VIEWS: state => {
    state.cachedViews = []
  },
  UPDATE_VISITED_VIEW: (state, view) => {
    for (let v of state.visitedViews) {
      if (v.path === view.path) {
        v = Object.assign(v, view)
        break
      }
    }
  }
}

const actions = {
  addView({ dispatch }, view) {
    dispatch('addVisitedView', view)
    dispatch('addCachedView', view)
  },
  addVisitedView({ commit }, view) {
    commit('ADD_VISITED_VIEW', view)
  },
  addCachedView({ commit }, view) {
    commit('ADD_CACHED_VIEW', view)
  },
  delView({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delVisitedView', view)
      dispatch('delCachedView', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delVisitedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_VISITED_VIEW', view)
      resolve([...state.visitedViews])
    })
  },
  delCachedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_CACHED_VIEW', view)
      resolve([...state.cachedViews])
    })
  },

  delOthersViews({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delOthersVisitedViews', view)
      dispatch('delOthersCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delOthersVisitedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_VISITED_VIEWS', view)
      resolve([...state.visitedViews])
    })
  },
  delOthersCachedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_CACHED_VIEWS', view)
      resolve([...state.cachedViews])
    })
  },

  delAllViews({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delAllVisitedViews', view)
      dispatch('delAllCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delAllVisitedViews({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_VISITED_VIEWS')
      resolve([...state.visitedViews])
    })
  },
  delAllCachedViews({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_CACHED_VIEWS')
      resolve([...state.cachedViews])
    })
  },

  setCurrentRoute({ commit }, currentRoute) {
    commit('SET_CURRENT_ROUTE', currentRoute)
  },
  updateVisitedView({ commit }, view) {
    commit('UPDATE_VISITED_VIEW', view)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

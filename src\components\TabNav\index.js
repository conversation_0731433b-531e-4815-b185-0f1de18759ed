import TabBar from 'element-ui/packages/tabs/src/tab-bar'
import { addResizeListener, removeResizeListener } from 'element-ui/src/utils/resize-event'

export default {
  name: 'TabNav',

  components: {
    TabBar
  },

  props: {
    stretch: Boolean,
    isX: {
      type: Boolean,
      default: false
    },
    isY: {
      type: Boolean,
      default: false
    },
    tabStyle: {
      type: String,
      default: ''
    },
    navScrollHeight: {
      type: String,
      default: null
    },
    navScrollWidth: {
      type: String,
      default: null
    },
    propNavOffsetX: {
      type: Number,
      default: 0
    },
    propNavOffsetY: {
      type: Number,
      default: 0
    },
    propScrollable: {
      type: Boolean,
      default: false
    },
    propNavStyle: {
      type: Object,
      default: null
    }
  },
  watch: {
    propNavOffsetX(newVal) {
      this.navOffsetX = newVal
    },
    propNavOffsetY(newVal) {
      this.navOffsetY = newVal
    }
  },
  data() {
    return {
      scrollableX: false,
      scrollableY: false,
      navOffsetX: 0,
      navOffsetY: 0
    }
  },

  computed: {
    navStyle() {
      const style = { zIndex: 1 }
      if (this.isX && this.isY) {
        style.transform = `translate(-${this.navOffsetX}px,-${this.navOffsetY}px)`
      } else if (this.isX) {
        style.transform = `translateX(-${this.navOffsetX}px)`
      } else if (this.isY) {
        style.transform = `translateY(-${this.navOffsetY}px)`
      }
      if (this.propNavStyle) {
        Object.assign(style, this.propNavStyle)
      }
      return style
    },
    navScrollStyle() {
      const style = { zIndex: 1 }
      if (this.navScrollHeight) {
        style.height = this.navScrollHeight
      }
      if (this.navScrollWidth) {
        style.width = this.navScrollWidth
      }
      return style
    }
  },

  updated() {
    this.update()
  },

  mounted() {
    addResizeListener(this.$el, this.update)
    document.addEventListener('visibilitychange', this.visibilityChangeHandler)
    window.addEventListener('blur', this.windowBlurHandler)
    window.addEventListener('focus', this.windowFocusHandler)
  },

  beforeDestroy() {
    if (this.$el && this.update) removeResizeListener(this.$el, this.update)
    document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
    window.removeEventListener('blur', this.windowBlurHandler)
    window.removeEventListener('focus', this.windowFocusHandler)
  },

  methods: {
    scrollPrevX() {
      const containerSize = this.$refs.navScroll[`offsetWidth`]
      const currentOffset = this.navOffsetX

      if (!currentOffset) return

      const newOffset = currentOffset > containerSize
        ? currentOffset - containerSize
        : 0

      this.navOffsetX = newOffset
      this.$emit('scroll-x', newOffset)
    },
    scrollNextX() {
      const navSize = this.$refs.nav[`offsetWidth`]
      const containerSize = this.$refs.navScroll[`offsetWidth`]
      const currentOffset = this.navOffsetX

      if (navSize - currentOffset <= containerSize) return

      const newOffset = navSize - currentOffset > containerSize * 2
        ? currentOffset + containerSize
        : (navSize - containerSize)

      this.navOffsetX = newOffset
      this.$emit('scroll-x', newOffset)
    },
    scrollPrevY() {
      const containerSize = this.$refs.navScroll[`offsetHeight`]
      const currentOffset = this.navOffsetY

      if (!currentOffset) return

      const newOffset = currentOffset > containerSize
        ? currentOffset - containerSize
        : 0

      this.navOffsetY = newOffset
      this.$emit('scroll-y', newOffset)
    },
    scrollNextY() {
      const navSize = this.$refs.nav[`offsetHeight`]
      const containerSize = this.$refs.navScroll[`offsetHeight`]
      const currentOffset = this.navOffsetY

      if (navSize - currentOffset <= containerSize) return

      const newOffset = navSize - currentOffset > containerSize * 2
        ? currentOffset + containerSize
        : (navSize - containerSize)

      this.navOffsetY = newOffset
      this.$emit('scroll-y', newOffset)
    },
    update() {
      if (!this.$refs.nav) return
      const navSizeX = this.$refs.nav[`offsetWidth`]
      const containerSizeX = this.$refs.navScroll[`offsetWidth`]
      const currentOffsetX = this.navOffsetX

      if (containerSizeX < navSizeX) {
        const currentOffsetX = this.navOffsetX
        this.scrollableX = this.scrollableX || {}
        this.scrollableX.prev = currentOffsetX
        this.scrollableX.next = currentOffsetX + containerSizeX < navSizeX
        if (navSizeX - currentOffsetX < containerSizeX) {
          this.navOffsetX = navSizeX - containerSizeX
        }
      } else {
        this.scrollableX = false
        if (currentOffsetX > 0) {
          this.navOffsetX = 0
        }
      }
      const navSizeY = this.$refs.nav[`offsetHeight`]
      const containerSizeY = this.$refs.navScroll[`offsetHeight`]
      const currentOffsetY = this.navOffsetY

      if (containerSizeY < navSizeY) {
        const currentOffsetY = this.navOffsetY
        this.scrollableY = this.scrollableY || {}
        this.scrollableY.prev = currentOffsetY
        this.scrollableY.next = currentOffsetY + containerSizeY < navSizeY
        if (navSizeY - currentOffsetY < containerSizeY) {
          this.navOffsetY = navSizeY - containerSizeY
        }
      } else {
        this.scrollableY = false
        if (currentOffsetY > 0) {
          this.navOffsetY = 0
        }
      }
    }
  },

  render(h) {
    const {
      stretch,
      navStyle,
      navScrollStyle,
      scrollableX,
      scrollNextX,
      scrollPrevX,
      scrollableY,
      scrollNextY,
      scrollPrevY,
      tabStyle
    } = this
    let scrollBtn = []
    if (this.propScrollable || (!scrollableX && !scrollableY)) {
      scrollBtn = null
    } else if (scrollableX && scrollableY) {
      scrollBtn = [<span style='z-index:2' className={['el-tabs__nav-prev', scrollNextX.prev ? '' : 'is-disabled']}
        on-click={scrollPrevX} class='spd-left'></span>,
      <span style='z-index:2' className={['el-tabs__nav-next', scrollNextX.next ? '' : 'is-disabled']}
        on-click={scrollNextX} class='spd-right'></span>,
      <span style='z-index:2' className={['el-tabs__nav-prev', scrollableY.prev ? '' : 'is-disabled']}
        on-click={scrollPrevY} class='spd-top'></span>,
      <span style='z-index:2' className={['el-tabs__nav-next', scrollableY.next ? '' : 'is-disabled']}
        on-click={scrollNextY} class='spd-bottom'></span>
      ]
    } else if (scrollableX) {
      scrollBtn = [<span style='z-index:2' className={['el-tabs__nav-prev', scrollNextX.prev ? '' : 'is-disabled']}
        on-click={scrollPrevX} class='spd-left'></span>,
      <span style='z-index:2' className={['el-tabs__nav-next', scrollNextX.next ? '' : 'is-disabled']}
        on-click={scrollNextX} class='spd-right'></span>
      ]
    } else if (scrollableY) {
      scrollBtn = [
        <span style='z-index:2' className={['el-tabs__nav-prev', scrollableY.prev ? '' : 'is-disabled']}
          on-click={scrollPrevY} class='spd-top'></span>,
        <span style='z-index:2' className={['el-tabs__nav-next', scrollableY.next ? '' : 'is-disabled']}
          on-click={scrollNextY} class='spd-bottom'></span>
      ]
    } else {
      scrollBtn = null
    }
    return (
      <div class={['el-tabs__nav-wrap', scrollableX || scrollableY ? 'is-scrollable' : '', scrollableX ? `is-X` : '', scrollableY ? `is-bottom` : '']}>
        {scrollBtn}
        <div class={['el-tabs__nav-scroll']} ref='navScroll'
          style={navScrollStyle}>
          <div
            class={['el-tabs__nav', scrollableX ? `is-X` : '', scrollableY ? `is-bottom` : '', stretch && scrollableX ? 'is-stretch' : '', tabStyle]}
            ref='nav'
            style={navStyle}
            role='tablist'
          >
            {this.$slots.default}
          </div>
        </div>
      </div>
    )
  }
}

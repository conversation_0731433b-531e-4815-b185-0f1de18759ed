<template>
  <div class="template-render">
    <div class="phone" style="">
      <div v-for="item in renderList" :key="item.key">
        <div v-if="item.type==='title'" class="title">
          <div
            class="flex-v-center"
            :style="{'background-color': item.props.bgColor}"
          >
            <div v-if="item.props.icon==1" class="mh-image">
              <img :src="item.props.icon[0].url">
            </div>
            <div v-else class="mh-image2">
              <img :src="require('@/assets/icon/logo1.png')">
            </div>
            <p class="mh-text">{{ item.props.title || '请在配置中输入标题' }}</p>
          </div>
        </div>
        <div v-if="item.type==='banner'" class="banner">
          <el-carousel v-if="item.props.images.length>0" :height="`${item.props.height}px`">
            <el-carousel-item v-for="(img,i) in item.props.images" :key="i">
              <div class="images">
                <img :src="img.url">
              </div>
            </el-carousel-item>
          </el-carousel>
          <div v-else :style="{height: `${item.props.height}px`}" class="no_content">(无图片)</div>
        </div>
        <div v-if="item.type==='productIntroduction'" class="productIntroduction">
          <div class="product_title">产品介绍</div>
          <span>{{ productInfo.remark || '暂无' }}</span>
        </div>

        <div v-if="item.type==='productInfo'" class="productInfo">
          <div :style="{background: `url(${item.props.bgImg[0]?item.props.bgImg[0].url:back})`}" class="bigBox">
            <div
              v-if="productInfo.productAttrList&&productInfo.productAttrList.length>0"
              class="mc-info mc-wrap"
            >
              <div v-if="item.props.showLogo" style="display: flex;justify-content: flex-end;margin-right: 20px;margin-bottom: 10px"><img :src="ky" style="width: 70px;"></div>
              <h1 :style="{'background-color': `${item.props.titleBgColor}`,color: `${item.props.titleTextColor}`}">{{ productInfo.packageName }}</h1>
              <div class="mc-info-cont">
                <div v-for="(e,i) in productData" :key="i" class="texts">
                  <div class="leftBox">【<span
                    :class="[e.attrNameCn.length<=4?'titleStyle':'','left']"
                  >{{ e.attrNameCn }}</span>】
                  </div>
                  <div style="margin-right: 5px">: </div>
                  <div v-if="/^(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?$/.test(e.attrValue)===true" class="right">
                    <a :href="e.attrValue"><span style="color: #0D93F4"> {{ e.attrValue }}</span></a></div>
                  <div v-else class="right"> {{ e.attrValue }}</div>
                </div>
              </div>
              <div v-if="item.props.showLogo" style="display: flex;align-items: center;justify-content: center;flex-direction: column">
                <div style="letter-spacing: 2px;font-weight: bold;font-size: 11px">江苏康缘药业股份有限公司</div>
                <div style="margin-bottom: 10px;width: 70%"><img :src="line" style=""></div>
              </div>
            </div>
            <div v-else class="no_content" style="margin-top: 5px;height: 100px">(请先配置产品属性)</div>
          </div>
          <div v-if="item.props.crossProvincialFiling===true" class="crossProvincialFiling">
            <div v-for="(item2,index) in productInfo.productBeianList" :key="index">
              <div>
                <h1 :style="{'background-color': `${item.props.titleBgColor}`,color: `${item.props.titleTextColor}`}">省份简称：{{ item2.attrNameCn }}</h1>
                <div class="crossBox">
                  <div class="left">备案号：</div>
                  <div class="right">{{ item2.attrValue }}</div>
                </div>
                <div class="crossBox">
                  <div class="left">中药饮片执行标准：</div>
                  <div class="right">{{ item2.otherAttr1 }}</div>
                </div>
                <div class="crossBox">
                  <div class="left">中药配方颗粒执行标准：</div>
                  <div class="right">{{ item2.otherAttr2 }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="item.type==='productBarCode'" class="productBarCode">
          <div class="mc-wrap warp">
            <h3>产品条码</h3>
            <div>
              <barcode :width="1" :height="30" :font-size="14" :value="productInfo.barCode||''">
                (请在产品中配置条形码)
              </barcode>
            </div>
          </div>
        </div>
        <div v-if="item.type==='qualityGuaranteePeriod'" class="qualityGuaranteePeriod">
          <div class="warp" style="justify-content: space-between">
            <h3 style="">保质期</h3>

            <div v-if="productInfo.productionDate&&productInfo.beyondShelfLifeDate">
              <div
                v-if="productInfo.beyondShelfLifeDate&&
                  dayjs(productInfo.beyondShelfLifeDate).diff(dayjs(new Date()).format('YYYY-MM-DD'))>0"
                class="right"
              >
                <div class="box">
                  <div>
                    {{
                      dayjs(productInfo.beyondShelfLifeDate).diff(productInfo.productionDate, 'day') + 1
                    }}天,
                    剩余
                    {{ dayjs(productInfo.beyondShelfLifeDate).diff(dayjs(new Date()).format('YYYY-MM-DD'), 'day') }}天
                  </div>
                  <div>{{ dayjs(productInfo.beyondShelfLifeDate).format(`YYYY年-MM月-DD日过期`) }}</div>
                </div>
                <el-progress
                  :text-inside="true"
                  :stroke-width="20"
                  :percentage="getProgressNum([productInfo.productionDate,productInfo.beyondShelfLifeDate])"
                  status="success"
                />
              </div>
              <div v-else style="text-align: right;font-size: 16px;color: #bdb9b9">过期</div>
            </div>
            <div v-else class="right" style="">
              <div class="box">
                <div>
                  剩余 x天
                </div>
                <div>xxxx年-xx月-xx日过期</div>
              </div>
              <el-progress
                :text-inside="true"
                :stroke-width="20"
                :percentage="100"
                status="success"
              />
            </div>
          </div>
        </div>
        <div v-if="item.type==='productPrice'" class="productPrice">
          <div class="mc-wrap warp">
            <h3>产品价格</h3>
            <div v-if="productInfo.price" class="money">{{
              productInfo.price &&
                (productInfo.price.toString().indexOf('.') > 0 ? '¥' + productInfo.price : '¥' + productInfo.price + '.00')
            }}
            </div>
            <div v-else class="money" style="font-size: 20px">(未配置价格)</div>
          </div>
        </div>
        <div v-if="item.type==='productImage'" class="productImage" style="margin-top: 5px">
          <div v-if="productInfo.productImgList&&productInfo.productImgList.length>0">
            <div v-for="(e,i) in productInfo.productImgList" :key="i" class="mh-image">
              <img :src="e.imgPath">
            </div>
          </div>
          <div v-else class="no_content" style="height: 200px">(请在产品中上传图片)</div>
        </div>
        <div v-if="item.type==='identificationCode'" class="identificationCode">

          <h3>{{ idisCode || '88.118.8/17305259620-KIT001' }}</h3>
          <div>
            <img src="@/assets/img/code1.png" class="badge">
            <br>
            <img src="@/assets/img/code2.png" class="badge">
          </div>

        </div>
        <div v-if="item.type==='text'" class="text">
          <div v-if="item.props.text">{{ item.props.text }}</div>
          <div v-else class="no_content" style="height: 100px">(无文本)</div>
        </div>
        <div v-if="item.type==='video'" class="video">
          <div>
            <video
              v-if="item.props.videoUrl"
              class="video-style"
              controls
              loop
              autoplay
              muted
              preload="metadata"
              :src="item.props.videoUrl"
              webkit-playsinline
              playsinline
              x5-video-player-type="”h5-page”"
            >
              您的浏览器不支持 video 标签。
            </video>
            <div v-else style="height: 200px" class="no_content">(无视频)</div>
          </div>
        </div>
        <div v-if="item.type==='image'" class="image">
          <div v-if="item.props.image&&item.props.image.length>0">
            <div v-for="(e,i) in item.props.image" :key="i" class="mh-image">
              <img :src="e.url" style="display: block;">
            </div>
          </div>
          <div v-else class="no_content" style="height: 50px">
            (无图片)
          </div>
        </div>
        <div v-if="item.type==='grid'" class="grid">
          <div class="bigBox">
            <div v-for="(e,i) in item.props.grid" :key="i" :style="{width:`${e.size*33.33}%`}">
              <div
                class="stylebox-item"
                :style="e.bgType?{'background-color':`${e.bgColor} !important`}:{background:`url(${e.bgImg[0]&&e.bgImg[0].url})`}"
                @click="openUrl(e)"
              >
                <i class="images" :class="e.icon" :style="{color: e.textColor}" />
                <div class="titles" :style="{color: e.textColor}">{{ e.title || '标题' }}</div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>

    <el-dialog
      title="详情"
      :visible.sync="dialogVisible"
      width="300px"
      :before-close="close"
    >
      <div style="text-align: center">
        <span>长按图片识别</span>
        <img :src="src">
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import QrCode from 'qrcode'
import dayjs from 'dayjs'
import acc from '@/api/acc/acc'
import back2 from '@/assets/img/back.jpg'
import ky from '@/assets/icon/ky.png'
import line from '@/assets/img/line.png'
export default {
  name: 'TemplateDesign',
  props: {
    renderList: {
      type: Array,
      default: () => {
        return []
      }
    },
    productId: {
      type: String,
      default: undefined
    },
    idisCode: {
      type: String,
      default: undefined
    }
  },
  data() {
    return {
      dayjs: dayjs,
      dialogVisible: false,
      src: '',
      back2,
      ky,
      line,
      productInfo: {},
      productData: [],
      beianDialogVisible: false,
      beianColumns: [
        {
          type: 'seq',
          title: '序号'
        },
        {
          title: '备案地区',
          field: 'attrNameCn'
        },
        {
          title: '备案号',
          field: 'attrValue'
        }
      ],
      beianTableData: []
    }
  },
  watch: {
    productId: {
      handler(val) {
        if (val) {
          this.getProductInfo(val)
        }
      },
      deep: true
    }
  },
  mounted() {
  },
  methods: {
    getProductInfo(productId) {
      acc.queryTemplateProduct({ productId, idisCode: this.idisCode, paramCheck: true }).then((res) => {
        this.productInfo = res.data
        const day = this.idisCode ? dayjs(this.productInfo.beyondShelfLifeDate).diff(dayjs(this.productInfo.productionDate), 'days') : 0
        this.productData = [
          { attrNameCn: '产品名称', attrValue: this.productInfo.packageName ? this.productInfo.packageName : this.productInfo.productName },
          // { attrNameCn: '生产日期', attrValue: this.idisCode ? this.productInfo.productionDate : 'xxxx年xx月xx日' },
          // { attrNameCn: '保质期', attrValue: this.idisCode ? day > 180 ? `${Math.floor(day.divide(30))}个月` : `${day}天` : 'xxxx年xx月xx日' },
          // { attrNameCn: '有效期至', attrValue: this.idisCode ? this.productInfo.beyondShelfLifeDate : 'xxxx年xx月xx日' },
          // { attrNameCn: '产品批号', attrValue: this.productInfo.batchNo },
          ...res.data.productAttrList]
      })
    },
    getImg(val) {
      if (val) {
        QrCode.toDataURL(val).then(url => {
          this.src = url
        })
      }
    },
    openUrl(e) {
      if (e.url) {
        if (e.isQrCode) {
          this.dialogVisible = true
          this.getImg(e.url)
        } else {
          window.open(e.url, '_blank')
        }
      }
    },
    beianClick(val) {
      this.beianDialogVisible = true
      this.beianTableData = val
    },
    close() {
      this.dialogVisible = false
    },
    getProgressNum(timeArr) {
      const left = dayjs(timeArr[1]).diff(dayjs(new Date()).format('YYYY-MM-DD'), 'day')
      const count = dayjs(timeArr[1]).diff(timeArr[0], 'day') + 1
      return Number(((count - left).divide(count) * 100).toFixed(2))
    },
    back() {
      this.$router.replace({ path: 'productTemplate' })
    }
  }
}
</script>

<style  scoped lang="scss">
.template-render {
  background: url(../../../../assets/img/mobile.png) no-repeat;
  flex-shrink: 0;
  background-size: contain;
  height: 750px;
  width: 360px;
  padding: 40px 13px 20px 13px;
  font-size: 13px;
}

.phone {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background-color: #f4f5f5;
  border-radius: 0 0 20px 20px;

  &::-webkit-scrollbar {
    display: none;
  }
}

::v-deep .el-dialog__wrapper .el-dialog__body {
  padding: 10px 20px;
  overflow-y: auto;
}
::v-deep .el-loading-mask {
  background-color: rgba(0,0,0,.7);
  z-index: 999998 !important;
}
.no_content {
  margin-top: 5px;
  display: flex;
  font-size: 16px;
  justify-content: center;
  align-items: center;
  color: rgb(136, 136, 136);
  background-color: rgb(255, 255, 255);
}

.popBox {
  background-color: #fff;
  border-radius: 16px 16px 0 0;
  border: 1px solid #969799;
  position: fixed;
  width: 100%;
  bottom: -100%;
  z-index: 999999;
  .box {
    padding: 14px 16px;
    font-size: 16px;
    text-align: center;
  }

  .top {
    border-radius: 16px 16px 0 0;
    text-align: center;
    color: #969799;
    font-size: 14px;
    line-height: 20px;
    padding: 20px 16px;
    border-bottom: 1px solid #ebedf0;
  }

  .mid {
    height: 8px;
    background-color: #f7f8fa;
  }

  .bottom {
    text-align: center;
    padding: 14px 16px;
    font-size: 16px;
  }
}
.v-enter-active{
  animation: slidein 0.5s ease forwards;
}
.v-leave-active{
  animation: slideout 1s ease forwards;
}
@keyframes slidein {
  from{
    bottom: -100%;
  }
  to{
    bottom: 0%;
  }
}
@keyframes slideout {
  from{
    bottom: 0%;
  }
  to{
    bottom: -100%;
  }
}
img {
  width: 100%;
  height: 100%;
}
.button-container {
  line-height: 60px;
  text-align: right;
}

.warp {
  background-color: #fff;
  padding: 15px;
  margin-top: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  h3 {
    word-break: break-all;
    font-weight: bold;
    flex-shrink: 0;
    //width: 80px;
    margin-right: 25px;
    font-size: 16px;
  }
}

.title {
  .flex-v-center {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 50px;
  }

  .mh-image {
    width: 25px;
    height: 25px;
    border-radius: 25px;
    border: 1px solid #e9e9e9;
    margin: 0 10px 0 18px;
    position: relative;
    display: inline-block;
    overflow: hidden;

  }  .mh-image2 {
    width: 28px;
    height: 25px;
    border-radius: 25px;
    margin: 0 10px 0 18px;
    position: relative;
    display: inline-block;
    overflow: hidden;

  }

  .mh-text {
    width: 0px;
    flex: 1;
    font-size: 16px;
    color: #fff;
    letter-spacing: 2px;
  }

}

.banner {
  .images {
    width: 100%;
    height: 100%;
  }
}

.productIntroduction {
  padding: 15px !important;
  background-color: #fff;
  margin-top: 5px;
  margin-bottom: 5px;
  border: 1px solid orange;

  .product_title {
    font-size: 16px;
    font-weight: 700;
    color: #1c2023;
    margin-bottom: 10px;
  }

  span {

    font-size: 14px;
  }
}

.productInfo {
  .bigBox{
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    padding: 15px 10px;
  }
  h1{
    text-align: center;
    margin: 0 10px;
    font-size: 16px;
    padding: 8px;
    border-radius: 3px
  }
  .mc-info {
    //margin-top: 5px;
    margin-bottom: 5px;
    //height: 450px;
    //background-color: rgba(255,255,255,0.96);
    border-radius: 2px;
    padding-top: 10px;

    .mc-info-cont {
      width: 100%;
      margin-top: 0px;
      //height: 380px;
      overflow-y: auto;
      display: block;
      font-size: 14px;
      padding: 20px 10px;

    }

    .texts {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #3C4D5B;
      font-size: 13px;
      font-weight: bolder;
      padding-bottom: 10px;
      //border-bottom: 1px solid #f5f5f5;

      .leftBox {

        width: 100px;
        text-align: left;

        .left {
          width: 65px;
        }

        .titleStyle {
          display: inline-block;
          text-align: justify;
          text-align-last: justify;
        }
      }

    }

    .right {
      width: 65%;
      word-break: break-all;
      font-weight: normal;
    }

  }
  .crossProvincialFiling{
    padding: 15px 10px;
    h1{
      margin:  0px;
    }
    .crossBox{
      background-color: #fff;
      border-bottom: 1px solid #E9E9E9;
      padding: 10px;
      display: flex;
      justify-content: space-between;
      background-color: #fff;
      .left{
        font-weight: bold;
        width: 90px;
        letter-spacing: 1px;
        color: #999FAC;
      }
      .right{
        flex: 1;
        color: #66706D;
        font-weight: normal;
      }
    }
    .crossBox:last-child{
      border-bottom: 0px;
    }

  }
}

.productBarCode {
  .mc-wrap {

    margin-bottom: 5px;
  }
}

.qualityGuaranteePeriod {
  .right {
    width: 100%;

    .box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 2px 3px;
    }
  }
}

.productPrice {
  .money {
    color: red;
    font-size: 28px;
  }
}

.identificationCode {
  background-color: #fff;
  padding: 5px 10px;
  margin-top: 5px;

  div {
    img {
      height: 20px;
      width: auto;
    }
  }

  h3 {
    word-break: break-all;
    font-weight: bold;
    flex-shrink: 0;
    margin-right: 15px;
    font-size: 16px;
  }
}

.text {
  div {
    white-space: pre-wrap;
    line-height: 1.5;
    word-break: break-all;
    font-size: 14px;
  }

  background-color: #fff;
  padding: 15px;
  margin-top: 5px;
}

.video {

  .video-style {
    width: 100%;
  }
}

.image {
  .mh-image {
    width: 100%;
  }
}

.grid {

  .bigBox {
    display: flex;
    align-items: center;
    justify-content: center;

    .stylebox-item {
      margin: 2px;
      height: 100px;
      background-size: cover !important;
      background-repeat: no-repeat;
      background-position: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .images {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      font-size: 40px;
      line-height: 40px;
    }

    .titles {
      margin: 5px 0 0 0;
      font-size: 14px;
      text-align: center;
    }
  }
}
</style>

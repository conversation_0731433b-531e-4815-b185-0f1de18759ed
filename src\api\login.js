import request from '@/utils/request'

export function loginByUsername(username, password, openid) {
  try {
    // for x-www-form-urlencoded
    return request({
      method: 'post',
      url: '/api/acc/user/login',
      data: {
        username,
        password
      }
    })
  } catch (e) {
    console.log('login error!')
    console.log(e)
  }
}

export function logout(token) {
  return request({
    url: '/api/acc/user/logout',
    method: 'post'
  })
}

export function getUserInfo() {
  return request({
    url: '/api/acc/user/loginUser',
    method: 'get'
  })
}
export function getMenuList() {
  return request({
    url: '/api/acc/user/menu',
    method: 'get'
  })
}
export function signUp(params) {
  return request.get('/api/acc/user/signUp', { params })
}
export function getCaptcha() {
  return request({
    url: '/api/acc/kaptcha/getCaptcha',
    method: 'post'
  })
}
// accPortal/enterprise/productMg/product
// accPortal/enterprise/tagMg/tagDetail
//
// export function getMenuList() {
//   return Promise.resolve({
//     'code': 'SUCCESS',
//     'msg': '成功',
//     'data': [
//       {
//         'url': '',
//         'title': '溯源有信',
//         'titleEn': null,
//         'menuIcon': 'el-icon-s-order',
//         'menuCode': 'accPortal',
//         'isShow': '1',
//         'level': '0',
//         'children': [
//           {
//             'url': null,
//             'title': '企业功能',
//             'titleEn': null,
//             'menuIcon': null,
//             'menuCode': 'enterprise',
//             'isShow': '1',
//             'level': '1',
//             'children': [
//               {
//                 'url': '',
//                 'title': '标识管理',
//                 'titleEn': null,
//                 'menuIcon': null,
//                 'menuCode': 'tagMg',
//                 'isShow': '1',
//                 'level': '2',
//                 'children': [
//                   {
//                     'url': 'TagDetail',
//                     'title': '标识明细',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'tagDetail',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   },
//                   {
//                     'url': 'TagGenerate',
//                     'title': '标识生成',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'tagGenerate',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   },
//                   {
//                     'url': 'TagRule',
//                     'title': '标识规则',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'tagRule',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   }
//                 ]
//               },
//               {
//                 'url': null,
//                 'title': '产品管理',
//                 'titleEn': null,
//                 'menuIcon': null,
//                 'menuCode': 'productMg',
//                 'isShow': '1',
//                 'level': '2',
//                 'children': [
//                   {
//                     'url': 'Product',
//                     'title': '产品管理',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'product',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   },
//                   {
//                     'url': 'ProductBatch',
//                     'title': '批次管理',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'productBatch',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   },
//                   {
//                     'url': 'ProductTemplate',
//                     'title': '产品模板',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'productTemplate',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   }
//                 ]
//               },
//               {
//                 'url': null,
//                 'title': '统计分析',
//                 'titleEn': null,
//                 'menuIcon': null,
//                 'menuCode': 'statisticAnalysis',
//                 'isShow': '1',
//                 'level': '3',
//                 'children': [
//                   // {
//                   //   'url': 'Statistic',
//                   //   'title': '扫码统计',
//                   //   'titleEn': null,
//                   //   'menuIcon': null,
//                   //   'menuCode': 'statisticAnalysis',
//                   //   'isShow': '1',
//                   //   'level': '3',
//                   //   'children': []
//                   // },
//                   {
//                     'url': 'Record',
//                     'title': '扫码记录',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'statisticAnalysis',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   }
//                 ]
//               }
//               // {
//               //   'url': null,
//               //   'title': '农户管理',
//               //   'titleEn': null,
//               //   'menuIcon': null,
//               //   'menuCode': 'farmerInfo',
//               //   'isShow': '1',
//               //   'level': '3',
//               //   'children': [
//               //     {
//               //       'url': 'FarmerInfo',
//               //       'title': '瓜农信息',
//               //       'titleEn': null,
//               //       'menuIcon': null,
//               //       'menuCode': 'farmerInfo',
//               //       'isShow': '1',
//               //       'level': '3',
//               //       'children': []
//               //     }
//               //   ]
//               // }
//             ]
//           },
//           {
//             'url': '',
//             'title': '系统配置',
//             'titleEn': null,
//             'menuIcon': 'el-icon-s-tools',
//             'menuCode': 'acc',
//             'isShow': '1',
//             'level': '1',
//             'children': [
//               {
//                 'url': '',
//                 'title': '系统管理',
//                 'titleEn': null,
//                 'menuIcon': '',
//                 'menuCode': 'sysMg',
//                 'isShow': '1',
//                 'level': '2',
//                 'children': [
//                   {
//                     'url': 'MenuConf',
//                     'title': '菜单配置',
//                     'titleEn': null,
//                     'menuIcon': '',
//                     'menuCode': 'menuConf',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   },
//                   {
//                     'url': 'UserInfo',
//                     'title': '用户信息配置',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'userInfo',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   },
//                   {
//                     'url': 'UserRole',
//                     'title': '角色权限配置',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'userRole',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   },
//                   {
//                     'url': 'DictMaintenance',
//                     'title': '数据字典配置',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'dictMaintenance',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   },
//                   {
//                     'url': 'Department',
//                     'title': '部门管理',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'department',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   },
//                   {
//                     'url': 'Log',
//                     'title': '操作日志',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'log',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   },
//                   {
//                     'url': 'TemplateSelf',
//                     'title': '自定义模板配置',
//                     'titleEn': null,
//                     'menuIcon': null,
//                     'menuCode': 'templateSelf',
//                     'isShow': '1',
//                     'level': '3',
//                     'children': []
//                   }
//                 ]
//               }
//             ]
//           }
//         ]
//       }
//     ]
//   })
// }
//

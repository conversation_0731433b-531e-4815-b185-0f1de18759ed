<template>
  <el-container is-vertical>
    <el-header>
      <div class="header-left">
<!--        <img src="@/assets/icon/logo1.png" alt="" class="header-logo">-->
        <span class="title">中药行业工业互联网标识应用平台</span>
      </div>
      <div class="header-right">
        <el-dropdown class="avatar-container" trigger="click" placement="bottom">
          <span class="avatar-wrapper">
            <img :src="require('../assets/icon/head-user.png')" class="user-avatar" alt="">
            <span>{{ name ? name : '' }}</span>
            <i class="el-icon-arrow-down el-icon--right"/>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="changePas">
              <svg-icon icon-class="pwd"/>
              <span style="margin-left: 5px">{{ $t('common.changePassword') }}</span>
            </el-dropdown-item>
            <el-dropdown-item @click.native="logout">
              <svg-icon icon-class="login-out"/>
              <span style="margin-left: 5px">{{ $t('common.logout') }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </el-header>
    <div
        :class="classObj"
        class="app-wrapper"
    >
      <div
          v-if="device==='mobile'&&sidebar.opened"
          class="drawer-bg"
          @click="handleClickOutside"
      />
      <sidebar class="sidebar-container"/>
      <div class="main-container">
        <div :class="{'fixed-header':fixedHeader}">
          <navbar/>
        </div>
        <app-main/>
      </div>
    </div>
    <el-my-drawer v-if="elMyDrawerVisible" :custom-class="`custom-class ${drawerClass}`" :append-to-body="true"
                  :modal="false" :visible.sync="elMyDrawerVisible" :direction="'btt'" @close="closeDrawer"
    >
      <component :is="drawerComponentName" v-if="elMyDrawerVisible"/>
    </el-my-drawer>

    <simple-data-dialog class="pswForm" :visible.sync="pswdialogVisible" size="small"
                        :title="$t('common.changePassword')"
    >

      <el-form ref="pswForm" :model="pswRuleForm" :rules="pswRules" label-width="100px" class="demo-ruleForm">
        <el-form-item :label="$t('layout.oldPassword')+'：'" prop="oldPassword">
          <el-input v-model="pswRuleForm.oldPassword" @blur="valid('newPassword','confirmPsw')"/>
        </el-form-item>
        <el-form-item :label="$t('layout.newPassword')+'：'" prop="newPassword">
          <el-input v-model="pswRuleForm.newPassword" @blur="valid('confirmPsw')"/>
        </el-form-item>
        <el-form-item :label="$t('layout.confirmPsw')+'：'" prop="confirmPsw">
          <el-input v-model="pswRuleForm.confirmPsw" @blur="valid('newPassword')"/>
        </el-form-item>
      </el-form>

      <div
          slot="footer"
          class="dialog_btn"
      >
        <el-button @click="canclepsw">{{ $t('common.cancel') }}</el-button>
        <el-button
            type="primary"
            @click="confirmpsw"
        >{{ $t('common.ok') }}
        </el-button>
      </div>
    </simple-data-dialog>
  </el-container>
</template>

<script>
import { AppMain, Navbar, Sidebar } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapGetters } from 'vuex'
import Cookies from 'js-cookie'
import drawerComponent from '@/utils/drawer-component'
import accApi from '@/api/acc/acc'
import VueEvent from '@/utils/vue-event'
import SimpleDataDialog from '@/components/SimpleDataDialog'

export default {
  name: 'Layout',
  componentName: 'Layout',
  components: {
    Navbar,
    Sidebar,
    AppMain,
    SimpleDataDialog
  },
  mixins: [ResizeMixin, drawerComponent],
  data() {
    return {
      canChoiceStore: false,
      elMyDrawerVisible: false,
      // curlang: Cookies.get('language') == null || Cookies.get('language') === 'zh' ? '1' : '2',
      curlang: Cookies.get('language') == null || Cookies.get('language') === 'zh' ? '中文' : 'English',
      curProjectCode: {
        value: null,
        selectOptions: []
      },
      pswRuleForm: {
        oldPassword: '',
        newPassword: '',
        confirmPsw: ''
      },
      pswRules: {
        oldPassword: [{
          required: true,
          message: this.$t('common.enter') + this.$t('layout.oldPassword'),
          trigger: 'blur'
        }],
        newPassword: [
          { required: true, message: this.$t('common.enter') + this.$t('layout.newPassword'), trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value === this.pswRuleForm.oldPassword) {
                return callback(new Error('新密码不能和旧密码一致'))
              } else {
                callback()
              }
            }, trigger: 'blur'
          }
        ],
        confirmPsw: [{
          required: true,
          message: this.$t('common.enter') + this.$t('layout.confirmPsw'),
          trigger: 'blur'
        }, {
          validator: (rule, value, callback) => {
            if (value !== this.pswRuleForm.newPassword) {
              return callback(new Error('两次新密码不一致'))
            } else {
              callback()
            }
          }, trigger: 'blur'
        }]
      },
      pswdialogVisible: false
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'name', 'drawerComponentName', 'drawerClass', 'drawerVisible', 'currentMenuPath', 'visitedViews', 'projectInfo', 'avatar']),
    sidebar() {
      return this.$store.state.app.sidebar
    },
    device() {
      return this.$store.state.app.device
    },
    fixedHeader() {
      return this.$store.state.settings.fixedHeader
    },
    needTagsView() {
      return this.$store.state.settings.tagsView
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  watch: {
    drawerVisible: {
      handler(val) {
        this.elMyDrawerVisible = val
      },
      immediate: true
    }
  },
  created() {
    this.$on('Layout.back2home', this.back2home)
  },
  beforeDestroy() {
    this.$off('Layout.back2home')
  },
  mounted() {
    if (Cookies.get('language') == null) {
      Cookies.set('language', 'zh')
    }
    this.initProject()
  },
  methods: {
    initProject() {
      const res = {
        data: [{
          code: '001',
          codeName: '康缘智慧港'
        },
          {
            code: '002',
            codeName: '连云港康缘'
          }
        ]
      }
      if (!(res.data?.length > 0)) {
        this.$message.warning(this.$t('common.noApprove'))
        this.back2home()
        return
      }
      this.curProjectCode.selectOptions = this.$util.mapOptions(res.data)
      const flag = this.projectInfo && this.curProjectCode.selectOptions.some(value => value.value === this.projectInfo.code)
      if (!flag) {
        this.curProjectCode.value = this.curProjectCode.selectOptions[0].value
        this.$store.dispatch('setProjectInfo', this.curProjectCode.selectOptions.find(option => option.value === this.curProjectCode.value))
      } else {
        this.curProjectCode.value = this.projectInfo.code
      }
    },
    closeDrawer() {
      this.$store.dispatch('app/setDrawerInfo', null)
      this.$store.dispatch('app/setDrawerComponentName', null)
      this.$store.dispatch('app/setDrawerVisible', false)
      this.$store.dispatch('app/setDrawerClass', null)
    },
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    logout() {
      this.$store.dispatch('FedLogOut').then(() => {
        this.$destroy()
        this.$router.push('/loginPage')
        location.reload() // 为了重新实例化vue-router对象 避免bug
      })
      this.$store.dispatch('tagsView/setCurrentRoute', null)
    },
    back2home() {
      if (this.currentMenuPath === '/store') {
        this.$store.dispatch('app/setDrawerVisible', false)
      }
      this.$destroy()
      this.$store.dispatch('tagsView/setCurrentRoute', null)
      this.$router.push({ path: '/dashboard/dashboard', name: 'PortalPage' })
    },
    changePas() {
      this.pswdialogVisible = true
    },
    canclepsw() {
      this.pswdialogVisible = false
      this.clearPsw()
    },
    clearPsw() {
      this.pswRuleForm.oldPassword = ''
      this.pswRuleForm.newPassword = ''
      this.pswRuleForm.confirmPsw = ''
      this.$refs.pswForm.clearValidate()
    },
    confirmpsw() {
      this.$refs.pswForm.validate((v) => {
        if (v) {
          accApi.changePassword(this.pswRuleForm).then(res => {
            if (res.code === 'SUCCESS') {
              this.pswdialogVisible = false
              this.clearPsw()
              this.$confirm(this.$t('common.reviseSuccess'), this.$t('common.prompt'), {
                confirmButtonText: this.$t('common.ok'),
                showCancelButton: false,
                showClose: false,
                closeOnClickModal: false,
                closeOnPressEscape: false,
                type: 'warning'
              }).then(() => {
                // 调用登出逻辑，清除token重定向到登录页面
                this.$store.dispatch('FedLogOut').then(() => {
                  this.$destroy()
                  this.$router.push('/loginPage')
                  location.reload() // 为了重新实例化vue-router对象 避免bug
                })
              })
            }
          })
        }
      })
    },
    valid(a, b) {
      a && this.$refs.pswForm.validateField(a)
      b && this.$refs.pswForm.validateField(b)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';
@import '~@/styles/variables.scss';
@import 'src/styles/variables.scss';

::v-deep {
  .el-badge__content.is-fixed {
    right: 20px;
  }
}

.el-header {
  background-color: $headerBg;
  height: $headerHeight !important;
  line-height: $headerHeight !important;
  padding: 0;

  .header-left {
    float: left;
    display: flex;
    align-items: center;

    .title {
      color: #0D6CE4;
      font-size: 20px;
      font-weight: bold;
      display: inline-block;
      margin-left: 11px;
      margin-bottom: -5px
    }
  }

  .header-right {
    float: right;
    display: flex;
    align-items: center;
  }

  .icon-btn {
    display: inline-block;

    i {
      font-size: 20px;
      vertical-align: middle;
      color: #2F3133;
      padding: 0 15px;
      cursor: pointer;
    }
  }

  .sep {
    width: 1px;
    height: 20px;
    background-color: rgba(255, 255, 255, .2);
    float: left;
    margin-top: 10px;
  }

  .theme {
    margin-left: 15px;
    height: $headerHeight;
    float: left;

    .title {
      font-size: 18px;
      margin: 3px 0 0 0;
      color: #FFFFFF;
      float: left;
      width: 100%;
    }

    .version {
      font-size: 12px;
      color: rgba(255, 255, 255, .5);
      float: left;
      transform: scale(.8, .8);
      margin-left: -11px;
    }
  }

  .avatar-container {
    display: inline-block;
    line-height: 48px;
    color: #2F3133;

    .avatar-wrapper {
      padding: 0 12px;
      position: relative;

      .user-avatar {
        cursor: pointer;
        width: 20px;
        height: 20px;
        border-radius: 25px;
        vertical-align: middle;
        margin-right: 5px;
      }

      .el-icon-caret-bottom {
        cursor: pointer;
        position: absolute;
        right: -20px;
        top: 25px;
        font-size: 12px;
      }
    }
  }
}

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 64px);
}

.mobile .fixed-header {
  width: 100%;
}

.el-select + .el-select {
  margin-left: 5px;
}

.pswForm .dialog .el-form-item {
  width: 80% !important;
}

.header-logo {
  height: 30px;
  margin: 0;
  margin-left: 15px;
  margin-top: 5px;
}
</style>

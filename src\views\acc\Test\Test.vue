<template>
  <div class="app-container">
    <div class="app-inner-container">
      <el-button @click="$refs.virtualTable.validate((valid) => {test(valid)})">校验</el-button>
      <el-button @click="test($refs.virtualTable.getCheckboxRecords())">选中</el-button>
      <el-button @click="$refs.virtualTable.print()">打印</el-button>
    </div>
    <virtual-table ref="virtualTable" :table-data="tableData" :columns="columns" :edit-rules="validRules" :print-config="printConfig">
      <template v-slot:name="{row, scope}">
        <el-input v-model="row.name" @change="$refs.virtualTable.updateStatus(scope)" />
      </template>
      <template v-slot:sex="{row, scope}">
        <el-select v-model="row.sex" @change="$refs.virtualTable.updateStatus(scope)">
          <el-option :value="0" label="男" />
          <el-option :value="1" label="女" />
        </el-select>
      </template>
      <template v-slot:test="{row}">
        <el-select v-model="row.test">
          <el-option :value="0" label="测试1" />
          <el-option :value="1" label="测试2" />
        </el-select>
      </template>
      <template v-slot:birthday="{row}">
        <el-date-picker v-model="row.birthday" /></template>
    </virtual-table>
  </div>
</template>

<script>

import VirtualTable from '@/components/VirtualTable/VirtualTable'
export default {
  name: 'Index',
  components: {
    VirtualTable
  },
  data() {
    return {
      printConfig: {
        title: '出货单',
        topContent: [
          {
            label: '出货人',
            value: 'aabbcc' },
          {
            label: '出货日期',
            value: '2023-03-01' },
          {
            label: '出货日期',
            value: '2023-03-01' }
        ],
        bottomContent: [
          {
            label: '创建人',
            value: 'bbccdd' }
        ]
      },
      validRules: {
        name: [
          { required: true, message: '姓名必填' },
          { min: 3, max: 50, message: '名称长度在 3 到 50 个字符' }
        ],
        sex: [
          { required: true, message: '性别必须填写' },
          { pattern: /^[0,3]{1}$/, message: '格式不正确' }
        ]
      },
      tableData: [],
      // 表头
      columns: [
        {
          type: 'checkbox',
          width: 50
        },
        {
          type: 'seq',
          title: '序号'
        },
        {
          title: '用户编号',
          field: 'code',
          width: '20%'
        },
        {
          title: '姓名',
          field: 'name',
          slotName: 'name'
        },
        {
          title: '性别',
          field: 'sex',
          slotName: 'sex'
        },
        {
          title: '测试下拉',
          field: 'test',
          slotName: 'test'
        },
        {
          title: '出生日期',
          field: 'birthday',
          slotName: 'birthday'
        }
      ]
    }
  },
  mounted() {
    const _tableData = []
    for (let i = 0; i < 100; i++) {
      if (i % 2 === 0) {
        _tableData.push({
          code: 'asffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff',
          name: `name${i}`,
          sex: 0,
          test: 0,
          birthday: '1998-12-05'
        })
        if (i === 0) {
          _tableData[i].sex = 1
        }
      } else {
        _tableData.push({
          code: i,
          name: `name${i}`,
          sex: 0,
          test: 0,
          birthday: '1998-12-05'
        })
      }
    }
    this.tableData = _tableData
  },
  activated() {
  },
  methods: {
    test(data) {
      console.log(data)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
}
.app-inner-container {
  padding-top: 20px;
}
</style>

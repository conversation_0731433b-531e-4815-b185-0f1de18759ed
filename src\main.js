import Vue from 'vue'
import Cookies from 'js-cookie'
import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import Element from 'element-ui'
import 'vxe-table/lib/style.css'
import i18n from './i18n/index'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import '@/icons' // icon
import '@/permission' // permission control
import util from './utils/util'
import constant from './utils/constant'
import checkBtnPermission from '@/utils/btnPermission'
import * as tools from './utils/utilsCommon'
// 表格穿梭框
import Cascader from '@/components/ElComponents/cascader'
import CascaderPanel from '@/components/ElComponents/cascader-panel'
// 下拉框
import ElSelect from '@/components/ElComponents/select'
import ElEmpty from '@/components/empty'
import ElInputNumber from '@/components/ElComponents/input-number'
import ElFormItem from '@/components/ElComponents/form-item'
import ElInput from '@/components/ElComponents/input'
import Popover from '@/components/ElComponents/popover'
import ElScrollbar from '@/components/ElComponents/scrollbar'
import ElColorPicker from '@/components/ElComponents/color-picker'
import ElDatePicker from '@/components/ElComponents/date-picker'
import ElTimePicker from '@/components/ElComponents/time-picker'
import ElTimeSelect from '@/components/ElComponents/time-select'
import MyDrawer from '@/components/ElComponents/my-drawer/index'
import ElUpload from '@/components/ElComponents/upload'
import ElButton from '@/components/ElComponents/button'
import ElCheckBox from '@/components/ElComponents/checkbox'
import DatePicker from '@/components/SelectFormItem/DatePicker/data-date-picker'
import TimePicker from '@/components/SelectFormItem/DatePicker/data-time-picker'
import TimeSelect from '@/components/SelectFormItem/DatePicker/data-time-select'
import JCascader from '@/components/JCascader'

import math from '@/utils/math'

import './utils/searchHelper'

import XEUtils from 'xe-utils'
import { Export, Edit, VXETable, Column, Filter, Validator, Tooltip, Icon, Toolbar, Button } from 'vxe-table'
import VxeTable from '@/components/vxeTablerewrite'
import zhCN from 'vxe-table/lib/locale/lang/zh-CN'
import { toThousandFilter } from '@/utils/mathUtils'

import VueBarcode from "vue-barcode";

Vue.component('barcode', VueBarcode);
// 按需加载的方式默认是不带国际化的，自定义国际化需要自行解析占位符 '{0}'，例如：
VXETable.setup({
  i18n: (key, args) => XEUtils.toFormatString(XEUtils.get(zhCN, key), args)
})

import directive from '@/directives'
Vue.use(directive)
// 按需加载
// 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
import * as echarts from 'echarts/core'
// 引入图表，图表后缀都为 Chart
import {
  BarChart,
  LineChart,
  PieChart,
  MapChart,
  TreeChart,
  LinesChart,
  EffectScatterChart
} from 'echarts/charts'
import {
  ToolboxComponent,
  LegendComponent,
  DataZoomComponent,
  GraphicComponent,
  MarkAreaComponent,
  PolarComponent,
  MarkLineComponent
} from 'echarts/components'
// 引入提示框，标题，直角坐标系组件，组件后缀都为 Component
import {
  TitleComponent,
  TooltipComponent,
  GridComponent
} from 'echarts/components'
// 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
import { CanvasRenderer } from 'echarts/renderers'
// echarts注册必须的组件
echarts.use([
  [PolarComponent],
  [ToolboxComponent],
  [LegendComponent],
  [DataZoomComponent],
  [GraphicComponent],
  [MarkAreaComponent],
  [MarkLineComponent],
  [TreeChart],
  [LinesChart],
  [EffectScatterChart],
  TitleComponent,
  TooltipComponent,
  GridComponent,
  BarChart,
  LineChart,
  PieChart,
  MapChart,
  CanvasRenderer
])
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */

const { mockXHR } = require('../mock')
mockXHR()

Vue.prototype.$util = util
Vue.prototype.$constant = constant
Vue.prototype.$checkBtnPermission = checkBtnPermission
Vue.prototype.$echarts = echarts
Vue.prototype.$toThousandFilter = toThousandFilter
Vue.use(Filter)
Vue.use(Validator)
Vue.use(Export)
Vue.use(Edit)
Vue.use(Tooltip)
Vue.use(Column)
Vue.use(Icon)
Vue.use(Toolbar)
Vue.use(Button)
Vue.component('VxeTable', VxeTable)

// set ElementUI lang to EN
Vue.use(Element, {
  size: Cookies.get('size') || 'small',
  i18n: (key, value) => i18n.t(key, value)
})
Vue.use(ElScrollbar)
Vue.use(ElSelect)
Vue.use(ElInputNumber)
Vue.use(ElFormItem)
Vue.use(ElInput)
Vue.use(Popover)
Vue.use(CascaderPanel)
Vue.use(Cascader)
Vue.use(ElColorPicker)
Vue.use(ElDatePicker)
Vue.use(ElTimePicker)
Vue.use(ElTimeSelect)
Vue.use(DatePicker)
Vue.use(TimePicker)
Vue.use(TimeSelect)
Vue.use(ElUpload)
Vue.use(ElButton)
Vue.use(ElCheckBox)
Vue.use(MyDrawer)
Vue.use(JCascader)
Vue.use(ElEmpty)
Vue.config.productionTip = false
new Vue({
  el: '#app',
  i18n,
  router,
  store,
  render: h => h(App)
})
// 添加全局方法
Object.keys(tools).forEach((k) => {
  Vue.prototype[k] = tools[k]
})
// eslint-disable-next-line no-extend-native
Number.prototype.toFixed = util.toFixed

// eslint-disable-next-line no-extend-native
Number.prototype.add = function(num) {
  return math.number(math.add(math.bignumber(this), math.bignumber(num)))
}
// eslint-disable-next-line no-extend-native
Number.prototype.sub = function(num) {
  return math.number(math.subtract(math.bignumber(this), math.bignumber(num)))
}
// eslint-disable-next-line no-extend-native
Number.prototype.multiply = function(num) {
  return math.number(math.multiply(math.bignumber(this), math.bignumber(num)))
}
// eslint-disable-next-line no-extend-native
Number.prototype.divide = function(num) {
  return math.number(math.divide(math.bignumber(this), math.bignumber(num)))
}


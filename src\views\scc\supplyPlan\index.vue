<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="供货计划列表" name="first">
        <!-- 搜索框 -->
        <data-select
          :search-data.sync="search"
          :button-data="buttonData"
          @return-search="
            (data) => {
              searchHelper.search(data);
            }
          "
          @return-reset="searchHelper.reset"
        >
        </data-select>

        <data-table
          ref="dataTable"
          :table-data="searchHelper.dataList"
          :column="column"
          :pagination.sync="searchHelper.pagination"
          :table-option="tableOption1"
          @search-event="
            () => {
              searchHelper.handleQuery();
            }
          "
          @selection-change="handleSelectionChange"
        >
          <template v-slot:planStatus="{ row }">
            <el-tag v-if="row.planStatus == 1" type="warning">{{
              statusMap[row.planStatus]
            }}</el-tag>
            <el-tag v-if="row.planStatus == 2" type="">{{
              statusMap[row.planStatus]
            }}</el-tag>
            <el-tag v-if="row.planStatus == 3" type="success">{{
              statusMap[row.planStatus]
            }}</el-tag>
          </template>
          <template v-slot:remark="{ row }">
            <el-tooltip
              v-if="row.remarkSwitch"
              class="item"
              effect="dark"
              :content="row.remark"
              placement="top"
            >
              <span class="remark">{{ row.remark }}</span>
            </el-tooltip>
          </template>
        </data-table>
      </el-tab-pane>
      <el-tab-pane label="发货单列表" name="second">
        <data-select
          :search-data.sync="deliverySearch"
          :button-data="buttonData"
          @return-search="
            (data) => {
              receiptHelper.search(data);
            }
          "
          @return-reset="receiptHelper.reset"
        >
        </data-select>
        <data-table
          ref="deliveryTable"
          :table-data="receiptHelper.dataList"
          :column="deliveryColumn"
          :pagination.sync="receiptHelper.pagination"
          :table-option="tableOption"
          @search-event="
            () => {
              receiptHelper.handleQuery();
            }
          "
        >
          <template v-slot:status="{ row }">
            <el-tag v-if="row.status == 1" type="warning">{{
              deliveryStatus[row.status]
            }}</el-tag>
            <el-tag v-if="row.status == 2" type="">{{
              deliveryStatus[row.status]
            }}</el-tag>
            <el-tag v-if="row.status == 3" type="success">{{
              deliveryStatus[row.status]
            }}</el-tag>
          </template>
          <template v-slot:receiptRemark="{ row }">
            <el-tooltip
              v-if="row.receiptRemarkEnabled"
              class="item"
              effect="dark"
              :content="row.receiptRemark"
              placement="top"
            >
              <span class="remark">{{ row.receiptRemark }}</span>
            </el-tooltip>
          </template>
          <template v-slot:deliveryRemark="{ row }">
            <el-tooltip
              class="item"
              effect="dark"
              :content="row.deliveryRemark"
              placement="top"
            >
              <span class="remark">{{ row.deliveryRemark }}</span>
            </el-tooltip>
          </template>
        </data-table>
      </el-tab-pane>
    </el-tabs>
    <simple-data-dialog
      v-if="aoTuMationDialogVisible"
      title="请选择收货方"
      :show-close="true"
      :visible.sync="aoTuMationDialogVisible"
    >
      <el-form
        ref="companyForm"
        :model="basicFormModel"
        label-width="100px"
        :rules="rules"
      >
        <el-form-item label="收货方选择：" prop="entCode" style="width: 100%">
          <el-select
            v-model="basicFormModel.entCode"
            filterable
            remote
            reserve-keyword
            placeholder="请输入企业名称或前缀"
            :remote-method="remoteSearchCompany"
            :loading="searchLoading"
            @change="changeCompany($event)"
          >
            <el-option
              v-for="(item, index) in options"
              :key="index"
              :label="item.companyName"
              :value="item.entCode"
            >
              <div>
                <div>{{ item.companyName }}</div>
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    color: #8492a6;
                    font-size: 13px;


                 “：立刻脚后跟范德萨ASADSDFM,
                 -"
                >
                  <span>{{ item.entPrefix }}</span>
                  <span>{{ item.entCode }}</span>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-footer class="button-container">
        <!--        <el-button @click="aoTuMationDialogVisible=false">取消</el-button>-->
        <el-button type="primary" @click="submit">确定</el-button>
      </el-footer>
    </simple-data-dialog>
  </div>
</template>
<script>
import DataSelect from "@/components/DataSelect/index.vue";
import uploadFile from "@/components/DataDialog/uploadFileList.vue";
import DataTable from "@/components/DataTable/index.vue";
import SimpleDataDialog from "@/components/SimpleDataDialog/index.vue";
import accApi from "@/api/acc/acc";

export default {
  name: "SupplyPlan",
  components: { SimpleDataDialog, DataTable, uploadFile, DataSelect },
  data() {
    return {
      basicFormModel: {
        entCode: "",
        companyName: "",
        entPrefix: "",
      },
      options: [],
      searchLoading: false,
      tableOption: {
        option: {
          height: "calc(100vh - 250px)",
        },
      },
      rules: {
        entCode: [
          { required: true, message: "请选择收货方", trigger: "change" },
        ],
      },
      activeName: "first",
      statusMap: {
        1: "待发货",
        2: "已发货",
        3: "已收货",
      },
      deliveryStatus: {
        1: "待确认发货",
        2: "已确认发货",
        3: "对方已收货",
      },
      aoTuMationDialogVisible: false,
      value: "",
      search: {
        searchKey: {
          label: "客户名称/物料名称/关联发货单号",
          value: null,
          type: "input",
          option: {
            placeholder: "请输入客户名称/物料名称/关联发货单号",
          },
        },
        status: {
          label: "状态",
          value: null,
          type: "select",
          option: {
            clearable: false,
            selectOptions: [
              { value: "1", label: "待发货" },
              { value: "2", label: "已发货" },
              { value: "3", label: "已收货" },
            ],
            placeholder: "请选择状态",
          },
        },
        planDeliveryTime: {
          label: "计划交货时间",
          value: null,
          type: "date",
          option: {
            placeholder: "请选择计划交货时间",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
        },
      },
      deliverySearch: {
        searchKey: {
          label: "客户名称/发货单号",
          value: null,
          type: "input",
          option: {
            placeholder: "请输入客户名称/发货单号",
          },
        },
        status: {
          label: "状态",
          value: null,
          type: "select",
          option: {
            clearable: false,
            selectOptions: [
              { value: "1", label: "待确认发货" },
              { value: "2", label: "已确认发货" },
              { value: "3", label: "对方已收货" },
            ],
            placeholder: "请选择状态",
          },
        },
      },
      searchHelper: new this.$searchHelper({
        api: accApi.queryCooperationLList,
      }),
      receiptHelper: new this.$searchHelper({
        api: accApi.queryCooperationOrderLList,
      }),
      buttonData: [
        {
          label: "创建发货单",
          action: this.onAddClick,
          permission: "all",
        },
      ],
      column: {
        // 表头
        data: [
          {
            label: "序号",
            prop: "index",
            sortable: false,
          },
          {
            label: "计划交货日期",
            prop: "planDeliveryTime",
            sortable: false,
          },
          {
            label: "计划状态",
            prop: "planStatus",
            sortable: false,
            slotName: "planStatus",
          },
          {
            label: "计划编号",
            prop: "planCode",
            sortable: false,
          },
          {
            label: "物料名称（客）",
            prop: "materialName",
            sortable: false,
          },
          {
            label: "计划数量",
            prop: "planQuantity",
            sortable: false,
          },
          {
            label: "单位",
            prop: "unitName",
            sortable: false,
          },
          {
            label: "客户名称",
            prop: "customerName",
            sortable: false,
          },
          {
            label: "关联发货单号",
            prop: "deliveryOrderNo",
            sortable: false,
          },
          {
            label: "备注",
            prop: "remark",
            sortable: false,
            slotName: "remark",
          },
          {
            label: "发货作业人",
            prop: "deliveryOperator",
            sortable: false,
          },
          {
            label: "发货时间",
            prop: "deliveryTime",
            sortable: false,
          },
        ],
        operation: {
          label: "操作",
          width: "220px",
          data: (row) => {
            let button = [];
            if (row.planStatus === "1") {
              if (row.deliveryOrderNo) {
                button = [
                  {
                    label: "编辑发货单",
                    action: this.onEdit,
                    permission: "all",
                  },
                ];
              } else {
                button = [
                  {
                    label: "去创建发货单",
                    action: this.onHarvest,
                    permission: "all",
                  },
                ];
              }
            } else if (row.planStatus === "2") {
              button = [
                {
                  label: "详情",
                  action: this.onDetail,
                  permission: "all",
                },
              ];
            } else {
              button = [
                {
                  label: "详情",
                  action: this.onDetail,
                  permission: "all",
                },
                {
                  label: "删除",
                  action: this.onDelete,
                  permission: "all",
                },
              ];
            }
            return button;
          },
        },
      },
      tableOption1: {
        option: {
          height: "calc(100vh - 250px)",
          enableSelected: true, // 开启多选
        },
        event: {
          selectionChange: this.handleSelectionChange,
          // 事件处理...
        },
      },
      selectedData: [],
      deliveryColumn: {
        // 表头
        data: [
          {
            label: "序号",
            prop: "index",
            sortable: false,
          },
          {
            label: "发货单号",
            prop: "deliveryOrderNo",
            sortable: false,
          },
          {
            label: "单据状态",
            prop: "status",
            sortable: false,
            slotName: "status",
          },
          {
            label: "品类数",
            prop: "categoryCount",
            sortable: false,
          },
          {
            label: "客户名称",
            prop: "customerName",
            sortable: false,
          },
          {
            label: "收货地址",
            prop: "deliveryAddress",
            sortable: false,
          },
          {
            label: "发货方备注",
            prop: "deliveryRemark",
            sortable: false,
            slotName: "deliveryRemark",
          },
          {
            label: "收货方备注",
            prop: "receiptRemark",
            sortable: false,
            slotName: "receiptRemark",
          },
          {
            label: "创建人",
            prop: "createId",
            sortable: false,
          },
          {
            label: "创建时间",
            prop: "createTime",
            sortable: false,
          },
        ],
        operation: {
          label: "操作",
          width: "220px",
          data: (row) => {
            let button = [];
            if (row.status === "1") {
              button = [
                {
                  label: "下载发货单",
                  action: this.onDeliveryDownload,
                  permission: "all",
                },
                {
                  label: "确认发货",
                  action: this.onDeliveryConfirm,
                  permission: "all",
                },
                {
                  label: "详情",
                  action: this.onDeliveryDetail,
                  permission: "all",
                },
                {
                  label: "编辑",
                  action: this.onEdit,
                  permission: "all",
                },
                {
                  label: "删除",
                  action: this.onDeliveryDelete,
                  permission: "all",
                },
              ];
            } else if (row.status === "2") {
              button = [
                {
                  label: "下载发货单",
                  action: this.onDeliveryDownload,
                  permission: "all",
                },
                {
                  label: "详情",
                  action: this.onDeliveryDetail,
                  permission: "all",
                },
              ];
            } else {
              button = [
                {
                  label: "详情",
                  action: this.onDeliveryDetail,
                  permission: "all",
                },
                {
                  label: "删除",
                  action: this.onDeliveryDelete,
                  permission: "all",
                },
              ];
            }
            return button;
          },
        },
      },
    };
  },
  created() {
    this.activeName = this.$route.query.activeName || "first";
    if (this.activeName === "first") {
      this.searchHelper.handleQuery();
    } else {
      this.receiptHelper.handleQuery();
    }
  },
  methods: {
    // 远程搜索企业
    async remoteSearchCompany(query) {
      if (query !== "") {
        this.searchLoading = true;
        try {
          const res = await accApi.setSupplierInvisible({
            searchKey: query,
          });
          this.options = res.data || [];
        } catch (error) {
          console.error("搜索企业失败:", error);
          this.options = [];
        } finally {
          this.searchLoading = false;
        }
      } else {
        this.options = [];
      }
    },
    changeCompany(entCode) {
      const selectedCompany = this.options.find(
        (company) => company.entCode === entCode
      );
      if (selectedCompany) {
        this.basicFormModel.entPrefix = selectedCompany.entPrefix;
        this.basicFormModel.companyName = selectedCompany.companyName;
      }
    },
    handleClick() {
      if (this.activeName === "first") {
        this.searchHelper.handleQuery();
      } else {
        this.receiptHelper.handleQuery();
      }
      this.$router.replace({
        path: "supplyPlan",
        query: { activeName: this.activeName },
      });
    },
    onAddClick() {
      console.log("onAddClick", this.selectedData);
      if (this.selectedData.length === 0) {
        accApi.setSupplierInvisible({}).then((res) => {
          this.options = res.data;
          this.basicFormModel.entCode = "";
          this.aoTuMationDialogVisible = true;
        });
      } else {
        // 验证批量发货条件
        const validationResult = this.validateBatchDeliveryConditions(
          this.selectedData
        );

        if (validationResult.isValid) {
          // 符合批量发货条件，直接跳转到发货页面
          const firstItem = this.selectedData[0];
          // 提取所有选中计划的ID，用于批量发货
          const selectedPlanCodes = this.selectedData
            .map((item) => item.id)
            .join(",");

          this.$router.replace({
            path: "supplyPlanAdd",
            query: {
              action: "add",
              companyName: firstItem.customerName,
              entPrefix: firstItem.entPrefix,
              entCode: firstItem.enterpriseCreditCode,
              batchPlanCodes: selectedPlanCodes, // 传递批量选中的计划编号
              activeName: this.activeName,
            },
          });
        } else {
          // 不符合批量发货条件，显示错误信息
          this.$confirm(
            "不符合批量发货条件!<br>请确保是同一客户、待发货状态 <br>且未关联其他发货单的计划",
            "",
            {
              confirmButtonText: "确定",
              type: "warning",
              center: true,
              showCancelButton: false,
              dangerouslyUseHTMLString: true,
            }
          )
            .then(() => {})
            .catch(() => {});
        }
      }
    },

    // 验证批量发货条件
    validateBatchDeliveryConditions(selectedItems) {
      if (!selectedItems || selectedItems.length === 0) {
        return {
          isValid: false,
          message: "请先选择要发货的计划",
        };
      }

      // 检查是否为同一客户
      const firstCustomer = selectedItems[0];
      const customerInfo = {
        customerName: firstCustomer.customerName,
        entPrefix: firstCustomer.entPrefix,
        enterpriseCreditCode: firstCustomer.enterpriseCreditCode,
      };

      for (let i = 1; i < selectedItems.length; i++) {
        const item = selectedItems[i];
        if (
          item.customerName !== customerInfo.customerName ||
          item.entPrefix !== customerInfo.entPrefix ||
          item.enterpriseCreditCode !== customerInfo.enterpriseCreditCode
        ) {
          return {
            isValid: false,
            message: "所选计划必须属于同一客户",
          };
        }
      }

      // 检查是否都是待发货状态
      const nonPendingItems = selectedItems.filter(
        (item) => item.planStatus !== "1"
      );
      if (nonPendingItems.length > 0) {
        return {
          isValid: false,
          message: "所选计划必须都是待发货状态",
        };
      }

      // 检查是否已关联其他发货单
      const linkedItems = selectedItems.filter(
        (item) => item.deliveryOrderNo && item.deliveryOrderNo.trim() !== ""
      );
      if (linkedItems.length > 0) {
        return {
          isValid: false,
          message: "所选计划必须都是未关联其他发货单的计划，请取消选择后重试",
        };
      }

      return {
        isValid: true,
        message: "验证通过",
      };
    },
    onDetail(row) {
      this.$router.replace({
        path: "supplyPlanDetail",
        query: { action: "detail", id: row.id, activeName: this.activeName },
      });
    },
    onDelete(row) {
      this.$confirm("是否删除？", "提示", { type: "warning" }).then(() => {
        accApi.deleteDeliveryPlanList({ id: row.id }).then((res) => {
          this.$message({
            message: "删除成功",
            type: "success",
          });
          this.searchHelper.handleQuery();
        });
      });
    },
    onHarvest(row) {
      this.$router.replace({
        path: "supplyPlanAdd",
        query: {
          action: "add",
          companyName: row.customerName,
          entPrefix: row.entPrefix,
          entCode: row.enterpriseCreditCode,
          batchPlanCodes: row.id,
          activeName: this.activeName,
        },
      });
    },
    onEdit(row) {
      this.$router.replace({
        path: "supplyPlanAdd",
        query: {
          action: "edit",
          deliveryOrderNo: row.deliveryOrderNo,
          companyName: row.customerName,
          entPrefix: row.entPrefix,
          entCode: row.enterpriseCreditCode,
          activeName: this.activeName,
        },
      });
    },
    handleSelectionChange(val) {
      this.selectedData = val;
    },
    onDeliveryConfirm(row) {
      this.$router.replace({
        path: "supplyPlanAdd",
        query: {
          action: "confirm",
          deliveryOrderNo: row.deliveryOrderNo,
          companyName: row.customerName,
          entPrefix: row.entPrefix,
          entCode: row.enterpriseCreditCode,
          activeName: this.activeName,
        },
      });
    },
    onDeliveryDownload(row) {
      // 显示下载提示
      // this.$message({
      //   message: "正在生成发货单PDF，请稍候...",
      //   type: "info",
      // });

      // 生成自定义文件名：发货单号_客户名称_日期.pdf
      // const currentDate = new Date().toISOString().split("T")[0]; // YYYY-MM-DD格式
      const customerName = row.customerName;
      const fileName = `${customerName}_${
        row.deliveryOrderNo.split("/")[1]
      }.pdf`;

      // 直接调用文件流下载API
      this.downloadPDFStream(row.deliveryOrderNo, fileName);
    },

    // PDF文件流下载方法 - 直接从API获取文件流
    downloadPDFStream(deliveryOrderNo, fileName) {
      // 创建XMLHttpRequest对象
      const xhr = new XMLHttpRequest();
      const url = `/api/inner/acc/api/delivery/planCooperation/order/deliveryOrderNo/pdf?deliveryOrderNo=${deliveryOrderNo}`;
      xhr.open("GET", url, true);
      xhr.responseType = "blob"; // 设置响应类型为blob

      // 添加请求头（如果需要认证）
      const token = this.$store.getters.token;
      if (token) {
        xhr.setRequestHeader("X-RD-Request-APIToken", `${token}`);
      }

      // 处理下载完成
      xhr.onload = () => {
        console.log(xhr, "=================================xhr");

        if (xhr.status === 200) {
          try {
            // 创建blob对象
            const blob = new Blob([xhr.response], { type: "application/pdf" });

            // 检查浏览器兼容性
            if (window.navigator.msSaveOrOpenBlob) {
              // IE浏览器
              window.navigator.msSaveOrOpenBlob(blob, fileName);
            } else {
              // 现代浏览器
              const downloadUrl = window.URL.createObjectURL(blob);
              const link = document.createElement("a");
              link.href = downloadUrl;
              link.download = fileName;
              link.style.display = "none";

              // 添加到DOM并触发下载
              document.body.appendChild(link);
              link.click();

              // 清理资源
              setTimeout(() => {
                document.body.removeChild(link);
                window.URL.revokeObjectURL(downloadUrl);
              }, 100);
            }

            this.$message({
              message: "发货单下载成功",
              type: "success",
            });
          } catch (error) {
            console.error("文件下载处理失败:", error);
            this.$message({
              message: "文件下载处理失败，请重试",
              type: "error",
            });
          }
        } else {
          this.$message({
            message: `下载失败，服务器返回状态码: ${xhr.status}`,
            type: "error",
          });
        }
      };

      // 处理下载错误
      xhr.onerror = () => {
        this.$message({
          message: "下载发货单失败，请检查网络连接后重试",
          type: "error",
        });
      };

      // 处理超时
      xhr.ontimeout = () => {
        this.$message({
          message: "下载超时，请重试",
          type: "error",
        });
      };

      // 设置超时时间（30秒）
      xhr.timeout = 30000;

      // 发送请求
      xhr.send();
    },
    onDeliveryDetail(row) {
      this.$router.replace({
        path: "supplyDeliveryDetail",
        query: {
          deliveryOrderNo: row.deliveryOrderNo,
          activeName: this.activeName,
        },
      });
    },
    onDeliveryDelete(row) {
      this.$confirm("是否删除？", "提示", { type: "warning" }).then(() => {
        accApi
          .deleteDeliveryOrderList({ deliveryOrderNo: row.deliveryOrderNo })
          .then((res) => {
            this.$message({
              message: "删除成功",
              type: "success",
            });
            this.receiptHelper.handleQuery();
          });
      });
    },
    submit() {
      this.$refs.companyForm.validate((valid) => {
        if (valid) {
          this.$router.replace({
            path: "supplyPlanAdd",
            query: {
              action: "add",
              companyName: this.basicFormModel.companyName,
              entPrefix: this.basicFormModel.entPrefix,
              entCode: this.basicFormModel.entCode,
              activeName: this.activeName,
            },
          });
          this.aoTuMationDialogVisible = false;
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.el-select-dropdown__item {
  height: 66px;
}
.button-container {
  line-height: 60px;
  text-align: center;
}
.remark {
  display: inline-block;
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

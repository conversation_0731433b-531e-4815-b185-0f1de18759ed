<template>
  <div class="app-container">
    <el-container :style="{ height: '100%'}">
      <el-main>
        <template-design ref="design" />
      </el-main>
      <el-footer class="button-container">
        <el-button @click="back">返回</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </el-footer>
    </el-container>

  </div>
</template>

<script>

import TemplateDesign from '@/views/enterprise/productMg/productTemplate/template-design.vue'
import accApi from '@/api/acc/acc'

export default {
  name: 'ProductTemplateDetail',
  components: { TemplateDesign },
  data() {
    return {
      action: null,
      id: null,
      defaultData: [{ 'type': 'image', 'name': '图片', 'icon': 'el-icon-camera', 'props': { 'image': [{ 'fileId': '1836595465118916610', 'url': 'https://kyqyjd.kanion.com/acc/resource/2024/0919/20f63990-7630-11ef-d0f6-be8b1a725614.jpg' }] }, 'id': 8152 }, { 'type': 'productInfo', 'name': '产品属性', 'icon': 'el-icon-tickets', 'props': { 'crossProvincialFiling': true, 'bgImg': [{ 'fileId': '1836595542742900738', 'url': 'https://kyqyjd.kanion.com/acc/resource/2024/0919/2c2cdc60-7630-11ef-d0f6-be8b1a725614.png' }], 'showLogo': true, 'titleBgColor': '#EE4A25', 'titleTextColor': '#FFFFFF' }, 'id': 8450 }]
    }
  },
  mounted() {
    this.action = this.$route.query.action
    if (this.action === 'edit' || this.action === 'copy') {
      if (this.action === 'edit') {
        this.id = this.$route.query.id
      }
      this.getTemplate()
    } else if (this.action === 'add') {
      this.$refs.design.setConfig({ config: JSON.stringify(this.defaultData), name: null, productId: null })
    }
  },
  methods: {
    getTemplate() {
      accApi.getProductTemplate({ id: this.$route.query.id }).then(res => {
        const data = res.data
        if (this.action === 'copy') {
          data.name = null
        }
        this.$refs.design.setConfig(res.data)
      })
    },
    back() {
      this.$router.replace({ path: 'productTemplate' })
    },
    submit() {
      this.$refs.design.getConfig((design) => {
        const apiMapping = {
          edit: 'editProductTemplate',
          add: 'addProductTemplate',
          copy: 'addProductTemplate'
        }
        accApi[apiMapping[this.action]]({ ...design, id: this.id }).then((res) => {
          this.$message.success(res.message)
          this.back()
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .button-container {
    line-height: 60px;
    text-align: center;
  }
  .template-main {
    display: flex;
    height: 100%;
    div{
      border: 1px solid #ccc;
    }
  }
  .template-left, .template-right {
    flex: 0 0 430px;
  }
  .template-center {
    flex: 1;
    min-width: 350px;
  }
  .template-left, .template-center, .template-right {
    height: 100%;
  }
</style>

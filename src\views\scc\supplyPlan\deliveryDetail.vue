<template>
  <div class="app-container">
    <el-container :style="{ height: '100%' }">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item :name="1">
            <template slot="title">
              发货单详情<span class="title-code">{{
                deliveryFormModel.deliveryOrderNo
              }}</span>
            </template>

            <el-form
              ref="basic"
              label-position="top"
              :model="basicFormModel"
              :disabled="true"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="客户名称：" prop="customerName">
                    <el-input v-model="basicFormModel.customerName" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发货单状态：" prop="status">
                    <el-tag v-if="deliveryFormModel.status == 1" type="warning"
                      >待确认发货</el-tag
                    >
                    <el-tag v-if="deliveryFormModel.status == 2"
                      >已确认发货</el-tag
                    >
                    <el-tag v-if="deliveryFormModel.status == 3" type="success"
                      >对方已收货</el-tag
                    >
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>

          <el-collapse-item
            v-if="
              deliveryFormModel.status != 1 ||
              deliveryFormModel.details.length > 0
            "
            :name="2"
            title="明细信息"
          >
            <el-table
              :data="renderData"
              class="main-table"
              border
              style="width: 100%"
              :span-method="handleSpanMethod"
              :row-key="getRowKey"
            >
              <!-- 序号 -->
              <el-table-column label="序号" width="60">
                <template slot-scope="scope">
                  <span class="sequence-number">{{ scope.row.mainIndex }}</span>
                </template>
              </el-table-column>

              <!-- 计划编号 -->
              <el-table-column prop="planNo" label="计划编号" width="150">
                <template slot-scope="scope">
                  <span>{{ scope.row.planNo }}</span>
                </template>
              </el-table-column>

              <!-- 添加子项 -->
              <!-- <el-table-column label="添加子项" width="80">
                <template slot-scope="scope">
                  <el-button
                    type="primary"
                    class="add-sub-btn"
                    @click="addSubItem(scope.row.originalData)"
                  >
                    添加
                  </el-button>
                </template>
              </el-table-column> -->

              <!-- 计划交货时间 -->
              <el-table-column
                prop="planDeliveryTime"
                label="计划交货时间"
                width="120"
              >
                <template slot-scope="scope">
                  <span>{{ scope.row.planDeliveryTime }}</span>
                </template>
              </el-table-column>

              <!-- 物料名称 -->
              <el-table-column prop="materialName" label="物料名称(号)">
                <template slot-scope="scope">
                  <span>{{ scope.row.materialName }}</span>
                </template>
              </el-table-column>

              <!-- 计划数量 -->
              <el-table-column
                prop="planQuantity"
                label="计划数量(单位)"
                width="150"
              >
                <template slot-scope="scope">
                  <!-- {{ scope.row.unit }} -->
                  <span
                    >{{ scope.row.planQuantity }}{{ scope.row.unitName }}</span
                  >
                </template>
              </el-table-column>

              <!-- 实发数量 -->
              <el-table-column
                prop="actualQuantity"
                label="实发数量(单位)"
                width="160"
              >
                <template slot-scope="scope">
                  <div class="input-group">
                    <el-input
                      v-model="scope.row.actualQuantity"
                      class="quantity-input"
                      size="small"
                      placeholder=""
                      disabled
                    />
                    <div class="unit-text">{{ scope.row.unitName }}</div>
                  </div>
                </template>
              </el-table-column>
              <!-- 实收数量 -->
              <el-table-column
                prop="receiptQuantity"
                label="实收数量(单位)"
                width="180"
                v-if="deliveryFormModel.status == 3"
              >
                <template slot-scope="scope">
                  <div class="input-group">
                    <el-input
                      v-model="scope.row.receiptQuantity"
                      class="quantity-input"
                      size="small"
                      disabled
                      @input="
                        handleReceiptQuantityChange(
                          scope.$index,
                          scope.row,
                          $event
                        )
                      "
                    />
                    <div class="unit-text">{{ scope.row.unitName }}</div>
                  </div>
                </template>
              </el-table-column>
              <!-- 工业互联网标识码 -->
              <el-table-column
                prop="identificationCode"
                label="工业互联网标识码"
                min-width="200"
              >
                <template slot-scope="scope">
                  <span class="link-text">{{
                    scope.row.identificationCode
                  }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="herbProductName" label="药材产品名称">
              </el-table-column>

              <el-table-column prop="productionBatchNo" label="生产批号">
              </el-table-column>
            </el-table>
          </el-collapse-item>

          <el-collapse-item
            v-if="
              deliveryFormModel.status != 1 ||
              deliveryFormModel.details.length > 0
            "
            :name="3"
            title="发货信息"
          >
            <el-form
              ref="basic"
              label-position="top"
              :model="deliveryFormModel"
              :disabled="true"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="收货地址：" prop="deliveryAddress">
                    <el-input v-model="deliveryFormModel.deliveryAddress" />
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="运输类型：" prop="transportType">
                    <el-input v-model="deliveryFormModel.transportType" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="司机电话：" prop="driverPhone">
                    <el-input
                      v-model="deliveryFormModel.driverPhone"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="车牌号：" prop="licensePlate">
                    <el-input
                      v-model="deliveryFormModel.licensePlate"
                      placeholder=""
                      maxlength="20"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="remark" label="备注：">
                    <!-- <el-switch
                      v-model="isRemark"
                      active-text="备注给对方"
                      inactive-text=""
                    /> -->
                    <el-input
                      v-model="deliveryFormModel.deliveryRemark"
                      type="textarea"
                      :rows="3"
                      placeholder=""
                      maxlength="200"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="deliveryImage" label="发货凭证：">
                    <div class="image-list">
                      <el-image
                        v-for="(url, index) in deliveryFormModel.deliveryImages"
                        :key="index"
                        :src="url"
                        :preview-src-list="deliveryFormModel.deliveryImages"
                        style="width: 150px; height: 150px; margin-right: 10px"
                        fit="cover"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>

          <el-collapse-item
            v-if="deliveryFormModel.status == 3"
            :name="4"
            title="收货信息"
          >
            <el-form
              ref="deliveryWork"
              label-position="top"
              :model="deliveryWorkForm"
              :disabled="true"
            >
              <el-row v-if="deliveryWorkForm.receiptRemarkEnabled" :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="receiptRemark" label="备注：">
                    <!-- <el-switch
                      v-model="isRemark"
                      active-text="备注给对方"
                      inactive-text=""
                    /> -->
                    <el-input
                      v-model="deliveryWorkForm.receiptRemark"
                      type="textarea"
                      :rows="3"
                      placeholder=""
                      maxlength="200"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="receiveImages" label="收货凭证：">
                    <div class="image-list">
                      <el-image
                        v-for="(url, index) in deliveryWorkForm.receiveImages"
                        :key="index"
                        :src="url"
                        :preview-src-list="deliveryWorkForm.receiveImages"
                        style="width: 150px; height: 150px; margin-right: 10px"
                        fit="cover"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>
        </el-collapse>
      </el-main>
      <el-footer class="button-container">
        <el-button @click="back">返回</el-button>
        <!-- <el-button type="primary" @click="save">确认收货</el-button> -->
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import acc from "@/api/acc/acc";
export default {
  name: "DeliveryDetail",
  data() {
    return {
      activeName: "first",
      collapse: [1, 2, 3, 4],
      isRemark: true,
      basicFormModel: {
        customerPrefix: "",
        customerName: "",
        customerCreditCode: "",
      },
      tableData: [],
      deliveryFormModel: {
        deliveryAddress: "",
        transportType: "",
        transportTypeName: "",
        driverPhone: "",
        licensePlate: "",
        deliveryRemark: "",
        deliveryImages: [],
      },
      deliveryWorkForm: {
        receiptRemark: "",
        receiveImages: [],
        receiptRemarkEnabled: null,
      },
    };
  },
  created() {
    acc
      .queryDeliveryOrderDetail({
        deliveryOrderNo: this.$route.query.deliveryOrderNo,
      })
      .then((res) => {
        this.basicFormModel.customerName = res.data.customerName;
        this.basicFormModel.customerPrefix = res.data.customerPrefix;
        this.basicFormModel.customerCreditCode = res.data.customerCreditCode;
        this.deliveryFormModel = res.data;
        this.deliveryFormModel.transportType = this.getTransportTypeText(
          res.data.transportType
        );
        this.deliveryWorkForm.receiptRemark = res.data.receiptRemark;
        this.deliveryWorkForm.receiveImages = res.data.receiveImages;
        this.deliveryWorkForm.receiptRemarkEnabled =
          res.data.receiptRemarkEnabled;
        this.tableData = this.convertToFlatFormat(res.data.details);
        this.processTableData();
      });
  },
  methods: {
    // 为表格行生成唯一key，避免DOM复用导致数据错乱
    getRowKey(row) {
      return row.uniqueKey;
    },
    // 处理单元格合并
    handleSpanMethod({ row, columnIndex }) {
      // 需要合并的列索引：0-序号, 1-计划编号, 2-添加子项, 3-计划交货时间, 4-物料名称, 5-计划数量
      let mergeColumns = [];
      if (this.deliveryFormModel.status == 3) {
        mergeColumns = [0, 1, 2, 3, 4, 6];
      } else {
        mergeColumns = [0, 1, 2, 3, 4];
      }

      if (mergeColumns.includes(columnIndex)) {
        if (row.isFirstInGroup) {
          // 如果是组内第一行，返回合并的行数
          return {
            rowspan: row.groupSize,
            colspan: 1,
          };
        } else {
          // 如果不是组内第一行，隐藏该单元格
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }

      // 其他列不合并
      return {
        rowspan: 1,
        colspan: 1,
      };
    },
    // 获取运输类型文本
    getTransportTypeText(type) {
      const typeMap = {
        1: "自有车辆",
        2: "三方车辆",
      };
      return typeMap[type] || type || "";
    },
    // 获取运输类型文本
    getStatusText(type) {
      const typeMap = {
        1: "待确认发货",
        2: "已确认发货",
        3: "对方已收货",
      };
      return typeMap[type] || type || "";
    },
    // 处理表格数据，为合并单元格做准备
    async processTableData() {
      // const response = await acc.queryDeliveryPlanAll({
      //   planIds: this.selectedPlanId,
      // });
      // this.deliveryAddressList = response.data;
      // 保持原有顺序，不重新分组排序
      this.renderData = [];
      let mainIndex = 1;
      const planNumberIndexMap = {}; // 记录每个计划编号的主序号

      // 按原有顺序处理数据
      this.tableData.forEach((item, index) => {
        // 检查是否是该计划编号的第一次出现
        const isFirstInGroup = !(item.planNo in planNumberIndexMap);

        if (isFirstInGroup) {
          planNumberIndexMap[item.planNo] = mainIndex;
          mainIndex++;
        }

        // 计算该计划编号的组大小
        const groupSize = this.tableData.filter(
          (data) => data.planNo === item.planNo
        ).length;

        // 计算在组内的索引
        const groupIndex =
          this.tableData
            .slice(0, index + 1)
            .filter((data) => data.planNo === item.planNo).length - 1;

        this.renderData.push({
          ...item,
          uniqueKey: `${item.planNo}_${index}`, // 使用原始索引确保唯一性
          mainIndex: isFirstInGroup ? planNumberIndexMap[item.planNo] : "", // 只有第一行显示主序号
          isFirstInGroup: isFirstInGroup,
          groupSize: groupSize,
          groupIndex: groupIndex,
          originalIndex: index,
          originalData: item,
        });
      });
    },
    // 将二维数据格式转换为一维数组
    convertToFlatFormat(groupedArray) {
      if (!Array.isArray(groupedArray) || groupedArray.length === 0) {
        return [];
      }

      const flatArray = [];

      groupedArray.forEach((group) => {
        // 先计算该组所有actualQuantity的总和
        // let actualQuantityAll = 0;
        if (
          group.deliveryOrderHerbInfoVOList &&
          Array.isArray(group.deliveryOrderHerbInfoVOList)
        ) {
          // 第一遍循环：计算总和
          // group.deliveryOrderHerbInfoVOList.forEach((item) => {
          //   const quantity = Number(item.actualQuantity) || 0;
          //   actualQuantityAll += quantity;
          // });

          // 第二遍循环：构建数据，每个子项都使用相同的总和
          group.deliveryOrderHerbInfoVOList.forEach((item) => {
            flatArray.push({
              id: item.id || "",
              planNo: group.planNo || "",
              planDeliveryTime: group.planDeliveryTime || "",
              materialName: group.materialName || "",
              materialCode: group.materialCode || "",
              planQuantity: group.planQuantity || undefined,
              unit: item.unit || "",
              unitName: item.unitName || "",
              actualQuantity: item.actualQuantity || "", // 使用计算好的总和
              identificationCode: item.identificationCode || "",
              productName: group.materialName || "",
              herbProductName: item.herbProductName || "",
              herbProductCode: item.herbProductCode || "",
              productionBatchNo: item.productionBatchNo || "",
              deliveryOrderNo: item.deliveryOrderNo || "",
              receiptQuantity: group.receiptQuantity || "",
            });
          });
        }
      });

      return flatArray;
    },
    back() {
      this.$router.replace({
        path: "supplyPlan",
        query: { activeName: this.$route.query.activeName },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.button-container {
  line-height: 60px;
  text-align: center;
}
.title-code {
  color: #8a8989;
  font-size: 12px;
  margin-left: 20px;
}
.input-group {
  display: flex;
}
.unit-text {
  display: inline-block;
  width: 50px;
  line-height: 32px;
  margin-left: 10px;
}
.button-container {
  line-height: 60px;
  text-align: center;
}

// 表格样式优化
.main-table {
  .sequence-number {
    font-weight: bold;
    color: #409eff;
  }

  .add-sub-btn {
    font-size: 12px;
  }

  .link-text {
    color: #409eff;
    cursor: pointer;
    text-decoration: underline;

    &:hover {
      color: #66b1ff;
    }
  }

  // 合并单元格的边框处理
  ::v-deep .el-table__body tr td {
    border-right: 1px solid #ebeef5;
  }

  // 子项行的样式区分
  ::v-deep .el-table__body tr:not(:first-child) {
    background-color: #fafafa;
  }
}
</style>

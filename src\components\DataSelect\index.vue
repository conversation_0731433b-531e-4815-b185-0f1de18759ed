<template>
  <div class="search-container" :class="[{'show-nbsp':showNbsp}]">
    <div v-if="showHeaderBtn" class="flex-between">
      <div class="flex-between">
        <slot name="topTitle" />
        <div
          v-if="showNbsp"
          v-html="nbsp"
        />
        <template v-for="(item,key) in getShowSelectItem(innerFiliterData)">
          <el-date-picker
            v-if="item.type === 'j-date'"
            :key="key"
            :ref="'innerSearchDataLable_' + key"
            v-model="item.value"
            v-bind="item.option"
            use-slot
            @focus="setActiveTrue(key)"
            @change="()=>{setActiveFalse(key,'innerSearchDataLable_'+key)}"
          >
            <template v-slot:default>
              <data-button
                :button-class="getActiveClass(innerSearchDataLable[key],true)"
              >
                {{ innerSearchDataLable[key].label }}
                <i class="el-icon-caret-bottom" />
              </data-button>
            </template>
          </el-date-picker>
          <tree-picker
            v-else-if="item.type === 'tree'"
            :key="key"
            :ref="'innerSearchDataLable_' + key"
            v-model="item.value"
            :value-label="item.valueLabel"
            :tree-option="item.treeOption"
            v-bind="item.option"
            use-wrapper
            @update:valueLabel="(valueLabel) => item.valueLabel=valueLabel"
            @show="setInnerShow(key)"
            @after-enter="setActiveTrue(key)"
            @after-leave="setActiveFalse(key,'innerSearchDataLable_'+key)"
          >
            <template #wrapper>
              <data-button
                :button-class="getActiveClass(innerSearchDataLable[key],true)"
              >
                {{ innerSearchDataLable[key].label }}
                <i class="el-icon-caret-bottom" />
              </data-button>
            </template>
          </tree-picker>
          <j-cascader
            v-else-if="item.type === 'j-cascader'"
            :key="key"
            :ref="'innerSearchDataLable_' + key"
            v-model="item.value"
            :value-label.sync="item.valueLabel"
            use-wrapper
            v-bind="item.option"
            @show="setInnerShow(key)"
            @after-enter="setActiveTrue(key)"
            @after-leave="setActiveFalse(key,'innerSearchDataLable_'+key)"
          >
            <template #wrapper="{ on }">
              <data-button
                :button-class="getActiveClass(innerSearchDataLable[key],true)"
                v-on="on"
              >
                {{ innerSearchDataLable[key].label }}
                <i class="el-icon-caret-bottom" />
              </data-button>
            </template>
          </j-cascader>
          <div v-else-if="item.type === 'slotDialog'" :key="key">
            <slot :keyindex="key" :name="item.slotName" :item="item" />
            <data-button
              :key="item.slotName"
              :button-class="getActiveClass(innerSearchDataLable[key],true)"
              @click="slotDialogBtn($event,item)"
            >
              {{ innerSearchDataLable[key].label }}
              <i class="el-icon-caret-bottom" />
            </data-button>
          </div>
          <el-popover
            v-else-if="item.type !== 'cascader'"
            :key="key"
            :ref="'innerSearchDataLable_'+key"
            scroll-hide
            placement="bottom-start"
            trigger="click"
            :popper-class="'el-popper-owner max-height-1'"
            @show="setInnerShow(key)"
            @after-enter="setActiveTrue(key,'innerSearchDataLable_'+key+'popSelectCompon')"
            @after-leave="setActiveFalse(key,'innerSearchDataLable_'+key)"
          >
            <div class="notAdvancedFilter popover-search popover-search-no-buttom">
              <pop-data-select
                :ref="'innerSearchDataLable_'+key+'popSelectCompon'"
                :search-data="getSearchItem(item,key)"
                :is-simple="false"
                :rules="rules"
                :label-position="'left'"
                :is-show-lable="false"
                :show-small-buttom="true"
                :select-mult-line-check-box="true"
                :show-button="false"
                :inline="false"
                @return-reset="resetSelect"
                @return-search="(params)=>{search('innerSearchDataLable_'+key)}"
                @update-search-data-init="updateFormData"
                @update:search-data="updateInnerFilterData"
              >
                <template slot-scope="scope">
                  <slot v-if="!isDataTransfer" :name="scope.data.slotName" :item="scope.data" />
                  <slot v-if="isDataTransfer" :data="scope.data" />
                </template>
              </pop-data-select>
            </div>
            <data-button
              slot="reference"
              :button-class="getActiveClass(innerSearchDataLable[key],true)"
            >
              {{ innerSearchDataLable[key].label }}
              <i class="el-icon-caret-bottom" />
            </data-button>
          </el-popover>
          <div
            v-else-if="item.type === 'cascader'"
            :key="key"
            @show="setInnerShow(key)"
            @after-enter="setActiveTrue(key)"
            @after-leave="setActiveFalse(key,'innerSearchDataLable_'+key)"
          >
            <data-button
              slot="reference"
              :button-class="getActiveClass(innerSearchDataLable[key],true)"
            >
              {{ innerSearchDataLable[key].label }}
              <i class="el-icon-caret-bottom" />
            </data-button>
          </div>
        </template>
        <!-- 高级筛选 -->
        <el-popover
          v-if="showAdvancedFilter"
          ref="innerSearchDataLable_advancedFilter"
          scroll-hide
          placement="bottom-start"
          trigger="click"
          :popper-class="'el-popper-owner max-height-1 owner-filter'"
          @hide="doClose('popDataSelect')"
          @show="setInnerShow"
          @after-enter="setActiveTrue('advancedFilter','innerSearchDataLable_advancedFilter')"
          @after-leave="setActiveFalse('advancedFilter','innerSearchDataLable_advancedFilter')"
        >
          <el-scrollbar
            ref="scrollbar"
            tag="div"
            wrap-style="overflow-x:hidden;"
            class="ul-height"
          >
            <div :class="['popover-search', advancedFilter]">
              <pop-data-select
                ref="popDataSelect"
                :search-data.sync="innerFiliterData"
                :is-simple="false"
                :rules="rules"
                :inline="true"
                :auto="false"
                is-select
                :show-special-slot="true"
                :show-small-buttom="true"
                @update-search-data-init="updateFormData"
                @return-reset="resetSelectAdvancedFilter"
                @return-search="(params)=>{searchAdvancedFilter('innerSearchDataLable_advancedFilter')}"
              >
                <template slot-scope="scope">
                  <slot v-if="!isDataTransfer" :name="scope.data.slotName" :item="scope.data" />
                  <slot v-if="isDataTransfer" :data="scope.data" />
                </template>
              </pop-data-select>
            </div></el-scrollbar>
          <data-button
            slot="reference"
            :button-class="getActiveClass(innerSearchDataLable.advancedFilter,false)"
          >
            {{ innerSearchDataLable.advancedFilter.label }}
            <i class="el-icon-caret-bottom" />
          </data-button>
        </el-popover>
        <!-- 右侧操作按钮 -->
        <div class="el-query">
          <permission-btn
            ref="permissionBtn"
            :button-data="buttonData"
            :button-index="buttonIndex"
          >
            <template slot-scope="scope">
              <slot :name="scope.data.slotName" :item="scope.data" />
            </template>
          </permission-btn>
          <slot name="queryButton" />
        </div>
      </div>
    </div>

    <div v-if="showSearchValue && showSearchValue.length>0" class="searchCondition">
      <div
        v-for="(item,index) in showSearchValue"
        :key="index"
        class="searchConditionTerm"
        :title="item.label+':'+item.showValue"
      >
        <span
          class="searchConditionSpan overflowEllipsis"
          style="max-width: 100px;"
        >{{ item.label }}</span>
        <span style="margin-right: 5px; float: left;">:</span>
        <span class="searchContentSpan" style="max-width: 195px;">{{ item.showValue }}</span>
        <i v-if="!item.notShowClear" class="el-icon-circle-close" @click="clearFormData(item)" />
      </div>
      <div class="clearScreening" @click="clearAllFormData(false)">{{ clearText || this.$t('common.screeningConditions') }}</div>
    </div>
  </div>
</template>
<script>
import 'element-ui/lib/theme-chalk/base.css'
import popDataSelect from './popDataSelect'
import dataButton from './dataButton'
import VueEvent from '@/utils/vue-event'
import PermissionBtn from '@/components/PermissionBtn'
import TreePicker from '@/components/TreePicker/TreePicker'
import 'element-ui/lib/theme-chalk/display.css'
export default {
  name: 'DataSelect',
  componentName: 'DataSelect',
  components: {
    popDataSelect,
    PermissionBtn,
    dataButton,
    TreePicker
  },
  provide() {
    return {
      dataSelect: this
    }
  },
  props: {
    clearText: {
      type: String,
      default: null
    },
    searchData: { // 搜索条件
      type: Object,
      default: () => {
        return {}
      }
    },
    rules: { // 校验规则
      type: Object,
      default: null
    },
    buttonData: { // 右侧按钮
      type: Array,
      default: () => {
        return []
      }
    },
    buttonIndex: { // 从第几个按钮开始成为下拉框
      type: Number,
      default: 3
    },
    isDataTransfer: { // 不用关系
      type: Boolean,
      default: false
    },
    // 高级筛选动态类
    advancedFilter: {
      type: String,
      default: null
    },
    advancedFilterIndex: {
      type: Number,
      default: 5
    }
  },
  data() {
    return {
      defaultHeight: this.$constant.searchBtnHeight,
      defaultShowHeight: 0,
      size: 'medium',
      nbsp: this.$constant.nbsp,
      showHeaderBtn: true,
      innerSearchDataLable: {},
      showAdvancedFilter: false,
      updateInfo: {},
      oldData: {},
      fields: [],
      innerFiliterData: {}
    }
  },
  computed: {
    getSearchItem() {
      return (item, key) => {
        const newItem = {}
        newItem[key] = item
        return newItem
      }
    },
    getShowSelectItem() {
      return obj => {
        const innerSearchData = {}
        Object.keys(obj).forEach((key, index, array) => {
          if (index >= this.advancedFilterIndex) return
          innerSearchData[key] = obj[key]
        })
        return innerSearchData
      }
    },
    showSearchValue() {
      const returnDate = []
      const that = this
      Object.keys(this.searchData).forEach((key, index, array) => {
        let value = that.searchData[key].value
        value =
          that.searchData[key].valueKey?.length > 0 &&
          typeof value === 'function'
            ? value(that.searchData[key])
            : value
        if (Array.isArray(value) ? value.length !== 0 : value) {
          const item = that.$util.cloneDeep(that.searchData[key])
          item.key = key
          if (item.type === 'select' || item.type === 'radio') {
            const showValue = []
            if (item.option?.selectOptions || item.option?.radioList) {
              const isMul = item.value instanceof Array;
              (item.option.selectOptions || item.option.radioList).forEach(
                (value, index, array) => {
                  if (!isMul && item.value === value.value) { showValue.push(value.label) }
                  if (isMul && item.value.indexOf(value.value) > -1) { showValue.push(value.label) }
                }
              )
            }
            item.showValue = showValue.join(',')
          } else if (item.type === 'tree' || item.type === 'chosen' || item.type === 'j-cascader') {
            item.showValue = item.valueLabel
          } else if (item.type === 'slot') {
            item.showValue = item.selectLabels(item)
          } else if (typeof item.selectLabels === 'function') {
            item.showValue = item.selectLabels(item)
          } else {
            item.showValue = item.value
          }
          returnDate.push(item)
        }
      })
      return returnDate
    },
    getActiveClass() {
      return (val, flag) => {
        let classStr = ''
        if (val.isActive) classStr += 'btn-active'
        return classStr
      }
    },
    showNbsp() {
      return !this.$slots.topTitle && (this.buttonData.filter((item) => { return this.$checkBtnPermission(item.permission) }).length > 0 > 0) && Object.keys(this.searchData).length === 0
    }
  },
  watch: {
    showSearchValue(val) {
      this.$nextTick(() => {
        let height = this.defaultHeight
        if (val.length > 0) {
          height += document.getElementsByClassName('searchCondition')[0].offsetHeight + 4
        }
        if (!this.isDataTransfer) VueEvent.$emit('data.table.resize', height)
        else VueEvent.$emit('data.transfer.resize')
      })
    },
    searchData: {
      handler(val) {
        this.innerFiliterData = this.$util.cloneDeep(val)
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.setInnerSearchData(this.searchData)
    this.oldData = this.$util.cloneDeep(this.searchData)
    this.$on('el.dataSelect.addField', (field) => {
      if (field) {
        this.fields.push(field)
      }
    })
    this.$on('el.dataSelect.removeField', (field) => {
      this.fields.splice(this.fields.indexOf(field), 1)
    })
  },
  mounted() {
    this.enterKeyup()
  },
  destroyed() {
    // 销毁enter事件
    this.enterKeyupDestroyed()
  },
  methods: {
    doClose(ref) {
      this.$refs[ref].doClose()
    },
    $forceUpdateSearchData() {
      this.setInnerSearchData(this.searchData)
    },
    clearAllFormData(noSearch) {
      const that = this
      Object.keys(this.innerFiliterData).forEach((key, index, array) => {
        if (that.innerFiliterData[key].valueKey?.length > 0 && typeof that.innerFiliterData[key].value === 'function') {
          that.innerFiliterData[key].valueKey.forEach(value =>
            this.$set(that.innerFiliterData[key], value, Array.isArray(this.innerFiliterData[key][value]) ? [] : null)
          )
        } else that.$set(that.innerFiliterData[key], 'value', Array.isArray(this.innerFiliterData[key].value) ? [] : null)
      })

      this.$emit('update:search-data', this.innerFiliterData)
      if (!noSearch) {
        this.search()
      }
    },
    clearFormData(val) {
      if (val.valueKey?.length > 0 && typeof val.value === 'function') {
        val.valueKey.forEach(value =>
          this.$set(this.innerFiliterData[val.key], value, Array.isArray(this.innerFiliterData[val.key][value]) ? [] : null)
        )
      } else this.$set(this.innerFiliterData[val.key], 'value', Array.isArray(this.innerFiliterData[val.key].value) ? [] : null)
      this.$emit('update:search-data', this.innerFiliterData)
      this.search()
    },
    updateInnerFilterData(val) {
      if (!val) return
      Object.keys(val).forEach((key, index, array) => {
        this.innerFiliterData[key] = val[key]
      })
    },
    updateFormData(val) {
      this.updateFormData2()
      this.oldData = this.$util.cloneDeep(this.innerFiliterData)
    },
    updateFormData2(val) {
      this.updateInnerFilterData(val)
      this.$emit('update:search-data', this.innerFiliterData)
    },
    setInnerSearchData(val) {
      const that = this
      this.innerSearchDataLable = { length: 0 }
      let length = 1
      Object.keys(val).forEach((key, index, array) => {
        that.innerSearchDataLable[key] = {
          label: that.searchData[key].label,
          isActive: false,
          length: length
        }
        length++
        that.innerSearchDataLable.length++
      })
      // 放出高级搜索
      this.innerSearchDataLable.advancedFilter = {
        label: this.$t('common.advancedFilter'),
        isActive: false
      }
      if (this.innerSearchDataLable.length > this.advancedFilterIndex) {
        this.innerSearchDataLable.advancedFilter = {
          label: this.$t('common.advancedFilter'),
          isActive: false
        }
        this.showAdvancedFilter = true
      } else this.showAdvancedFilter = false
    },
    setInnerShow(key) {
      if (key) {
        this.innerFiliterData[key] = this.$util.cloneDeep(this.searchData[key])
      } else {
        this.$refs.popDataSelect.initFormData()
        this.innerFiliterData = this.$util.cloneDeep(this.searchData)
      }
      if (this.fields?.length > 0) {
        this.fields.forEach(field => field.resetSearch())
      }
    },
    setActiveTrue(key, popRef) {
      if (key !== 'advancedFilter' && popRef && this.$refs[popRef][0] && this.$refs[popRef][0].searchData[key].type === 'date') {
        this.$refs[popRef][0].$refs['fromItemRef2'].$refs[key][0].handleEnter()
      }
      this.$set(this.innerSearchDataLable[key], 'isActive', true)
      this.$forceUpdate()
      if (key !== 'advancedFilter') { this.oldData[key] = this.$util.cloneDeep(this.innerFiliterData[key]) }
    },
    resetValue() {
      this.innerFiliterData = this.$util.cloneDeep(this.oldData)
      this.$emit('update:search-data', this.innerFiliterData)
    },
    isObjectValueEqual(a, b) {
      // 取对象a和b的属性名
      var aProps = Object.keys(a)
      var bProps = Object.keys(b)
      // 判断属性名的length是否一致
      if (aProps.length !== bProps.length) {
        return false
      }
      // 循环取出属性名，再判断属性值是否一致
      for (var i = 0; i < aProps.length; i++) {
        var propName = aProps[i]
        if (a[propName] !== b[propName]) {
          return false
        }
      }
      return true
    },
    setActiveFalse(key, itemRef) {
      this.$set(this.innerSearchDataLable[key], 'isActive', false)
      if (key !== 'advancedFilter') {
        let value = this.innerFiliterData[key].value
        value = this.$util.cloneDeep(this.innerFiliterData[key].valueKey?.length > 0 && typeof value === 'function' ? value(this.innerFiliterData[key]) : value)
        let oldValue = this.oldData[key].value
        oldValue = this.$util.cloneDeep(this.oldData[key].valueKey?.length > 0 && typeof oldValue === 'function' ? oldValue(this.oldData[key]) : oldValue)
        this.updateFormData2()
        if (value && oldValue && typeof value === 'object' && typeof oldValue === 'object') {
          if (!this.isObjectValueEqual(value, oldValue)) this.$emit('return-search', this.getAllParams())
        } else if (value !== oldValue) {
          this.$emit('return-search', this.getAllParams())
        }
      }
      this.$forceUpdate()

      if (itemRef && this.$refs[itemRef][0] && typeof this.$refs[itemRef][0]['handleBlur'] === 'function') {
        this.$refs[itemRef][0].handleBlur()
      } else if (itemRef && this.$refs[itemRef] && typeof this.$refs[itemRef]['handleBlur'] === 'function') {
        this.$refs[itemRef].handleBlur()
      }
      if (
        key !== 'advancedFilter' &&
        itemRef &&
        this.$refs[itemRef + 'popSelectCompon'] &&
        this.$refs[itemRef + 'popSelectCompon'][0] &&
        this.$refs[itemRef + 'popSelectCompon'][0].searchData[key].type === 'date'
      ) {
        this.$refs[itemRef + 'popSelectCompon'][0].$refs['fromItemRef2'].$refs[key][0].handleLeave()
      }
    },
    // 查询
    searchAdvancedFilter(itemRef) {
      this.updateFormData2()
      this.$emit('return-search', this.getAllParams())
      if (itemRef) {
        if (this.$refs[itemRef][0]) this.$refs[itemRef][0].showPopper = false
        else if (this.$refs[itemRef]) this.$refs[itemRef].showPopper = false
      }
    },
    // 查询
    search(itemRef) {
      this.$emit('return-search', this.getAllParams())
      if (itemRef) {
        if (this.$refs[itemRef][0]) this.$refs[itemRef][0].showPopper = false
        else if (this.$refs[itemRef]) this.$refs[itemRef].showPopper = false
      }
    },
    // 获取所有参数{key:value}
    getAllParams() {
      const params = {}
      for (const key in this.innerFiliterData) {
        const value = this.innerFiliterData[key].value
        params[key] =
          typeof value === 'function'
            ? value(this.innerFiliterData[key])
            : value
      }
      return params
    },
    setFormData(val) {
      this.$refs.popDataSelect.setFormData(val)
    },
    resetSelect() {
      this.$emit('return-reset')
    },
    resetSelectAdvancedFilter() {
      this.updateFormData2()
      this.$emit('return-reset')
    },
    enterKey(event) {
      event.stopPropagation()
      const componentName = this.$options._componentTag
      if (componentName === 'data-select') {
        const code = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode
        if (code === 13) {
          Object.keys(this.$refs).forEach(key => {
            if (!key.includes('popSelectCompon') && key.includes('innerSearchDataLable_') && key !== 'innerSearchDataLable_advancedFilter') {
              const propKey = key.substring('innerSearchDataLable_'.length)
              const type = this.searchData[propKey].type
              if (type !== 'chosen' && type !== 'tree') {
                let propPopover
                if (this.$refs[key][0]) {
                  propPopover = this.$refs[key][0]
                } else {
                  propPopover = this.$refs[key]
                }
                propPopover.handleBlur()
              }
            }
          })
        }
      }
    },
    enterKeyupDestroyed() {
      document.removeEventListener('keyup', this.enterKey)
    },
    enterKeyup() {
      document.addEventListener('keyup', this.enterKey)
    },
    slotDialogBtn(e, item) {
      // 直接设置dialogVisible，会出现一种情况，在点击其他搜索条件，不关闭popver时，直接点击弹窗，弹窗关闭后，再点击高级筛选，弹框会一起显示。暂时没想到解决办法
      e.stopPropagation()
      this.$emit('slot-dialog-click', item)
      // this.$set(item, 'dialogVisible', !item.dialogVisible)
    }
  }
}
</script>
<style scoped>
.popover-search {
  position: relative;
  min-height: 80px;
  min-width: 300px;
  padding-bottom: 25px;
}
.popover-search-no-buttom {
  position: relative;
  min-height: 50px;
  min-width: 300px;
  padding-bottom: 0;
}
i {
  margin-left: 3px;
}
</style>
<style lang="scss">
@import "../../styles/variables";
.el-popper-owner {
  max-width: 60vw;
  overflow-y: auto;
  padding-bottom: 0;
}
.el-popper-owner.owner-filter {
  padding-bottom: 12px;
}
.btn-active {
  color: $primary-color;
  background-color: $primary-color-light-9;
}
.searchCondition {
  min-height: 20px;
  overflow: visible;
  width: 100%;
  clear: both;
  border-radius: 2px;
  .clearScreening {
    color: $primary-color;
    float: left;
    height: 20px;
    margin-left: 5px;
    line-height: 20px;
    font-size: 12px;
    margin-top: 5px;
    cursor: pointer;
  }
  .searchConditionTerm {
    margin: 0 10px 4px 0;
    max-width: 340px;
    overflow: hidden;
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 20px;
    float: left;
    padding: 5px 24px 5px 10px;
    color: $primary-color;
    font-size: 12px;
    position: relative;
    background-color: $primary-color-light-9;
    .searchConditionSpan {
      color: $primary-color;
      float: left;
    }
    .searchContentSpan {
      color: $primary-color;
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      float: left;
    }
  }
}
.overflowEllipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  display: inline-block;
  max-width: 100%;
  vertical-align: top;
}
.app-container .search-container.show-nbsp{
  padding-bottom: 10px;
}
</style>

<template>
  <div>
    <template v-for="(item,index) in formData">
      <el-form-item
        v-show="item.show&&item.type!=='slotDialog'"
        :ref="'elFormItem'+(item.for || index)"
        :key="index"
        :prop="index"
        :for="item.for"
        :label="isShowLable?item.label+'：':null"
        :rules="item.rules"
        :required="item.required"
        :class="{'el-form-item-width': !auto && !item.fullWidth && !item.width}"
        :style="{width:auto ? null : item.fullWidth ? 'calc(100%)':item.width?item.width: isSelect ? 'calc(50% - 10px)' : null}"
      >
        <el-input
          v-if="item.type=='input'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          @blur="(event) => {if(item&&item.event&&typeof item.event.blur == 'function') item.event.blur(event)}"
          @focus="(event) => {if(item&&item.event&&typeof item.event.focus == 'function') item.event.focus(event)}"
          @change="(value) => {if(item&&item.event&&typeof item.event.change == 'function') item.event.change(value)}"
          @clear="() => {if(item&&item.event&&typeof item.event.clear == 'function') item.event.clear()}"
        >
          <template v-if="item.option && item.option.prefix" slot="prefix"><span v-html="item.option.prefix" /></template>
          <template v-if="item.option && item.option.suffix" slot="suffix"><span v-html="item.option.suffix" /></template>
        </el-input>
        <el-input-number
          v-if="item.type=='number'"
          :ref="index"
          v-model="item.value"
          :class="['date-width',item.popperClass]"
          v-bind="item.option"
          @blur="(event) => {if(typeof item.event.blur == 'function') item.event.blur(event)}"
          @focus="(event) => {if(typeof item.event.focus == 'function') item.event.focus(event)}"
          @change="(value) => {if(typeof item.event.change == 'function') item.event.change(value)}"
        >
          <template v-if="item.option && item.option.prefix" slot="prefix"><span v-html="item.option.prefix" /></template>
          <template v-if="item.option && item.option.suffix" slot="suffix"><span v-html="item.option.suffix" /></template>
        </el-input-number>
        <!-- clearable -->
        <el-select
          v-if="item.type=='select'"
          :ref="index"
          v-model="item.value"
          :class="['date-width',item.popperClass]"
          v-bind="item.option"
          @change="(value) =>{changeSelect(value,item)}"
          @visible-change="(show) => {if (typeof item.event.visibleChange == 'function') item.event.visibleChange(show)}"
          @remove-tag="(tag) => {if (typeof item.event.removeTag == 'function') item.event.removeTag(tag)}"
          @clear="() => {if (typeof item.event.clear == 'function') item.event.clear()}"
          @blur="(event) => {if (typeof item.event.blur == 'function') item.event.blur(event)}"
          @focus="(event) => {if (typeof item.event.focus == 'function') item.event.focus(event)}"
        >
          <template v-if="item.option.multiple" v-slot:header>
            <div
              class="all-select-header el-select-dropdown__item"
              :class="{selected: allSelected(item.option.selectOptions,item.value)}"
              @click.stop="changeAll(item,allSelected(item.option.selectOptions,item.value))"
            >
              <span>{{ $t('common.allSelect') }}</span>
            </div>
          </template>
          <el-option
            v-for="(option,$index2) in item.option.selectOptions"
            :key="$index2"
            :label="option.label"
            :value="option.value"
            :disabled="option.disabled"
          >
            <slot
              v-if="item.slotName"
              :name="item.slotName"
              :$index="$index2"
              :row="option"
            />
          </el-option>
        </el-select>
        <chosen
          v-if="item.type === 'chosen'"
          :id="item.option.id"
          :ref="index"
          v-model="item.value"
          :value-label="item.valueLabel"
          :class="['date-width',item.popperClass]"
          v-bind="item.option"
          :options.sync="item.option.options"
          @change="(value) =>{if (typeof item.event.change == 'function') item.event.change(value)}"
          @visible-change="(show) => {if (typeof item.event.visibleChange == 'function') item.event.visibleChange(show)}"
          @remove-tag="(tag) => {if (typeof item.event.removeTag == 'function') item.event.removeTag(tag)}"
          @clear="() => {if (typeof item.event.clear == 'function') item.event.clear()}"
          @blur="(event) => {if (typeof item.event.blur == 'function') item.event.blur(event)}"
          @focus="(event) => {if (typeof item.event.focus == 'function') item.event.focus(event)}"
          @update:valueLabel="(valueLabel) => item.valueLabel=valueLabel"
        />
        <tree-picker
          v-if="item.type === 'tree'"
          v-model="item.value"
          :value-label="item.valueLabel"
          v-bind="item.option"
          :tree-option="item.treeOption"
          @update:valueLabel="(valueLabel) => item.valueLabel=valueLabel"
        />
        <el-date-picker
          v-if="item.type === 'date' || item.type === 'j-date'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          :class="['date-width',item.popperClass]"
          @change="(value) => {if (typeof item.event.change == 'function') item.event.change(value)}"
          @blur="() => {if (typeof item.event.blur == 'function') item.event.blur()}"
          @focus="() => {if (typeof item.event.focus == 'function') item.event.focus()}"
        />
        <el-time-picker
          v-if="item.type=='time'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          :class="['date-width',item.popperClass]"
          @change="(value) => {if (typeof item.event.change == 'function') item.event.change(value)}"
          @blur="() => {if (typeof item.event.blur == 'function') item.event.blur()}"
          @focus="() => {if (typeof item.event.focus == 'function') item.event.focus()}"
        />
        <el-time-select
          v-if="item.type=='time-select'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          :class="['date-width',item.popperClass]"
          @change="(value) => {if (typeof item.event.change == 'function') item.event.change(value)}"
          @blur="() => {if (typeof item.event.blur == 'function') item.event.blur()}"
          @focus="() => {if (typeof item.event.focus == 'function') item.event.focus()}"
        />
        <el-radio-group
          v-if="item.type=='radio'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          @change="(value)=>{if (typeof item.event.change == 'function' ) item.event.change(value)}"
        >
          <template v-if="!item.option.isButton">
            <el-radio
              v-for="(radioItem,radioIndex) in item.option.radioList"
              :key="radioIndex"
              v-model="item.value"
              v-bind="item.option"
            >{{ radioItem.name }}</el-radio> </template>
          <template v-if="item.option.isButton">
            <el-radio-button
              v-for="(radioItem,radioIndex) in item.option.radioList"
              :key="radioIndex"
              :label="radioItem.label"
              :disabled="radioItem.disabled"
              :name="radioItem.name"
            >{{ radioItem.name }}</el-radio-button>
          </template>
        </el-radio-group>
        <el-switch
          v-if="item.type=='switch'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          @change="(value) => {if (typeof item.event.change == 'function') item.event.change(value)}"
        />
        <j-cascader
          v-if="item.type === 'j-cascader'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          :value-label.sync="item.valueLabel"
          v-on="$listeners"
        />
        <el-cascader
          v-if="item.type=='cascader'"
          :ref="index"
          v-model="item.value"
          v-bind="item.option"
          :class="['date-width',item.popperClass]"
          @change="(value) => {if (typeof item.event.change == 'function') item.event.change(value)}"
          @expand-change="(value) => {if (typeof item.event.expandChange == 'function') item.event.expandChange(value)}"
          @blur="(value) => {if (typeof item.event.blur == 'function') item.event.blur(value)}"
          @focus="(value) => {if (typeof item.event.focus == 'function') item.event.focus(value)}"
          @visible-change="(value) => {if (typeof item.event.visibleChange == 'function') item.event.visibleChange(value)}"
          @remove-tag="(value) => {if (typeof item.event.removeTag == 'function') item.event.removeTag(value)}"
        />
        <!-- 上传图片 -->
        <upload
          v-if="item.type==='upload'"
          :ref="index"
          v-model="item.value"
          :param="item.option.param"
        />
        <slot
          v-if="item.type=='slot' && (!showSpecialSlot || item.notPop)"
          :data="item"
        />

        <template v-if="item.type=='slot' &&(showSpecialSlot && !item.notPop)">

          <el-popover
            :ref="'fadeInLinearFormItemRef'+index"
            placement="bottom-start"
            trigger="click"
            scroll-hide
            :transition="'fade-in-linear-form-item-'+index"
            :popper-class="'el-popper-owner max-height-1'"
            @click-out-side="doClose('fadeInLinearFormItemRef'+index)"
          >
            <div slot="reference" class="el-input el-input--small el-input--suffix">
              <input v-if="item.option&&item.inputId" :id="item.inputId" type="text" autocomplete="off" rows="2" readonly="readonly" :placeholder="item.option.placeholder" class="el-input__inner">
            </div>
            <slot
              :data="item"
            />
          </el-popover>
        </template>
        <slot v-if="item.appendRender" :data="item" />
      </el-form-item>
    </template>
    <slot name="end" />
  </div>
</template>
<script>
import Chosen from '@/components/Chosen/index'
import TreePicker from '@/components/TreePicker/TreePicker'
import upload from '@/components/DataDialog/upload'
import formItemMixins from '@/components/SelectFormItem/form-item-mixins'
import Clickoutside from 'element-ui/src/utils/clickoutside'
export default {
  name: 'FromItem',
  components: { upload, Chosen, TreePicker },
  directives: { Clickoutside },
  mixins: [formItemMixins]
}
</script>

<style lang="scss" scoped>
@import "~element-ui/packages/theme-chalk/src/mixins/mixins";
.el-popover{
  max-height: 50vh;
}
.date-width{
  width: 100%;
}
@include b(select-dropdown) {
  .all-select-header {
    margin: 3px 0;

&:hover {
   background-color: unset;
 }
&.selected {
  .el-checkbox__inner {
    background-color: $--checkbox-checked-background-color;
    border-color: $--checkbox-checked-input-border-color;

&::after {
   transform: rotate(45deg) scaleY(1);
 }
}
}
}
  & .el-select-dropdown__item {
    &.selected {
      .el-checkbox__inner {
        background-color: $--checkbox-checked-background-color;
        border-color: $--checkbox-checked-input-border-color;

        &::after {
          transform: rotate(45deg) scaleY(1);
        }
      }
    }
  }
}
</style>

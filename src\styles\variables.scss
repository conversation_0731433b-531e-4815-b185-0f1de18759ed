$primary-color: #004DA1;


$primary-color-light-1: mix(#FFFFFF, $primary-color, 10%) !default;
$primary-color-light-8: mix(#FFFFFF, $primary-color, 80%) !default;
$primary-color-light-9: mix(#FFFFFF, $primary-color, 90%) !default;
$primary-color-dark-3: mix(#000000, $primary-color, 30%) !default;


// header
$headerBg: #FFFFFF;
$headerHeight: 48px;
// sidebar
$leftMenuBg: $primary-color;
$leftMenuText: #FFFFFF;
$leftMenuWidth: 48px;

$menuText: #303133;
$menuActiveText: #303133;
$subMenuActiveText: #FFFFFF; //https://github.com/ElemeFE/element/issues/12951

$menuBg: #FFFFFF; //#304156;
$menuHover: #FFFFFF; //#263445;

$subMenuBg: #FFFFFF;
$subMenuHover: #0D6CE4;

$sideBarWidth: 208px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  headerBg: $headerBg;
  headerHeight: $headerHeight;
  leftMenuBg: $leftMenuBg;
  leftMenuText: $leftMenuText;
  leftMenuWidth: $leftMenuWidth;
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  primaryColor: $primary-color
}

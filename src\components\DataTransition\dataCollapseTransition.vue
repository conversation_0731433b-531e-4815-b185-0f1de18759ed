<template>
  <transition
    @before-enter="beforeEnterFnc"
    @enter="enterFnc"
    @after-enter="afterEnterFnc"

    @before-leave="beforeLeaveFnc"
    @leave="leaveFnc"
    @after-leave="afterLeaveFnc"
  >
    <slot />
  </transition>
</template>

<script>
export default {
  name: 'DataCollapseTransition',
  props: {
    beforeEnter: {
      type: Function,
      default: null
    },
    enter: {
      type: Function,
      default: null
    },
    afterEnter: {
      type: Function,
      default: null
    },
    beforeLeave: {
      type: Function,
      default: null
    },
    leave: {
      type: Function,
      default: null
    },
    afterLeave: {
      type: Function,
      default: null
    }
  },
  methods: {
    beforeEnterFnc(el) {
      this.$util.addClass(el, 'collapse-transition')
      if (!el.dataset) el.dataset = {}

      el.dataset.oldPaddingTop = el.style.paddingTop
      el.dataset.oldPaddingBottom = el.style.paddingBottom

      el.style.height = '0'
      el.style.paddingTop = 0
      el.style.paddingBottom = 0
      if (typeof this.beforeEnter === 'function') this.beforeEnter()
    },
    enterFnc(el, done) {
      el.dataset.oldOverflow = el.style.overflow
      if (el.scrollHeight !== 0) {
        el.style.height = el.scrollHeight + 'px'
        el.style.paddingTop = el.dataset.oldPaddingTop
        el.style.paddingBottom = el.dataset.oldPaddingBottom
      } else {
        el.style.height = ''
        el.style.paddingTop = el.dataset.oldPaddingTop
        el.style.paddingBottom = el.dataset.oldPaddingBottom
      }
      el.style.overflow = 'hidden'
      if (typeof this.enter === 'function') this.enter()
    },
    afterEnterFnc(el) {
      // for safari: remove class then reset height is necessary
      this.$util.removeClass(el, 'collapse-transition')
      el.style.height = ''
      el.style.overflow = el.dataset.oldOverflow
      if (typeof this.afterEnter === 'function') this.afterEnter()
    },
    beforeLeaveFnc(el) {
      if (!el.dataset) el.dataset = {}
      el.dataset.oldPaddingTop = el.style.paddingTop
      el.dataset.oldPaddingBottom = el.style.paddingBottom
      el.dataset.oldOverflow = el.style.overflow

      el.style.height = el.scrollHeight + 'px'
      el.style.overflow = 'hidden'
      if (typeof this.beforeLeave === 'function') this.beforeLeave()
    },
    leaveFnc(el, done) {
      if (el.scrollHeight !== 0) {
      // for safari: add class after set height, or it will jump to zero height suddenly, weired
        this.$util.addClass(el, 'collapse-transition')
        el.style.height = 0
        el.style.paddingTop = 0
        el.style.paddingBottom = 0
      }
      if (typeof this.leave === 'function') this.leave()
    },
    afterLeaveFnc(el) {
      this.$util.removeClass(el, 'collapse-transition')
      el.style.height = ''
      el.style.overflow = el.dataset.oldOverflow
      el.style.paddingTop = el.dataset.oldPaddingTop
      el.style.paddingBottom = el.dataset.oldPaddingBottom
      if (typeof this.afterLeave === 'function') this.afterLeave()
    }
  }
}
</script>

<style scoped>

</style>

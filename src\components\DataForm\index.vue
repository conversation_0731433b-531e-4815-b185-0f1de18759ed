<template>
  <el-form
    ref="form"
    :model="validatorDate"
    :rules="rules"
    :label-position="labelPosition"
    :inline="true"
  >
    <form-item
      ref="fromItemRef"
      :form-data="formData"
      :auto="auto"
      @get-data="getData"
      @update:form-data="updateFormData"
    >
      <template slot-scope="scope">
        <slot :data="scope.data" />
        <slot
          :name="scope.data.slotName"
          :$index="scope.$index"
          :data="scope.data"
        />
        <slot v-if="scope.data.appendRender" :data="scope.data" :name="scope.data.appendRender" />
      </template>
      <template slot="end">
        <slot name="end" />
      </template>
    </form-item>
  </el-form>
</template>
<script>
import formItem from '@/components/DataForm/formItem'
import VueEvent from '@/utils/vue-event'
export default {
  name: 'DataForm',
  components: { formItem },
  props: {
    formData: {
      type: Object,
      default: null
    },
    rules: {
      type: Object,
      default: null
    },
    showSpecialSlot: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    auto: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    labelPosition: {
      type: String,
      default: 'top'
    }
  },
  data() {
    return {
      validatorDate: {}
    }
  },
  create() {
    // this.$nextTick(() => {
    //   this.validatorDate = this.$refs.fromItemRef.getAllParams()
    // })
  },
  mounted() {
    this.$nextTick(() => {
      this.validatorDate = this.$refs.fromItemRef.getAllParams()
    })
  },
  methods: {
    updateFormData(data) {
      this.$emit('update:form-data', data)
      this.$nextTick(() => {
        VueEvent.$emit('data.table.resize')
      })
    },
    getData(data) {
      this.validatorDate = data
    },
    clearValidate() {
      this.$refs.form.clearValidate()
    },
    validate(callback) {
      let promise
      // if no callback, return promise
      if (typeof callback !== 'function' && window.Promise) {
        promise = this.$refs.form.validate(callback)
      } else {
        this.$refs.form.validate(valid => {
          if (valid && typeof callback === 'function') {
            callback(this.$refs.fromItemRef.getAllParams())
          }
        })
      }
      if (promise) {
        return promise
      }
    },
    getFormItemAllRefs() {
      return this.$refs.fromItemRef.getAllRefs()
    },
    getAllParams() {
      return this.$refs.fromItemRef.getAllParams()
    },
    setFormData(val) {
      return this.$refs.fromItemRef.setFormData(val)
    }
  }
}
</script>

<style scoped>

</style>

export default {
  name: 'query',
  data() {
    return {
      dataTableScrollTop: 0,
      dataTableBodyWrapper: null
    }
  },
  mounted() {
    this.dataTableBodyWrapper = this.$refs.dataTable.$el.querySelector('.el-table__body-wrapper')
  },
  activated() {
    setTimeout(() => {
      this.dataTableBodyWrapper.scrollTop = this.dataTableScrollTop
    })
    this.searchHelper && this.searchHelper.handleQuery()
  },
  beforeRouteLeave(to, from, next) {
    this.dataTableBodyWrapper && (this.dataTableScrollTop = this.dataTableBodyWrapper.scrollTop)
    next()
  }
}

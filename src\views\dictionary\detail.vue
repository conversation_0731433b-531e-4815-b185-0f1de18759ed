<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="(data) => {searchHelper.search(data)}"
      @return-reset="searchHelper.reset"
    />
    <!-- 表格 -->
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="() => {searchHelper.handleQuery()}"
    />
    <div class="app-inner-container bottom-button-container">
      <el-button @click="back">返回</el-button>
    </div>
    <simple-data-dialog
      v-if="dialogVisible"
      :title="['新增','编辑'][dialogType]"
      :visible="true"
      size="middle"
    >
      <el-form
        ref="detailForm"
        label-position="top"
        :model="detailForm"
        :rules="detailFormRules"
        :inline="true"
      >
        <el-form-item
          prop="code"
          label="字典编码："
          class="el-form-item-width"
        >
          <el-input v-model="detailForm.code" :disabled="dialogType===1" clearable :maxlength="5" placeholder="请输入字典编码" />
        </el-form-item>
        <el-form-item
          prop="name"
          label="字典名称："
          class="el-form-item-width"
        >
          <el-input v-model="detailForm.name" clearable :maxlength="50" placeholder="请输入字典名称" />
        </el-form-item>
        <el-form-item
          prop="sort"
          label="排序："
          class="el-form-item-width"
        >
          <el-input v-model="detailForm.sort" clearable :maxlength="50" placeholder="请输入排序" />
        </el-form-item>
      </el-form>
      <el-footer class="button-container">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="saveDetail(false)">保存</el-button>
      </el-footer>
    </simple-data-dialog>
  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import acc from '@/api/acc/acc'
import SimpleDataDialog from '@/components/SimpleDataDialog'

export default {
  name: 'DictMaintenanceDetail',
  components: {
    DataSelect,
    SimpleDataDialog,
    DataTable
  },
  data() {
    return {
      searchHelper: new this.$searchHelper({ api: acc.dictionaryZiListApi, handleSearchParams: this.handleSearchParams }),
      dialogVisible: false,
      dialogType: 0,
      detailForm: {},
      detailFormRules: {
        code: [
          { required: true, message: '请输入字典编码', trigger: 'blur' },
          {
            pattern: /^(0|[1-9][0-9]*)$/,
            message: `请输入正确数值！`,
            trigger: `blur`
          }
        ],
        name: [
          { required: true, message: '请输入字典名称', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' },
          {
            pattern: /^(0|[1-9][0-9]*)$/,
            message: `请输入正确数值！`,
            trigger: `blur`
          }
        ]
      },
      search: {
        code: {
          label: '字典编码',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入字典编码'
          }
        },
        name: {
          label: '字典名称',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入字典名称'
          }
        }
      },
      buttonData: [
        {
          label: '新增',
          action: this.onAddClick,
          permission: 'dictionary:add'
        }
      ],
      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            sortable: false
          },
          {
            label: '类型编码',
            prop: 'dictTypeCode',
            sortable: false
          },
          {
            label: '字典编码',
            prop: 'code',
            sortable: false
          },
          {
            label: '字典名称',
            prop: 'name',
            sortable: false
          },
          {
            label: '排序',
            prop: 'sort',
            sortable: false
          }
        ],
        // 操作
        operation: {
          label: '操作',
          width: '150px',
          data: [
            {
              label: '编辑',
              action: this.onEditClick,
              permission: 'dictionary:edit'
            }, {
              label: '删除',
              action: this.onDelClick,
              permission: 'dictionary:del'
            }
          ]
        }
      }
    }
  },
  mounted() {
    this.dictTypeId = this.$route.query.id
    console.log(this.dictTypeId, '6667')
    this.searchHelper.handleQuery()
  },
  methods: {
    // 表格查询条件
    handleSearchParams(params) {
      return Object.assign({}, params, { dictTypeId: this.dictTypeId })
    },
    // 投入明细表
    onAddClick() {
      this.detailForm = {}
      this.dialogType = 0
      this.dialogVisible = true
    },
    onEditClick(row) {
      this.dialogVisible = true
      this.dialogType = 1
      acc.detailDictionaryZiApi({ id: row.id }).then((res) => {
        this.detailForm = res.data
      })
    },
    onDelClick(row) {
      this.$confirm('请确认是否删除?', '警告').then(() => {
        acc.deleteByIdDictionaryZiApi({ id: row.id }).then(() => {
          this.$message.success('操作成功!')
          this.searchHelper.handleQuery()
        })
      })
    },
    cancel() {
      this.detailForm = {}
      this.dialogVisible = false
    },
    saveDetail() {
      this.$refs.detailForm.validate((valid) => {
        if (valid) {
          // 新增
          if (this.dialogType === 0) {
            acc.addDictionaryZiApi({ ...this.detailForm, dictTypeId: this.dictTypeId }).then(() => {
              this.$message.success('操作成功!')
              this.searchHelper.handleQuery()
              this.cancel()
            })
          } else {
            acc.editDictionaryZiApi({ ...this.detailForm, dictTypeId: this.dictTypeId }).then(() => {
              this.$message.success('操作成功!')
              this.searchHelper.handleQuery()
              this.cancel()
            })
          }
        }
      })
    },
    back() {
      this.$router.replace('dictMaintenance')
    }
  }
}
</script>

<style scoped lang="scss">
.button-container {
  line-height: 60px;
  text-align: center;
}
</style>

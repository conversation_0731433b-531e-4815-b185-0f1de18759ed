server {
    listen       80;
    server_name  localhost;
    client_max_body_size 40M;

    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        error_page 404 /index.html;
    }

#    location ^~/spd {
#        alias   /usr/share/nginx/html;
#        index  index.html index.htm;
#        error_page 404 /index.html;
#    }

    location /spd {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        error_page 404 /index.html;
        try_files $uri $uri/ @router;

    }

    location /static/res {
        alias  /opt/static/res/;
        autoindex on;
    }

    location @router{
           rewrite ^/(spd)/(.*)$ /$1/index.html last;
    }

    error_page   500 502 503 504 404 /50x.html;

    location = /50x.html {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        error_page 404 /index.html;
    }

 #   location /api {
 #       # proxy_pass http://123.206.222.218:32644/;
 #       # proxy_pass http://gateway-service.portal:8080/;
 #   }

}

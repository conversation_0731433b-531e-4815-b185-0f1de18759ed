<template>
  <div>
    <template v-for="(form, name) in formItem">
      <el-divider
        :key="name"
        class="divider-form"
        content-position="left"
      >
        <span class="span-divider">{{ form.formTitle }}</span>
      </el-divider>
      <data-form
        ref="form"
        :key="name"
        :auto="false"
        :rules="form.formRules"
        :form-data.sync="form.formData"
      />
    </template>
  </div>
</template>

<script>
import DataForm from '@/components/DataForm'
import accApi from '@/api/acc/acc'

export default {
  name: 'UserDetail',
  components: {
    DataForm
  },
  props: {
    emplCode: {
      type: String,
      default: null
    },
    guid: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formItem: {
        basic: {
          formTitle: this.$t('acc.userInfo.basicInfo'),
          formData: {
            emplCode: {
              type: 'input',
              label: this.$t('acc.userInfo.emplCode'),
              value: null,
              option: {
                disabled: false,
                placeholder: '请输入账号'
              }
            },
            userName: {
              type: 'input',
              label: this.$t('acc.userInfo.userName'),
              value: null,
              option: {
                placeholder: this.$t('common.enter') + this.$t('acc.userInfo.userName')
              }
            },
            mail: {
              type: 'input',
              label: this.$t('acc.userInfo.mail'),
              value: null,
              option: {
                placeholder: this.$t('common.enter') + this.$t('acc.userInfo.mail')
              }
            },
            phone: {
              type: 'input',
              label: this.$t('acc.userInfo.phone'),
              value: null,
              option: {
                placeholder: this.$t('common.enter') + this.$t('acc.userInfo.phone')
              }
            },
            // spaceCode: {
            //   type: 'chosen',
            //   label: '部门名称',
            //   value: null,
            //   option: {
            //     placeholder: '请选择部门名称',
            //     options: []
            //   }
            // },
            enterpriseWechatUserId: {
              type: 'input',
              label: '企业微信',
              value: null,
              option: {
                placeholder: '请输入企业微信'
              }
            }
          },
          formRules: {
            emplCode: [
              { required: true, message: '请输入账号', trigger: 'blur' },
              { validator: (rule, val, cb) => {
                const { valid, tip } = this.$util.validRule('code', val)
                if (valid) {
                  cb()
                } else {
                  cb(tip)
                }
              }, trigger: 'blur' }
            ], spaceCode: [
              { required: true, message: '请选择部门名称', trigger: 'blur' }
            ],
            userName: [
              { required: true, message: this.$t('common.enter') + this.$t('acc.userInfo.userName'), trigger: 'blur' }
            ],
            mail: [
              { type: 'email', message: this.$t('common.incorrectMailbox'), trigger: 'blur' }
            ],
            phone: [
              { validator: (rule, value, callback) => this.$util.validIpt(value, callback, 'fixTelAndTel', false), trigger: 'blur' }
            ]
          }
        },
        other: {
          formTitle: this.$t('acc.userInfo.otherInfo'),
          formData: {
            certificateId: {
              type: 'input',
              label: this.$t('acc.userInfo.identityId'),
              value: null,
              option: {
                placeholder: this.$t('common.enter') + this.$t('acc.userInfo.identityId')
              }
            },
            sex: {
              type: 'chosen',
              label: this.$t('acc.userInfo.sex'),
              value: null,
              option: {
                placeholder: this.$t('common.select') + this.$t('acc.userInfo.sex'),
                options: []
              }
            },
            birthdate: {
              type: 'date',
              label: this.$t('acc.userInfo.birthdate'),
              value: null,
              option: {
                placeholder: this.$t('common.select') + this.$t('acc.userInfo.birthdate')
              }
            }
          },
          formRules: {
            certificateId: [
              { validator: (rule, value, callback) => this.$util.validIpt(value, callback, 'numberLetters', false) }
            ]
          }
        }
      },
      formData: {
        userName: null,
        mail: null,
        phone: null
      }
    }
  },
  watch: {
    emplCode: {
      handler() {
        this.queryUserInit()
      },
      immediate: true
    }
  },
  created() {
  //   // this.$on('getUserDetail', this.getUserDetail)
    this.$on('submit', this.submit)
  //   this.$nextTick(() => {
  //     this.initFormData()
  //   })
  },
  mounted() {
    // this.queryUserInit()
  },
  methods: {
    queryUserInit() {
      this.initFormData()
      accApi.userInfo.queryUserInit().then(result => {
        this.formItem.other.formData.sex.option.options = [
          { code: 'F', codeName: '女' },
          { code: 'M', codeName: '男' }
        ]
        this.formItem.basic.formData.deptCode.option.selectOptions = result.data['deptList'].map(
          item => { return { value: item.code, label: item.codeName } }) || []
      })
      // accApi.departmentAllListApi().then((res) => {
      //   this.formItem.basic.formData.spaceCode.option.options = res.data.map((e) => {
      //     return {
      //       codeName: e.deptName,
      //       code: e.deptCode
      //     }
      //   })
      // })
    },
    initFormData() {
      Object.keys(this.formItem).forEach(formKey => {
        const formData = this.formItem[formKey]['formData']
        Object.keys(formData).forEach(prop => {
          formData[prop].value = null
        })
      })
      if (this.emplCode) {
        this.formItem.basic.formData.emplCode.option.disabled = true
        accApi.userInfo.queryUserDetail(this.emplCode).then(result => {
          const userDetail = result.data
          Object.keys(this.formItem).forEach(formKey => {
            const formData = this.formItem[formKey]['formData']
            Object.keys(formData).forEach(prop => {
              let value = userDetail[prop]
              if (value && prop === 'contractDateRange') {
                value = [userDetail['contractStartDate'], userDetail['contractEndDate']]
              }
              formData[prop].value = value
            })
          })
        })
      }
    },
    submit(callback) {
      let result = true
      let formNum = 1
      this.$refs.form.forEach(f => {
        f.$refs.form['validate'].apply(this, [
          (valid) => {
            if (result) {
              result = valid
              if (formNum === this.$refs.form.length && result) {
                this.saveUser(callback)
              }
            }
            formNum++
          }
        ])
      })
    },
    saveUser(callback) {
      const saveData = {}
      Object.keys(this.formItem).forEach(formKey => {
        const formData = this.formItem[formKey]['formData']
        Object.keys(formData).forEach(prop => {
          saveData[prop] = formData[prop].value
        })
      })
      const emplCode = saveData['emplCode']
      saveData.accountType = '2'
      if (!this.emplCode) {
        accApi.userInfo.add(saveData).then(result => {
          callback(result)
        })
      } else {
        accApi.userInfo.edit(emplCode, saveData).then(result => {
          callback(result)
        })
      }
    },
    remoteJobMethod(query, callback) {
      query['paramCheck'] = true
      accApi.jobInfoQueryList(query).then(result => {
        callback(result.data.list, result.data.total)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../styles/variables';

.span-divider {
  font-size: 16px;
  color: $subMenuHover;
}

.divider-form {
  width: calc(100% - 10px);
}
</style>

<template>
  <div class="app-container">
    <!--    &lt;!&ndash; 搜索框 &ndash;&gt;-->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="
        (data) => {
          searchHelper.search(data);
        }
      "
      @return-reset="searchHelper.reset"
    >
      <!--      <template #queryButton>-->
      <!--        <upload-file style="width: 130px" :on-change.sync="onChange" btn-text="批量导入产品信息" />-->
      <!--      </template>-->
    </data-select>
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="
        () => {
          searchHelper.handleQuery();
        }
      "
    >
      <template v-slot:syncStatus="{ row }">
        <el-tag v-if="row.syncStatus == 1" type="primary">{{
          statusMap[row.syncStatus]
        }}</el-tag>
        <el-tag v-if="row.syncStatus == 2" type="success">{{
          statusMap[row.syncStatus]
        }}</el-tag>
        <el-tag v-if="row.syncStatus == 3" type="danger">{{
          statusMap[row.syncStatus]
        }}</el-tag>
      </template>
    </data-table>
    <simple-data-dialog
      v-if="aoTuMationDialogVisible"
      title="自动化设置"
      :visible="true"
      size="small"
    >
      <el-form
        ref="aoTuMaTion"
        label-position="top"
        :model="aoTuMationFrom"
        :rules="aoTuMationFormRules"
        :inline="true"
      >
        <el-form-item
          prop="identificationRuleId"
          label="展示模版："
          style="width: 90%"
        >
          <el-select
            v-model="aoTuMationFrom.templateId"
            placeholder="请输入展示模版"
          >
            <el-option
              v-for="item in templateList"
              :key="item.value"
              v-bind="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="identificationRuleId"
          label="生码规则："
          style="width: 90%"
        >
          <el-select
            v-model="aoTuMationFrom.ruleId"
            placeholder="请输入自动标识规则"
          >
            <el-option
              v-for="item in ruleList"
              :key="item.value"
              v-bind="item"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <el-footer class="button-container">
        <el-button @click="aoTuMationDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAoToMation">保存</el-button>
      </el-footer>
    </simple-data-dialog>
  </div>
</template>

<script>
import DataSelect from "@/components/DataSelect/index.vue";
// import uploadFile from "@/components/DataDialog/uploadFileList.vue";
import DataTable from "@/components/DataTable/index.vue";
import accApi from "@/api/acc/acc";
import SimpleDataDialog from "@/components/SimpleDataDialog/index.vue";

export default {
  name: "MaterialList",
  components: { SimpleDataDialog, DataTable, DataSelect },
  data() {
    return {
      statusMap: {
        1: "已创建",
        2: "已同步",
        3: "同步异常",
      },
      search: {
        keyword: {
          label: "物料编码/物料名称",
          value: null,
          type: "input",
          option: {
            placeholder: "请输入物料编码/物料名称",
          },
        },
        syncStatus: {
          label: "状态",
          value: null,
          type: "select",
          option: {
            clearable: false,
            selectOptions: [
              { value: "1", label: "已创建" },
              { value: "2", label: "已同步" },
              { value: "3", label: "同步异常" },
            ],
            placeholder: "请选择同步状态",
          },
        },
      },
      buttonData: [
        {
          label: "添加",
          action: this.onAddClick,
          permission: "all",
        },
      ],
      searchHelper: new this.$searchHelper({
        api: accApi.queryMaterialList,
        handleSearchParams: this.handleSearchParams,
      }),
      column: {
        // 表头
        data: [
          {
            label: "序号",
            prop: "index",
            sortable: false,
          },
          {
            label: "物料编码",
            prop: "materialCode",
            sortable: false,
          },
          {
            label: "物料名称",
            prop: "materialName",
            sortable: false,
          },
          {
            label: "物料类型",
            prop: "materialTypeName",
            sortable: false,
          },
          {
            label: "物料组",
            prop: "materialGroupName",
            sortable: false,
          },
          {
            label: "单位",
            prop: "unitName",
            sortable: false,
          },
          {
            label: "规格",
            prop: "specification",
            sortable: false,
          },
          {
            label: "同步状态",
            prop: "syncStatus",
            sortable: false,
            slotName: "syncStatus",
          },
          {
            label: "物料描述",
            prop: "materialDescription",
            sortable: false,
          },
          {
            label: "备注",
            prop: "remark",
            sortable: false,
          },
        ],
        operation: {
          label: "操作",
          width: "220px",
          data: [
            // {
            //   label: '自动化设置',
            //   action: this.onAutomation,
            //   permission: 'all'
            // },
            {
              label: "详情",
              action: this.onDetail,
              permission: "all",
            },
            {
              label: "编辑",
              action: this.onEditClick,
              permission: "all",
            },
            {
              label: "删除",
              action: this.onDeleteClick,
              permission: "all",
            },
          ],
        },
      },
      aoTuMationDialogVisible: false,
      aoTuMationFrom: {
        templateId: null,
        ruleId: null,
      },
      aoTuMationFormRules: {
        templateId: [
          { required: true, message: "请选择展示模版", trigger: "change" },
        ],
        ruleId: [
          { required: true, message: "请选择生码规则", trigger: "change" },
        ],
      },
      templateList: [],
      ruleList: [],
    };
  },
  mounted() {
    this.searchHelper.handleQuery();
  },
  methods: {
    // 表格查询条件
    handleSearchParams(params) {
      return Object.assign({}, params);
    },
    onAutomation(row) {
      this.aoTuMationDialogVisible = true;
      this.aoTuMationFrom = row;
    },
    onDeleteClick(row) {
      this.$confirm("请确认是否删除?", "提示").then(() => {
        accApi.delMaterial({ id: row.id }).then((res) => {
          this.$message.success(res.message);
          this.searchHelper.handleQuery();
        });
      });
    },
    onEditClick(row) {
      this.$router.replace({
        path: "materialDetail",
        query: { action: "edit", id: row.id },
      });
    },
    onDetail(row) {
      this.$router.replace({
        path: "materialDetail",
        query: { action: "detail", id: row.id },
      });
    },
    onAddClick() {
      this.$router.replace({
        path: "materialDetail",
        query: { action: "add" },
      });
    },
    submitAoToMation() {},
  },
};
</script>
<style scoped lang="scss"></style>

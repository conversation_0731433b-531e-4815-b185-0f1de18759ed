<template>
  <div
    class="sidebar-logo-container"
    :class="{'collapse':collapse}"
  >
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        class="sidebar-logo-link"
        to="/"
      >
        <el-image v-if="logo" class="sidebar-logo" v-bind="imageProps" />
        <h1
          v-else
          class="sidebar-title"
        >{{ title }} </h1>
      </router-link>
      <router-link
        v-else
        key="expand"
        class="sidebar-logo-link"
        to="/"
      >
        <el-image v-if="logo" class="sidebar-logo" v-bind="imageProps" />
        <h1 class="sidebar-title">{{ title }} </h1>
      </router-link>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      title: '', // this.$t('common.system'),
      logo: 'https://wpimg.wallstcn.com/69a1c46c-eb1c-4b46-8bd4-e9e686ef5251.png',
      imageProps: {
        src: require('@/assets/icon/skc-small-logo.png'),
        fit: 'scale-down'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  min-width: 80px; //54
  height: 40px;
  line-height: 40px;
  background: #464C5A;
  text-align: center;
  padding: 0;
  overflow: hidden;
  float: left;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    display: inline-block;
    & .sidebar-logo {
      width: 50px; //20
      height: 24px; //20
      vertical-align: middle;
      margin-right: 11px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      // font-weight: 600;
      line-height: 50px;
      font-size: 16px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      padding: 4px;
      height: 100%;
      width: 100%;
    }
  }
}
</style>

<template>
  <div class="fullscreen-icon" @click="click">
    <i
      :title="isFullscreen ? '退出全屏' : '进入全屏'"
      class="el-icon-full-screen"
    />
  </div>
</template>

<script>
import { defaultfullScreen, toggleFullscreen, toggleHalfFullscreen, fullscreenChange, removeFullscreenChange } from '@/utils/fullscreen.js'

export default {
  name: 'FullscreenIcon',
  props: {
    eleHtml: { // 该对象里面有一个html，是进入全屏的元素。需要进入全屏的是原始html，只需要传入$refs,如果是组件，需要使用$refs[''].$el
      type: Object,
      default: () => {
        return {}
      }
    },
    fullType: { // half只遮罩当前页面元素，并不是真正意义的全屏
      type: String,
      default: 'all'
    }
  },
  data() {
    return {
      isFullscreen: false
    }
  },
  mounted() {
    this.fullType === 'all' && fullscreenChange(this.updataFullscreenStatus)
  },
  beforeDestroy() {
    this.destroy()
  },
  methods: {
    click() {
      this.fullType === 'half'
        ? toggleHalfFullscreen(this.eleHtml.html.$el || this.eleHtml.html, this.updataFullscreenStatus)
        : toggleFullscreen(this.eleHtml.html.$el || this.eleHtml.html)
    },
    init(element) {
      console.log(element)
      defaultfullScreen(element)
    },
    updataFullscreenStatus(isFullscreen) {
      console.log('isFullscreen', isFullscreen)

      this.isFullscreen = isFullscreen
      console.log('1' + this.isFullscreen)

      this.$emit('fullscreen-change', this.isFullscreen)
    },

    destroy() {
      removeFullscreenChange()
    }
  }
}
</script>

<style scoped>
.fullscreen-icon {
  cursor: pointer;
}
</style>

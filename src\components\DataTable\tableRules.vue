<template>
  <div
    ref="table_rules"
    :class="['el-form-item',{
               'is-error': validateState === 'error',
               'is-validating': validateState === 'validating',
               'is-success': validateState === 'success',
               'is-warning': validateState === 'warning',
               'is-required': isRequired || required
             },
             sizeClass ? 'el-form-item--' + sizeClass : ''
    ]"
  >

    <el-popover
      :disabled="disabled"
      placement="top"
      trigger="hover"
      :init-create="initCreate"
      :transition="'fade-in-linear-table-rules'"
      :popper-class="'is-'+validateState+'-'+popperClass"
      :content="validateMessage"
      :popper-options="popperOptions"
    >
      <slot slot="reference" />
    </el-popover>
  </div>
</template>
<script>
import AsyncValidator from '@/components/mixin/asyncValidator'
import emitter from 'element-ui/src/mixins/emitter'
import objectAssign from 'element-ui/src/utils/merge'
import { noop } from 'element-ui/src/utils/util'
export default {
  name: 'TableRules',

  componentName: 'ElFormItem',

  mixins: [emitter],

  inject: ['dataTable'],

  provide() {
    return {
      elFormItem: this
    }
  },

  props: {
    required: {
      type: Boolean,
      default: undefined
    },
    rules: {
      type: [Object, Array],
      default: null
    },
    prop: {
      type: String,
      default: null
    },
    error: {
      type: String,
      default: null
    },
    warning: {
      type: String,
      default: null
    },
    validateStatus: {
      type: String,
      default: null
    },
    showMessage: {
      type: Boolean,
      default: true
    },
    value: {
      type: [String, Number, Boolean, Array, Object, Function, Promise],
      default: null
    },
    index: {
      type: [String, Number],
      default: null
    },
    size: {
      type: String,
      default: null
    },
    initCreate: {
      type: Boolean,
      default: true
    },
    popperOptions: {
      type: Object,
      default: () => {
        return {}
      }
    },
    showErrorBackground: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      validateState: '',
      validateMessage: '',
      validateDisabled: false,
      validator: {},
      isNested: false,
      computedLabelWidth: '',
      popperClass: 'tooltip'
    }
  },
  computed: {
    isRequired() {
      const rules = this.getRules()
      let isRequired = false

      if (rules && rules.length) {
        rules.every(rule => {
          if (rule.required) {
            isRequired = true
            return false
          }
          return true
        })
      }
      return isRequired
    },
    elFormItemSize() {
      return this.size
    },
    sizeClass() {
      return this.elFormItemSize || (this.$ELEMENT || {}).size
    },
    disabled() {
      return !((this.validateState === 'error' || this.validateState === 'warning') && this.showMessage)
    }
  },
  watch: {
    error: {
      immediate: true,
      handler(value) {
        this.validateMessage = value
        this.validateState = value ? 'error' : ''
      }
    },
    warning: {
      immediate: true,
      handler(value) {
        this.validateMessage = value
        this.validateState = value ? 'warning' : ''
      }
    },
    validateStatus(value) {
      this.validateState = value
    }
  },
  mounted() {
    this.addValidateEvents()
  },
  beforeDestroy() {
    this.dispatch('DataTable', 'el.dataTable.removeField', [this])
  },
  methods: {
    validate(trigger, callback = noop, isEnd = false) {
      this.validateDisabled = false
      const that = this
      var isError = false
      let rules = this.rules
      if (!rules) {
        rules = {}
        rules[this.prop] = this.getFilteredRule(trigger)
      }
      Object.keys(rules).forEach((name, index, array) => {
        if (isError) return
        const rules = that.getFilteredRule(trigger, name)
        if ((!rules || rules.length === 0) && that.required === undefined) {
          callback()
          return true
        }

        that.validateState = 'validating'

        const descriptor = {}
        if (rules && rules.length > 0) {
          rules.forEach(rule => {
            delete rule.trigger
            rule.data = that.value
            rule.index = that.index
            rule.isEnd = isEnd
          })
        }
        descriptor[name] = rules

        const validator = new AsyncValidator(descriptor)
        const model = {}
        model[name] = that.value[name]?.toString() || null
        validator.validate(model, { firstFields: true }, (errors, invalidFields) => {
          let errorMessage = null
          let warningMessage = null
          const flag = !errors ? 0 : errors.every(val => {
            if (val.warning) {
              warningMessage = val.message
              return true
            }
            errorMessage = val.message
            return false
          }) ? 1 : 2
          that.validateState = flag === 0 ? 'success' : flag === 1 ? 'warning' : 'error'
          that.validateMessage = flag === 2 ? errorMessage : flag === 1 ? warningMessage : ''
          callback(flag === 1 ? null : that.validateMessage, invalidFields)
        })
        if (that.validateState === 'error') {
          isError = true
          if (trigger === '') {
            this.setElTableValidateColor(true)
          }
        } else {
          this.setElTableValidateColor(false)
        }
      })
    },
    setElTableValidateColor(validateError) {
      if (!this.showErrorBackground) {
        return
      }
      const row = this.$refs['table_rules']?.parentNode?.parentNode?.parentNode
      if (row) {
        const column = row.getElementsByTagName('td')
        for (const item of column) {
          if (item) {
            item.style.backgroundColor = validateError ? '#fadad2' : null
          }
        }
        // column.forEach((item) => {
        //   item.style.backgroundColor = validateError ? '#fadad2' : null
        // })
      }
    },
    clearValidate() {
      this.validateState = ''
      this.validateMessage = ''
      this.validateDisabled = false
    },
    getRules(name) {
      let selfRules = []
      if (name === undefined && this.rules instanceof Object && Object.keys(this.rules)?.length > 0) { selfRules = Object.values(this.rules)[0] } else if (name === undefined && this.rules instanceof Array) { selfRules = this.rules } else if (this.rules) selfRules = this.rules[name]
      const requiredRule = this.required !== undefined ? [{ required: !!this.required, message: this.$t('common.require') }] : []
      const warningRules = ((!selfRules ? null : Array.isArray(selfRules) ? selfRules : [selfRules]) || []).filter(value => value.warning || false)
      const normalRules = ((!selfRules ? null : Array.isArray(selfRules) ? selfRules : [selfRules]) || []).filter(value => !value.warning)
      return [].concat(normalRules, requiredRule || [], warningRules)
    },
    getFilteredRule(trigger, name) {
      const rules = this.getRules(name)

      return rules.filter(rule => {
        if (!rule.trigger || trigger === '') return true
        if (Array.isArray(rule.trigger)) {
          return rule.trigger.indexOf(trigger) > -1
        } else {
          return rule.trigger === trigger
        }
      }).map(rule => objectAssign({}, rule))
    },
    onFieldBlur() {
      this.validate('blur')
    },
    onFieldChange() {
      if (this.validateDisabled) {
        this.validateDisabled = false
        return
      }

      this.validate('change')
    },
    addValidateEvents() {
      const rules = this.getRules()
      if (rules.length > 0 || this.required !== undefined) {
        this.dispatch('DataTable', 'el.dataTable.addField', [this])
        this.$on('el.form.blur', this.onFieldBlur)
        this.$on('el.form.change', this.onFieldChange)
      }
    },
    removeValidateEvents() {
      this.$off()
    }
  }
}
</script>

<style scoped>
.el-form-item{
  margin-bottom: 0;
}
</style>
<style lang="scss">
@import "~element-ui/packages/theme-chalk/src/common/var.scss";
.is-error-tooltip{
  color: $--color-danger;
  padding-bottom: 10px;
  padding-top: 10px;
  /*box-shadow: 1px 3px 12px 0 rgba(0,0,0,.4);*/
}
.is-warning-tooltip{
  color: $--color-warning;
  padding-bottom: 10px;
  padding-top: 10px;
  /*box-shadow: 1px 3px 12px 0 rgba(0,0,0,.4);*/
}
</style>

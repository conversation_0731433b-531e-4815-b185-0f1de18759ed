<template>
  <div class="template-design-config">
    <div v-if="!this.$route.query.addType" class="config-title">
      基本信息
    </div>
    <el-form  ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="模板名称" prop="name" v-if="!this.$route.query.addType">
        <el-input v-model="form.name" placeholder="请输入模板名称" />
      </el-form-item>
      <el-form-item label="绑定产品" prop="productId" v-if="!this.$route.query.addType">
        <el-select v-model="form.productId" style="width: 100%" placeholder="请绑定产品" filterable @change="onProductChange">
          <el-option v-for="item in productList" :key="item.id" :value="item.id" :label="item.productName" />
        </el-select>
      </el-form-item></el-form>
    <div class="config-title">
      容器
    </div>
    <draggable
      class="container-drag-wrap"
      tag="ul"
      :list="containers"
      :group="{name: 'dragGroup', pull: onDragPull, put: false}"
      ghost-class="ghost"
      :sort="false"
      :clone="cloneProp"
    >
      <li
        v-for="(ctn, index) in containers"
        :key="index"
        class="container-item"
      >
        <i :class="ctn.icon" />
        <span>{{ ctn.name }}</span>
      </li>
    </draggable>
    <div class="config-title">
      显示
    </div>
    <div class="design-view">
      <div v-if="renderList.length === 0" class="no-widget-hint">请将容器拖动到此处</div>
      <draggable
        :list.sync="renderList"
        v-bind="{group:'dragGroup', ghostClass: 'ghost',animation: 300}"
      >
        <transition-group name="fade" tag="ul" class="design-view-list">
          <template v-for="(ctx, index) in renderList">
            <li
              :key="ctx.id"
              class="design-view-item"
              :class="{'view-active': active.id === ctx.id}"
              @click="handleClick(ctx)"
            >{{ ctx.name }}
              <i class="el-icon-delete" @click.stop="handleRemove(index)" />
            </li>
          </template>
        </transition-group>
      </draggable>
    </div>
  </div>
</template>

<script>
import Draggable from 'vuedraggable'
import { containers } from './template-design'
import { generateId } from 'element-ui/src/utils/util'
import { debounce } from 'lodash'
import accApi from '@/api/acc/acc'
export default {
  name: 'TemplateDesignConfig',
  components: {
    Draggable
  },
  inject: ['templateDesign'],
  data() {
    return {
      productList: [],
      active: this.templateDesign.active,
      renderList: this.templateDesign.renderList,
      containers,
      form: {
        name: '',
        productId: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入模板名称', trigger: 'change' }
        ],
        productId: [
          { required: true, message: '请绑定产品', trigger: 'change' }
        ]
      },
      debounceMsg: debounce(this.$message.warning, 500)
    }
  },
  watch: {
    'form.productId': {
      handler(val) {
        this.$emit('clickEvent', val)
      },
      deep: true
    }
  },
  mounted() {
    this.queryProductList()
  },
  methods: {
    queryProductList() {
      accApi.productList().then(res => {
        this.productList = res.data
      })
    },
    cloneProp(originProp) {
      const newProp = this.$util.cloneDeep(originProp)
      newProp.id = generateId()
      if (newProp.type === 'productInfo' && this.form.productId) {
        this.setProductInfo(newProp.props, this.form.productId)
      }
      if (newProp.type === 'productIntroduction' && this.form.productId) {
        this.setProductIntroduction(newProp.props, this.form.productId)
      }
      if (newProp.type === 'productBarCode' && this.form.productId) {
        this.setProductBarCode(newProp.props, this.form.productId)
      }
      if (newProp.type === 'productPrice' && this.form.productId) {
        this.setProductPrice(newProp.props, this.form.productId)
      }
      if (newProp.type === 'productImage' && this.form.productId) {
        this.setProductImage(newProp.props, this.form.productId)
      }
      return newProp
    },
    onProductChange(val) {
      const map = {
        productInfo: [],
        productIntroduction: [],
        productBarCode: [],
        productImage: [],
        productPrice: []
      }

      this.renderList.forEach(item => {
        if (Object.prototype.hasOwnProperty.call(map, item.type)) {
          map[item.type].push(item.props)
        }
      })

      Object.keys(map).forEach(key => {
        map[key].forEach(item => {
          this.setProductInfo(item, val)
          this.setProductIntroduction(item, val)
          this.setProductBarCode(item, val)
          this.setProductImage(item, val)
          this.setProductPrice(item, val)
        })
      })
    },
    setProductInfo(props, productId) {
      if (props && productId) {
        const productInfo = this.productList.find(item => item.id === productId)
        props.productInfo = productInfo.productAttrList
        props.beianList = productInfo.productBeianList
      }
    },
    setProductIntroduction(props, productId) {
      if (props && productId) {
        props.productIntroduction = this.productList.find(item => item.id === productId).remark
      }
    },
    setProductBarCode(props, productId) {
      if (props && productId) {
        props.productBarCode = this.productList.find(item => item.id === productId).barCode
      }
    },
    setProductPrice(props, productId) {
      if (props && productId) {
        props.productPrice = this.productList.find(item => item.id === productId).price
      }
    },
    setProductImage(props, productId) {
      if (props && productId) {
        props.productImage = this.productList.find(item => item.id === productId).productImgList
      }
    },
    onDragPull() {
      if (!this.$route.query.addType) {
        if (!this.form.name) {
          this.debounceMsg('请填写模板名称')
          return false
        }
        if (!this.form.productId) {
          this.debounceMsg('请绑定产品')
          return false
        }
      }
      return 'clone'
    },
    handleClick(ctx) {
      this.templateDesign.active.id = ctx.id
      this.templateDesign.active.type = ctx.type
      this.templateDesign.active.renderProps = ctx.props
    },
    handleRemove(index) {
      this.templateDesign.active.id = null
      this.templateDesign.active.type = null
      this.templateDesign.active.renderProps = null
      this.renderList.splice(index, 1)
    },
    getForm(callback) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          callback(this.form)
        }
      })
    },
    setForm(form) {
      Object.keys(this.form).forEach(key => {
        this.form[key] = form[key]
      })
    }
  }
}
</script>

<style scoped lang="scss">
.template-design-config {
  height: 100%;
}
.config-title {
  font-size: 14px;
  color: #666666;
  height: 48px;
  line-height: 48px;
}
.container-drag-wrap {
  padding: 10px 5px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 0;
  li{
    list-style: none;
  }
}
.container-item {
  width: 98px;
  text-align: left;
  height: 32px;
  line-height: 32px;
  cursor: move;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  background: #fff;
  border: 1px solid #e8e9eb;
  border-radius: 4px;
  padding: 0 8px;
  color: #303133;
  i {
    margin: 0 5px;
  }
  &:hover {
    background: #f1f2f3;
    border-color: #409eff;
    i {
      color: #409eff;
    }
  }
}
.design-view {
  position: relative;
}
.no-widget-hint {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 18px;
  color: #999999;
}
.design-view-list {
  min-height: 200px;
  padding: 10px 5px;
  margin: 0;
}
.design-view-item {
  cursor: move;
  list-style: none;
  padding: 10px 15px;
  color: #666;
  background-color: #f5f5f5;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  & +.design-view-item {
    margin-top: 10px;
  }
  .el-icon-delete {
    cursor: pointer;
    font-size: 18px;
  }
}
.view-active {
  color: #fff;
  background-color: #409eff;
}
</style>

<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="(data) => {searchHelper.search(data)}"
      @return-reset="searchHelper.reset"
    />
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="() => {searchHelper.handleQuery()}"
    />
    <!--新增编辑弹窗-->
    <data-dialog
      :dialog-visible.sync="dialogVisible"
      :dialog-data.sync="dialogData"
      :dialog-title="dialogTitle"
      :dialog-rule="dialogRule"
      :confirm-func="confirm"
    />
  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import acc from '@/api/acc/acc'
import DataDialog from '@/components/DataDialog/index.vue'
import accApi from '@/api/acc/acc'

export default {
  name: 'ProductBatch',
  components: { DataDialog, DataSelect, DataTable },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '添加产品批次信息',
      dialogData: {
        productId: {
          label: '产品',
          type: 'select',
          value: null,
          option: {
            selectOptions: [],
            filterable: true
          }
        },
        batchNo: {
          label: '批次编号',
          type: 'input',
          value: null
        },
        batchTitle: {
          label: '批次标题',
          type: 'input',
          value: null
        },
        batchResponorUser: {
          label: '负责人',
          type: 'input',
          value: null
        },
        batchDate: {
          label: '批次日期',
          type: 'date',
          value: null
        },
        warrantyPeriod: {
          label: '质保期',
          type: 'date',
          value: null,
          option: {
            type: 'daterange',
            startPlaceholder: '生产日期',
            endPlaceholder: '到期日期'
          }
        },
        remark: {
          label: '说明',
          type: 'input',
          value: null,
          fullWidth: true,
          option: {
            type: 'textarea'
          }
        }
      },
      dialogRule: {
        productId: {
          required: true,
          trigger: 'change',
          message: '请选择产品'
        },
        batchNo: {
          required: true,
          trigger: 'change',
          message: '请输入批次编号'
        },
        batchTitle: {
          required: true,
          trigger: 'change',
          message: '请输入批次标题'
        },
        batchResponorUser: {
          required: true,
          trigger: 'change',
          message: '请输入负责人'
        },
        batchDate: {
          required: true,
          trigger: 'change',
          message: '请选择批次日期'
        }
      },
      buttonData: [
        {
          label: '添加',
          action: this.onAddClick,
          permission: 'all'
        }
      ],
      searchHelper: new this.$searchHelper({ api: acc.productBatchPageListList }),
      dataList: [],
      search: {
        searchText: {
          label: '批次标题/产品名称',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入批次标题或产品名称'
          }
        }
      },
      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            sortable: false
          },
          {
            label: '批次编号',
            prop: 'batchNo',
            sortable: false
          },
          {
            label: '批次标题',
            prop: 'batchTitle',
            sortable: false
          },
          {
            label: '批次日期',
            prop: 'batchDate',
            sortable: false
          },
          {
            label: '负责人',
            prop: 'batchResponorUser',
            sortable: false
          },
          {
            label: '产品编号',
            prop: 'productNo',
            sortable: false
          },
          {
            label: '产品条码',
            prop: 'barCode',
            sortable: false
          },
          {
            label: '产品名称',
            prop: 'productName',
            sortable: false
          },
          {
            label: '关联编码数',
            prop: 'codeCount',
            sortable: false
          },
          {
            label: '质保期',
            prop: 'beyondShelfLifeDate',
            width: '200px',
            format: (row) => {
              return (row.beyondShelfLifeDate && row.productionDate) ? `${this.$util.formatTime(new Date(row.productionDate))} - ${this.$util.formatTime(new Date(row.beyondShelfLifeDate))}` : ''
            },
            sortable: false
          },
          {
            label: '说明',
            prop: 'remark',
            sortable: false
          },
          {
            label: '创建人',
            prop: 'createUserName',
            sortable: false
          }
        ],
        operation: {
          label: '操作',
          width: '80px',
          data: [
            {
              label: '删除',
              action: this.onDeleteClick,
              permission: 'all'
            }]
        }
      }
    }
  },
  mounted() {
    this.searchHelper.handleQuery()
    this.queryProductList()
  },
  methods: {
    onAddClick() {
      this.dialogVisible = true
      Object.keys(this.dialogData).forEach(key => {
        const item = this.dialogData[key]
        item.value = undefined
      })
    },
    onDeleteClick(row) {
      this.$confirm('请确认是否删除?', '提示').then(() => {
        accApi.deleteProductBatch(row).then(_ => {
          this.$message.success('删除成功')
          this.searchHelper.handleQuery()
        })
      })
    },
    confirm(data) {
      if (data.warrantyPeriod) {
        data.productionDate = this.$util.formatTime(data.warrantyPeriod[0])
        data.beyondShelfLifeDate = this.$util.formatTime(data.warrantyPeriod[1])
      }
      data.batchDate = this.$util.formatTime(data.batchDate)
      accApi.addProductBatch(data).then(_ => {
        this.$message.success('添加成功')
        this.searchHelper.handleQuery()
        this.dialogVisible = false
      })
    },
    queryProductList() {
      accApi.productList().then(res => {
        this.dialogData.productId.option.selectOptions = res.data.map(item => {
          return {
            label: item.productName,
            value: item.id
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
</style>

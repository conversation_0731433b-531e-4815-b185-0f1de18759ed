export const containers = [
  {
    type: 'title',
    name: '标题',
    icon: 'el-icon-star-off',
    props: {
      title: '',
      icon: [],
      bgColor: '#004DA1'
    }
  },
  {
    type: 'banner',
    name: '轮播图',
    icon: 'el-icon-picture-outline',
    props: {
      height: 200,
      images: []
    }
  },
  {
    type: 'productIntroduction',
    name: '产品介绍',
    icon: 'el-icon-document-copy',
    props: {
      productIntroduction: null
    }
  },
  {
    type: 'productInfo',
    name: '产品属性',
    icon: 'el-icon-tickets',
    props: {
      crossProvincialFiling: true,
      bgImg: [],
      productInfo: [],
      beianList: [],
      showLogo: true,
      titleBgColor: '#3C9599',
      titleTextColor: '#FFFFFF'
    }
  },
  {
    type: 'productBarCode',
    name: '产品条码',
    icon: 'el-icon-full-screen',
    props: {
      productBarCode: null
    }
  },
  {
    type: 'qualityGuaranteePeriod',
    name: '保质期',
    icon: 'el-icon-date',
    props: {
      qualityGuaranteePeriod: null
    }
  },
  {
    type: 'productPrice',
    name: '产品价格',
    icon: 'el-icon-price-tag',
    props: {
      productPrice: null
    }
  },
  {
    type: 'productImage',
    name: '产品图片',
    icon: 'el-icon-picture-outline-round',
    props: {
      productImage: []
    }
  },
  {
    type: 'identificationCode',
    name: '标识码',
    icon: 'el-icon-c-scale-to-original',
    props: {
      identificationCode: null
    }
  },
  {
    type: 'text',
    name: '文本',
    icon: 'el-icon-chat-square',
    props: {
      text: null
    }
  },
  {
    type: 'video',
    name: '视频',
    icon: 'el-icon-video-play',
    props: {
      videoUrl: null
    }
  },
  {
    type: 'image',
    name: '图片',
    icon: 'el-icon-camera',
    props: {
      image: [{ 'fileId': '1836595465118916610', 'url': 'https://kyqyjd.kanion.com/acc/resource/2024/0919/20f63990-7630-11ef-d0f6-be8b1a725614.jpg' }]
    }
  },
  {
    type: 'grid',
    name: '宫格',
    icon: 'el-icon-s-grid',
    props: {
      grid: [
        genGridStyleProps(1),
        genGridStyleProps(1),
        genGridStyleProps(1)
      ]
    }
  }

]

export const containerConfigBinds = {
  title: {
    title: {
      label: '标题',
      type: 'input',
      bind: {
        placeholder: '请输入标题'
      }
    },
    icon: {
      label: '图标',
      type: 'upload-img',
      bind: {
        limitCount: 1
      }
    },
    bgColor: {
      label: '背景颜色',
      type: 'color-picker',
      bind: {}
    }
  },
  banner: {
    height: {
      label: '高度',
      type: 'select',
      bind: {
        options: [
          {
            label: '200',
            value: '200'
          },
          {
            label: '220',
            value: '220'
          },
          {
            label: '250',
            value: '250'
          },
          {
            label: '280',
            value: '280'
          },
          {
            label: '300',
            value: '300'
          }
        ]
      }
    },
    images: {
      label: '轮播图',
      type: 'upload-img',
      bind: {}
    }
  },
  identificationCode: {},
  productIntroduction: {
  },
  productInfo: {
    showLogo: {
      label: 'logo',
      type: 'switch',
      bind: {
        activeText: '显示',
        inactiveText: '隐藏'
      }
    },
    crossProvincialFiling: {
      label: '跨省备案',
      type: 'switch',
      bind: {
        activeText: '显示',
        inactiveText: '隐藏'
      }
    },
    bgImg: {
      label: '背景图片',
      type: 'upload-img',
      bind: {
        limitCount: 1
      }
    },
    titleTextColor: {
      label: '标题颜色',
      type: 'color-picker',
      bind: {}
    },
    titleBgColor: {
      label: '标题背景颜色',
      type: 'color-picker',
      bind: {}
    }
  },
  productBarCode: {
  },
  qualityGuaranteePeriod: {
  },
  productPrice: {
  },
  productImage: {
  },
  text: {
    text: {
      label: '文本',
      type: 'input',
      bind: {
        type: 'textarea'
      }
    }
  },
  video: {
    videoUrl: {
      label: '视频地址',
      type: 'input',
      bind: { type: 'textarea' }
    }
  },
  image: {
    image: {
      label: '图片',
      type: 'upload-img',
      bind: {}
    }
  },
  grid: {
    grid: {
      type: 'grid',
      bind: {
        limitCount: 1
      }
    }
  }
}

export function genGridStyleProps(size) {
  return {
    size,
    title: null,
    url: null,
    isQrCode: false,
    textColor: '#FFFFFF',
    icon: 'el-icon-menu',
    bgType: true,
    bgColor: '#004DA1',
    bgImg: []
  }
}

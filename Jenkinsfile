pipeline {
  agent {
    node {
      label 'master'
    }
  }

  environment {
    APP_NAME = 'rydeen-spd-frontend-1-0-0'

    DOCKER_HUB_HOST = 'swr.cn-north-4.myhuaweicloud.com'

    DOCKER_HUB_NAMESPACE = 'rydeen/spd-projects'

    IMAGE_NAME = '${DOCKER_HUB_NAMESPACE}/${APP_NAME}'

    DATE = sh (returnStdout: true, script: 'date +%y%m%d%H%M%S').trim()
    SHORT = sh (returnStdout: true, script: 'echo ${GIT_COMMIT}|cut -c 1-8').trim()
    IMAGE_TAG = '${GIT_BRANCH}_${DATE}_${SHORT}'

  }

  stages {
    //   stage('SonarQube Analysis') {
    //     agent {
    //       docker {
    //         image '${DOCKER_HUB_HOST}/mcd-public/sonar-scanner-cli:latest'
    //         label 'master'
    //         args ''
    //         reuseNode 'true'
    //       }
    //     }
    //     steps {
    //       sh 'sonar-scanner \
    //             -Dsonar.projectKey=${DOCKER_HUB_NAMESPACE}-$APP_NAME \
    //             -Dsonar.sources=. \
    //             -Dsonar.host.url=${SONAR_HOST} \
    //             -Dsonar.login=${SONAR_AUTH_TOKEN}'
    //     }
    //   }

    stage('Npm Build') {
      agent {
        docker {
          image 'registry.saas.rydeen.com.cn:5000/rydeen-public/node:14.15.1'
          label 'master'
          args '-e HOME'
          reuseNode 'true'
        }
      }
      environment {
        HOME = '${JENKINS_HOME}'
      }
      steps {
        //  sh 'npm cache clean -f'
        sh 'npm install --verbose --registry https://registry.npm.taobao.org'
        sh 'npm run build:prod --verbose --registry https://registry.npm.taobao.org'
      }
    }

    stage('Docker Build'){
      steps {
        sh "docker build -t ${IMAGE_NAME} ."

        //sh 'docker tag ${IMAGE_NAME} ${DOCKER_HUB_HOST}/${IMAGE_NAME}:${IMAGE_TAG}'
        //sh 'docker push ${DOCKER_HUB_HOST}/${IMAGE_NAME}:${IMAGE_TAG}'

        // tag = branch_latest
        //sh 'docker tag ${IMAGE_NAME} ${DOCKER_HUB_HOST}/${IMAGE_NAME}:${GIT_BRANCH}_latest'
        //sh 'docker push ${DOCKER_HUB_HOST}/${IMAGE_NAME}:${GIT_BRANCH}_latest'

        // tag = latest
        sh "docker tag ${IMAGE_NAME} ${DOCKER_HUB_HOST}/${IMAGE_NAME}:latest"
        sh "docker push ${DOCKER_HUB_HOST}/${IMAGE_NAME}:latest"
      }
    }
    stage('deploy yaml'){
      steps {
        sh "ssh root@************ 'cd /opt/k8s-deployments/frontend-deployment/;./deploy_spd.sh'"
      }
    }
  }
}




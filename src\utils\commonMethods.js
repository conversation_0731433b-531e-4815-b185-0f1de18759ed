export function textRange(el) {
  const textContent = el
  const targetW = textContent.getBoundingClientRect().width
  const range = document.createRange()
  range.setStart(textContent, 0)
  range.setEnd(textContent, textContent.childNodes.length)
  const rangeWidth = range.getBoundingClientRect().width
  return rangeWidth > targetW
}

export function inputTextRange(el) {
  const textContent = el.getElementsByClassName('el-input__inner')[0]
  return textContent.scrollWidth > textContent.clientWidth
}


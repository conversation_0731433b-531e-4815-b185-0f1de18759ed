import axios from 'axios'
import { Loading, Message, MessageBox } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import Cookies from 'js-cookie'
import qs from 'qs'
import constant from './constant'
import i18n from '@/i18n'

const debug = process.env.NODE_ENV !== 'production'
let loading = null
let loadingCount = 0
// create an axios instance
axios.defaults.retry = 4
axios.defaults.retryDelay = 1000

// create an axios instance
const service = axios.create({
  baseURL: process.env.BASE_API, // url = base url + request url
  timeout: 100000, // request timeout
  paramsSerializer: params => qs.stringify(params, { arrayFormat: 'repeat' })
})

// request interceptor
service.interceptors.request.use(

  async config => {
    loadingCount++
    if (!(config?.params?.paramCheck || config?.data?.paramCheck)) {
      loading = Loading.service({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading'
      })
    }
    // 去掉cookies---
    const cookies = document.cookie.split(';')
    for (let i = 0; i < cookies.length; i++) {
      let cookie = cookies[i]
      while (cookie.charAt(0) === ' ') {
        cookie = cookie.substring(1)
      }
      const name = cookie.split('=')[0]
      document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;'
    }
    // 去掉cookies---
    const lang = Cookies.get('language') == null ? 'zh_CN' : Cookies.get('language') === 'cn' || Cookies.get('language') === 'zh' ? 'zh_CN' : 'en_US'
    // eslint-disable-next-line eqeqeq
    if (config.method === 'get' || config.method === 'delete') {
      // eslint-disable-next-line eqeqeq
      if (!config.params) {
        config.params = {}
      }
      // config.params['lang'] = lang
    } else {
      if (!config.data) {
        config.data = {}
      }
      if (typeof config.data === 'object') {
        // config.data['lang'] = lang
        config.data.responseType && (config.responseType = config.data.responseType)
      }
    }

    if (store.getters.token) {
      config.headers['X-RD-Request-APIToken'] = getToken()
      // config.headers['X-RD-Request-Language'] = lang
    }
    // 由于pageNum为0的时候，后台分页出错
    if (config?.params?.pageNum === 0) {
      config.params.pageNum = 1
    }
    if (config?.data?.pageNum === 0) {
      config.data.pageNum = 1
    }

    return config
  },
  error => {
    loadingCount = 0
    loading && loading.close()
    // do something with request error
    console.log(error) // for debug
    Promise.reject(error)
  }
)
// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    if (--loadingCount <= 0) {
      loadingCount = 0
    }

    if (loading != null && loadingCount === 0) {
      loading.close()
    }
    const res = response.data
    if (debug) {
      // console.log(res)
    }
    const constantErrorKey = constant.errorKeys
    // PARAM_CHECK 参数校验，1014导入Excel校验
    if (res.code && res.code !== 'SUCCESS' && res.code !== '200' && res.code !== 'PARAM_CHECK' && res.code !== '1014') {
      let message = null
      if (Object.keys(constantErrorKey).includes(res.code)) {
        message = constantErrorKey[res.code]
      }
      if (res.code !== 'warnMsg') {
        Message({
          message: message || res.msg,
          dangerouslyUseHTMLString: true,
          type: 'error',
          duration: 5000
        })
      }
      // 50008:非法的token; 50012:其他客户端登录了;  50014:Token 过期了;
      if (res.code === '401') {
        // 请自行在引入 MessageBox
        // import { Message, MessageBox } from 'element-ui'
        MessageBox.confirm(i18n.t('common.loginOutMessage'), i18n.t('common.loginOut'), {
          confirmButtonText: i18n.t('common.relogin'),
          cancelButtonText: i18n.t('common.cancelMessage'),
          type: 'warning'
        }).then(() => {
          store.dispatch('FedLogOut').then(() => {
            location.reload() // 为了重新实例化vue-router对象 避免bug
          })
        })
      } else if (res.code === '4001' || res.code === 'NO_LOGIN_ERROR') {
        store.dispatch('FedLogOut').then(() => {
          location.reload()
        })
      }

      return Promise.reject(res.code === 'warnMsg' ? res : 'error')
    } else if (res.code === 'PARAM_CHECK') {
      Message({
        message: res.msg,
        dangerouslyUseHTMLString: true,
        type: 'error',
        duration: 5000
      })
    } else if (response.config.responseType === 'blob') {
      if (res.type === 'application/json') {
        const fileReader = new FileReader()
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result)
          // 后台信息
          Message({
            message: jsonData.msg,
            dangerouslyUseHTMLString: true,
            type: 'error',
            duration: 5000
          })
        }
        fileReader.readAsText(res)
        return
      }
      const content = response.data
      const blob = new Blob([content]) // 构造一个blob对象来处理数据
      let responseFileName = response.headers['filename']
      if (responseFileName === undefined) {
        if (response.config.params.filename) {
          responseFileName = response.config.params.filename
          response.config.params.filename = responseFileName.substring(0, responseFileName.lastIndexOf('.'))
        }
      }
      // const fileName = response.config.params?.filename
      const fileName = response.config.params?.filename ? `${response.config.params.filename}.${responseFileName ? (responseFileName.substring(responseFileName.lastIndexOf('.') + 1, responseFileName.length)) : null}` : decodeURIComponent(response.headers['filename'])
      // 对于<a>标签，只有 Firefox 和 Chrome（内核） 支持 download 属性
      // IE10以上支持blob但是依然不支持download
      if (window.navigator.msSaveOrOpenBlob) {
        // 其他浏览器
        navigator.msSaveBlob(blob, fileName)
      } else {
        // 支持a标签download的浏览器
        const link = document.createElement('a') // 创建a标签
        link.download = fileName // a标签添加属性
        link.style.display = 'none'
        link.href = URL.createObjectURL(blob)
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click() // 执行下载
        URL.revokeObjectURL(link.href) // 释放url
        document.body.removeChild(link) // 释放标签
      }
      return
    } else {
      if (response.data?.data?.pageSize) {
        response.data.data.pagination = {
          pageNum: response.data.data.pageNum,
          pageSize: response.data.data.pageSize,
          total: response.data.data.total
        }
        if (response?.config?.params?.orderName) {
          response.data.data.pagination.orderName = response.config.params.orderName
          response.data.data.pagination.orderType = response.config.params.orderType
        }
      }
      return response.data
    }
  },
  error => {
    if (error && error.response) {
      switch (error.response.status) {
        case 401:
          Message.error(i18n.t('common.loginDisabledMessage'))
          break
        case 403:
          Message.error(i18n.t('common.loginForbidden'))
          break
        default:
          Message.error(i18n.t('common.loginTimeOut'))
          break
      }
    }
    loadingCount = 0
    loading && loading.close()
    return Promise.reject(error)
  }
)

export default service

<template>
  <div class="tree-table-transfer">
    <el-row>
      <el-col :gutter="20" :span="10">
        <div class="tobeSelectList">
          <div v-if="leftType == 'tree'" class="list-top">
            {{ $t('report.toSelectList') }}
          </div>
          <div
            v-if="leftType == 'tree'"
            class="left-tree"
            :style="{
              height: defaultRightTableOption.option.height
            }"
          >
            <tree-index
              ref="leftTreeIndex"
              :wrap-class="''"
              v-bind="defaultTreeOption"
              :tree-data="leftData"
              :tree-row.sync="treeRow"
              @treeRowChange="treeRowChange"
            />
          </div>
          <div v-else-if="leftType == 'table'">
            <div ref="leftContent">
              <data-select
                ref="leftSelect"
                :is-data-transfer="true"
                :search-data="leftSearchParams"
                @update:search-data="updateleftSearchParams"
                @return-search="leftSearchList"
                @return-reset="leftSearchReset"
              >
                <template slot-scope="scope">
                  <slot :name="scope.data.slotName" :item="scope.data" />
                </template>
              </data-select>
              <data-table
                ref="leftTable"
                :key="leftTableKey"
                :table-data="leftData"
                :column="leftTableColumn"
                :table-option="defaultLeftTableOption"
                :pagination.sync="leftTablePagination"
                @search-event="leftTableEvent"
              />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :gutter="20" :span="4">
        <div
          :style="{
            height: defaultRightTableOption.option.height,
            position: 'relative'
          }"
        >

          <div class="move-btn">
            <el-button
              icon="el-icon-arrow-right"
              type="primary"
              @click="dataToRight"
            />
            <el-button
              icon="el-icon-arrow-left"
              type="primary"
              @click="dataToLeft"
            />
          </div>
        </div>
      </el-col>
      <el-col :gutter="20" :span="10">
        <div class="selectList">
          <div class="list-top">{{ $t('report.selectList') }}</div>
          <data-table
            :key="rightTableKey"
            :table-data="rightTableData"
            :column="rightTableColumn"
            :table-option="defaultRightTableOption"
          />
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import treeIndex from '@/components/treeIndex'
import DataTable from '@/components/DataTable'
import DataSelect from '@/components/DataSelect'
export default {
  name: 'TreeTable',
  components: { treeIndex, DataTable, DataSelect },
  props: {
    leftType: {
      type: String,
      default: ''
    },
    treeOption: {
      type: Object,
      default: () => {
        return {}
      }
    },
    rightTableColumn: {
      type: Object,
      default: () => {
        return {
          data: [],
          opration: {}
        }
      }
    },
    rightTableOption: {
      type: Object,
      default: () => {
        return {}
      }
    },
    leftTableColumn: {
      type: Object,
      default: () => {
        return {
          data: [],
          opration: {}
        }
      }
    },
    leftTableOption: {
      type: Object,
      default: () => {
        return {}
      }
    },
    leftPagination: {
      type: Object,
      default: () => {
        return {}
      }
    },
    //   树的总数据
    leftData: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 左侧为表格时搜索条件
    leftSearchParams: {
      type: Object,
      default: () => {
        return {}
      }
    },
    rightTableData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      // 选中的树的数据
      treeRow: [],
      treeRowAll: [],

      leftTablePagination: {},
      //   选中的右侧表格的数据
      rightTableSelectList: [],
      //   选中的左侧表格的数据
      leftTableSelectList: [],
      leftTableKey: 0,
      rightTableKey: 0,
      defaultLeftHeight: {}

    }
  },
  computed: {
    defaultTreeOption() {
      return Object.assign({
        nodeKey: 'id',
        singleCheck: false,
        checkStrictly: false,
        defaultExpandAll: true,
        props: {
          label: 'name',
          children: 'children'
        }
      }, this.treeOption)
    },
    defaultLeftTableOption() {
      const option = Object.assign({
        rowKey: 'merchandiseCode',
        enableSelected: true,
        selectable: this.leftSelectable,
        reserveSelection: false,
        height: 'calc(100vh - 350px)'
      }, this.leftTableOption.option)
      const event = Object.assign({
        selectionChange: this.leftTableSelect
      }, this.leftTableOption.event)
      return { option: option, event: event }
    },
    defaultRightTableOption(val) {
      const option = Object.assign({}, {
        rowKey: 'id',
        enableSelected: true,
        height: 'calc(100vh - 320px)'
      }, this.rightTableOption.option)
      const event = Object.assign({ selectionChange: this.rightTableSelect }, this.rightTableOption.event)
      return { option: option, event: event }
    }
  },
  watch: {
    leftPagination: {
      handler(val) {
        this.leftTablePagination = val
      },
      deep: true
    }
  },
  created() {},
  mounted() {
    this.leftTablePagination = this.leftPagination
    this.$nextTick(() => {
      this.leftHeight()
    })
  },
  methods: {

    // 左侧为表格时搜索
    leftSearchList() {
      this.leftTableEvent()
    },
    // 左侧为表格时重置
    leftSearchReset() {
      this.leftTablePagination.pageNum = 1
      this.leftTableEvent()
    },
    leftTableEvent() {
      this.$emit('update:leftPagination', this.leftTablePagination)
      this.leftSearchEmit()
    },
    leftSearchEmit() {
      this.$emit('left-table-search', this.$refs.leftSelect.getAllParams())
      this.$nextTick(() => {
        this.$set(this.leftTableOption.option, 'height', this.defaultLeftHeight - this.$refs.leftSelect.$el.offsetHeight - 46)
      })
    },
    leftHeight() {
      this.$refs.leftContent && (this.defaultLeftHeight = this.$refs.leftContent.offsetHeight)
    },
    updateleftSearchParams(val) {

      this.$emit('update:left-search-params', val)
    },
    // 左侧树被选择时
    treeRowChange(treeSelect) {
    },
    // 左侧表格选择时
    leftTableSelect(leftTableSelectList) {
      this.leftTableSelectList = leftTableSelectList
    },
    // 右侧树选择时
    rightTableSelect(rightTableSelectList) {
      this.rightTableSelectList = rightTableSelectList
    },
    // 数据移入右边
    dataToRight() {
      let rightTableData = []
      switch (this.leftType) {
        case 'tree':
          // if (this.treeRow.length === 0) {
          //   this.$confirm(this.$t('common.selectAllMove'), this.$t('common.prompt'), {
          //     confirmButtonText: this.$t('common.ok'),
          //     cancelButtonText: this.$t('common.cancel'),
          //     type: 'warning'
          //   }).then(() => {
          //     this.$refs.leftTreeIndex.toggleSelect(this.leftData, true)
          //     console.log(this.treeRow)

          //     rightTableData = this.treeToRight(this.treeRow)
          //     this.updateRightTableData(this.rightTableData.concat([...rightTableData]))
          //     this.$refs.leftTreeIndex.toggleSelect(checkTree, false)
          //     this.treeRow = []
          //   })
          // } else {
          rightTableData = this.treeToRight(this.treeRow)
          this.updateRightTableData(this.rightTableData.concat([...rightTableData]))
          this.treeRow = []
          // }

          //    this.updateRightTableData([...rightTableData])
          break
        case 'table' :
          if (this.leftTableSelectList.length === 0) {
            this.$confirm(this.$t('common.selectAllMove'), this.$t('common.prompt'), {
              confirmButtonText: this.$t('common.ok'),
              cancelButtonText: this.$t('common.cancel'),
              type: 'warning'
            }).then(() => {
              // 全部右移
              this.leftData.map(left => {
                this.$set(left, 'disabled', true)
              })
              this.$emit('remove-all-to-right', this.rightTableData)
              this.leftTableSelectList = []
            })
          } else {
            rightTableData = this.tableToRight()
            this.updateRightTableData(this.rightTableData.concat([...rightTableData]))
            this.leftTableSelectList = []
          }

          break
        default :
          break
      }
    },
    updateRightTableData(rightTableData) {
      this.$emit('update:right-table-data', rightTableData)
    },
    // 树移入右边时
    treeToRight() {
      const tablist = []
      this.treeRow.map(tree => {
        this.$set(tree, 'disabled', true)
        if (!tree.children || tree.children.length === 0) {
        //   const item = this.rightTableData.find(b => b.id === tree.id)
        //   !item &&
          tablist.push(tree)
        }
      })

      return tablist
    },
    // // 树设置禁止选择
    disableTree() {
      this.$nextTick(() => {
        this.$refs.leftTreeIndex.toggleSelect(this.rightTableData, true)
        this.treeRow.map(item => {
          this.$set(item, 'disabled', true)
        })
        this.$refs.leftTreeIndex.toggleSelect(this.rightTableData, false)
      })
    },
    // 左侧表格禁止选择
    disabledArr(arr1) {
      const arr = this.compairArr2(arr1, this.rightTableData, this.defaultLeftTableOption.option.rowKey)
      //   this.$refs.leftTable.toggleRowSelection(arr, true)

      arr.map(item => {
        this.$set(item, 'disabled', true)
      })
    },
    clearTreeSelect() {
      this.$refs.leftTreeIndex.treeSelectRest()
    },
    clearLeftTableSelect() {
      this.$refs.leftTable.clearSelection()
    },
    // 表格移入右边是
    tableToRight() {
      const rightTableData = []
      this.leftTableSelectList.map(item => {
        this.$set(item, 'disabled', true)
        !this.rightTableData.find(t => t[this.defaultRightTableOption.option.rowKey] === item[this.defaultRightTableOption.option.rowKey]) && rightTableData.push(item)
      })
      this.leftTableKey++
      //   this.$nextTick(() => {
      //     this.$refs.leftTable.toggleRowSelection(this.leftTableSelectList)
      //   })
      return rightTableData
    },
    leftSelectable(row) {
      // true 禁用
      return !row.disabled
    },
    // 数据移入左边
    dataToLeft() {
      switch (this.leftType) {
        case 'tree':
          //  更新左侧的树
          this.refreshLeftTreeCheck(this.treeRow, this.rightTableSelectList)
          // 删除右侧表格选中的数据
          this.updateRightTableData(this.compairArr(this.rightTableData, this.rightTableSelectList, this.defaultTreeOption.nodeKey))
          this.rightTableSelectList = []
          this.rightTableKey++

          break
        case 'table' :
          if (this.rightTableSelectList.length === 0) {
            // 全部左移
            this.$confirm(this.$t('common.selectAllMove'), this.$t('common.prompt'), {
              confirmButtonText: this.$t('common.ok'),
              cancelButtonText: this.$t('common.cancel'),
              type: 'warning'
            }).then(() => {
              this.leftData.map(left => {
                this.$set(left, 'disabled', false)
              })
              this.updateRightTableData([])
            })
          } else {
            // 左侧表格数据取消disable
            this.cancelDisable()
            // 删除右侧表格选中的数据
            this.updateRightTableData(this.compairArr(this.rightTableData, this.rightTableSelectList, this.defaultLeftTableOption.option.rowKey))
            this.rightTableSelectList = []
          }

          break
        default :
          break
      }
    },

    cancelDisable() {
      const arr = this.compairArr2(this.leftData, this.rightTableSelectList, this.defaultLeftTableOption.option.rowKey)
      arr.map(item => {
        this.$set(item, 'disabled', false)
      })
    },
    // 移入左边时刷新树
    refreshLeftTreeCheck(treeList, tableList) {
      this.$refs.leftTreeIndex.toggleSelect(tableList, true)
      this.treeRow.map(item => {
        this.$set(item, 'disabled', false)
      })
      const halfNode = this.$refs.leftTreeIndex.getHalfCheckedNodes()
      halfNode.map(half => {
        this.$set(half, 'disabled', false)
      })
      this.$refs.leftTreeIndex.toggleSelect(tableList)
      // tableList.map(item => {
      //   this.$set(item, 'disabled', false)
      //   this.setParentDisable(item)
      // })
      // this.$refs.leftTreeIndex.toggleSelect(tableList)
    },
    setParentDisable(item) {
      const item3 = this.treeRow.find((item2) => item2.id === item.pid || item.pid === null)
      if (item3) {
        item3.disabled = false
        this.setParentDisable(item3)
      } else {
        return
      }
    },
    // 差集
    compairArr(arr1, arr2, key) {
      const arr = arr1.filter((item) => {
        return !arr2.some(a2 => a2[key] === item[key])
      })
      return arr
    },

    // 交集
    compairArr2(arr1, arr2, key) {
      const arr = arr1.filter((item) => {
        return arr2.some(a2 => a2[key] === item[key])
      })
      return arr
    }
  }
}
</script>
<style lang="scss" scoped>
.tree-table-transfer {
  .list-top {
    height: 45px;
    line-height: 45px;
    background-color: #f5f5f5 !important;
    padding: 0 15px;
    color: #606266;
  }
  .left-tree {
    margin-top: 10px;

  }
  .move-btn {
    position: absolute;
    top: 50%;
    left: 27%;
    display: flex;
    flex-direction: column;
  }
}

::v-deep .tree-index {
  height: 100%;
}
::v-deep .tree-index .el-scrollbar {
  height: calc(100% - 32px);
}
::v-deep .tree-index .el-scrollbar__view {
  height: 100%;
}
::v-deep .tree-index .el-tree {
  position: relative;
  height: 100%;
}
::v-deep .tree-index .el-tree__empty-text {
  top: 47%;
  font-size: 12px;
}
::v-deep .el-scrollbar__wrap {
  height: 100%;
}
::v-deep .move-btn .el-button + .el-button {
  margin-left: 0 !important;
  margin-top: 10px;
}
::v-deep .searchContentSpan{
max-width: 90px !important;
}
::v-deep .el-scrollbar__wrap{
  margin-right: 0px !important;
}
::v-deep .left-tree .el-scrollbar__thumb{
  display: none;
}
::v-deep .el-scrollbar__bar.is-horizontal{
  display: none;
}
::v-deep .el-scrollbar__bar.is-vertical{
   display: none;
}
::v-deep .table-search{
      position: absolute !important;
    top: -3px !important;
    right: 10px !important;
}
</style>

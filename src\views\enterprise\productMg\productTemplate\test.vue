<template>
  <div class="homepage" style="font-size: 13.33vw;">
    <div class="header_logo" />
    <div class="guaershula">
      <div class="title" />
      <div class="content">
        <div v-if="xgObj.isCompany==='Y'" class="company">{{ xgObj.village }} </div>
        <div class="hang" :style="xgObj.isCompany==='N' ? {paddingTop: '20px',marginTop: '-0.01rem'} : ''">
          <div class="">尊敬的顾客，您好:</div>
          <div class="text">
            您所购买的东台西瓜品种为金榜龙珠，这颗西瓜生长在江苏省东台市五烈镇甘港村，从台湾省技术引进，每一颗重约3KG。期待与您共度甜蜜、共享清凉。
          </div>
        </div>
      </div>
    </div>
    <div class="xiguajianjie">
      <div class="title" />
      <div class="content">
        <div class="hang">
          <div class="text">
            金榜龙珠，椭圆果型，果皮金黄，深红瓤色，中心可溶性固体物含量可达12°，脆甜多汁，果香浓郁，耐储运。荣誉：荣获全美园艺金奖。种植过程中采用BLOF技术，即通过综合考量土壤和西瓜本身的需求，量身定制施肥方案，使得板结土壤变得松软，改善土壤条件，并提供西瓜所需的营养元素。现经权威机构品测，品质优等，与它一起生长的还有20亩田地里3000个“兄弟姐妹”。
            <!--            农友小兰：极早熟小果型西瓜，果实圆球形，果皮浅绿色，上覆深绿色窄条纹。果肉黄色晶亮，中心含糖含量13%以上，中边糖梯度小，口感风味佳。全生育期80天左右，果实发育期25天左右。单瓜重1.5-2.0kg，果皮极薄，抗性好。-->
          </div>
        </div>
      </div>
    </div>
    <div style="width: 100%; padding: .4rem  .4rem 0; ">
      <video
        style="width: 100%;border-radius: 0.2rem;"
        controls
        loop
        autoplay
        muted
        preload="metadata"
        src="https://spd.guoyaoplat.com/1.mp4"
        webkit-playsinline
        playsinline
        x5-video-player-type="”h5-page”"
      >
        您的浏览器不支持 video 标签。
      </video>
    </div>
    <div class="rongyuzizhi">
      <div class="title" />
      <div class="content">
        <div class="banner">
          <el-carousel indicator-position="none">
            <el-carousel-item v-for="(img,i) in picList" :key="i">
              <div class="images">
                <img style="object-fit: contain" :src="require(`@/assets/img/${img}`)">
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </div>
    <div style="text-align: center;font-size: .3rem;margin: .3rem 0.4rem; color: #059653" />
    <div class="flag">
      <span>
        {{ xgObj.code }}
      </span>
      <img src="@/assets/img/code1.png" class="badge">
      <img class="beian" src="@/assets/img/beian.png">
    </div>
  </div></template>
<script>
import dayjs from 'dayjs'
import acc from '@/api/acc/acc'
import math from '../../../../utils/math'

export default {
  name: 'Test',
  data() {
    return {
      picList: ['greenSign.png', 'redSign.png'],
      xgObj: {}
    }
  },
  activated() {
    document.title = '东台市中国地理标志农产品溯源平台'
    acc.getInfoXg({ flagCode: this.$route.query.flagCode }).then((res) => {
      this.xgObj = res.data
    })
  },
  methods: {
    math() {
      return math
    },
    getProductInfo(productId) {
      acc.queryTemplateProduct({ productId, idisCode: this.idisCode, paramCheck: true }).then((res) => {
        this.productInfo = res.data
        const day = this.idisCode ? dayjs(this.productInfo.beyondShelfLifeDate).diff(dayjs(this.productInfo.productionDate), 'days') : 0
        this.productData = [
          { attrNameCn: '产品名称', attrValue: this.productInfo.productName },
          // { attrNameCn: '生产日期', attrValue: this.idisCode ? this.productInfo.productionDate : 'xxxx年xx月xx日' },
          // { attrNameCn: '保质期', attrValue: this.idisCode ? day > 180 ? `${Math.floor(day.divide(30))}个月` : `${day}天` : 'xxxx年xx月xx日' },
          // { attrNameCn: '有效期至', attrValue: this.idisCode ? this.productInfo.beyondShelfLifeDate : 'xxxx年xx月xx日' },
          // { attrNameCn: '产品批号', attrValue: this.productInfo.batchNo },
          ...res.data.productAttrList]
      })
    }
  }
}
</script>

<style>html {
  font-size: 13.33vw;
}</style>
<style  scoped lang="scss">

.homepage {
  //font-family: '微软雅黑';
  background-color: #abd7b4;
  padding-bottom: 50px;
  .header_logo {
    height: 4.12rem;
    /* background: #3bb377; */
    background: url("../../../../assets/img/nav_bg.png") 0 0 no-repeat;
    background-size: 100% 100%;
  }
}

.guaershula .title{
  background: url("../../../../assets/img/comtitle.png") 0 0 no-repeat;
}
.xiguajianjie .title {
  background: url("../../../../assets/img/maptitle.png") 0 0 no-repeat;
}
.rongyuzizhi .title {
  background: url("../../../../assets/img/honortitle.png") 0 0 no-repeat;
}

.guaershula, .xiguajianjie, .rongyuzizhi {
  position: relative;
  margin: -1.3rem  .4rem 0;
  .title {
    background-size: 100% 100%;
    height: 1rem;
  }
  .content {
    font-size: 0.3rem;
    background-color: #D8FBD1;
    padding: 0 .44rem .2rem;
    border-radius: .2rem;
    line-height: 0.55rem;
    text-align: justify;
    color: #c23e1e;
    word-break: break-all;
    width: 100%;
    .company {
      line-height: 1.3rem;
      height: 1.3rem;
      margin: 0 -0.44rem;
      white-space: nowrap;
      overflow: hidden;
      font-weight: 700;
      color: #77261c;
      text-align: center;
      &:after {
        content: '';
        position: absolute;
        top: 2.3rem;
        left: 0;
        z-index: 1;
        width: 100%;
        height: 1rem;
        background-image: linear-gradient(
                to right,
                #83db86 0%,
                #83db86 50%,
                transparent 50%
        );
        background-size: .3rem .03rem;
        background-repeat: repeat-x;
      }
    }
    .hang {
      margin-top: .2rem;
      .text {
        text-indent: 2em;
      }
    }
  }
}
.xiguajianjie,.rongyuzizhi {
  margin: 0 .4rem;
  .content {
    padding: 0.44rem;
  }
  .title {
    margin-top: 0.3rem;
  }
}

.xiguajianjie .content{
  padding-top: .16rem;
}

.high-light {
  color: #77261c !important;
  font-weight: bold;

}

.banner {
  padding: .1rem;
  width: 100%;
  background: #D8FBD1;
  .images {
    img{
      width: 5.5rem;
      height: 5.5rem;
    }
  }
}

::v-deep .el-carousel__container {
  width: 5.5rem;
  height: 5.5rem;
}

.badge {
  height: .4rem;
  width: auto;
  align-self: flex-start;
  margin-top: .1rem;
}
.flag {
  display: flex;
  flex-direction: column;
  font-size: .3rem;
  background-color: rgb(216,251,209);
  margin: 0 0.4rem;
  border-radius: .2rem;
  padding: .24rem .44rem;
  position: relative;
  .beian {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);;
    right: .1rem;
    width: 1.45rem;
    height: 1.3rem;
  }
}

</style>

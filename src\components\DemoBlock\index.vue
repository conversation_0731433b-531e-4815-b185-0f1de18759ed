<template>
  <div class="demo-block" @mouseover="setClass = true" @mouseleave="setClass = false">
    <div class="demo-block-control">
      <i :class="[iconClass,{'hovering':setClass}]" />
      <span v-show="setClass">{{ message }}</span>
    </div>
  </div>

</template>

<script>
export default {
  name: 'DemoBlock',
  components: {},
  props: {
    iconClass: { // +号样式
      type: String,
      default: 'el-icon-caret-bottom'
    },
    message: { // 显示信息
      type: String,
      default: null
    }
  },
  data() {
    // 这里存放数据
    return {
      setClass: false
    }
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {
  },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
  },
  // 方法集合
  methods: {} // 如果页面有keep-alive缓存功能，这个函数会触发
}
</script>

<style scoped lang="scss">
@import "~element-ui/packages/theme-chalk/src/common/var.scss";
.demo-block {
  border: 1px solid $--border-color-light;
  margin-bottom: 12px;
  border-radius: 3px;
  transition: .2s;
}
.demo-block.hover {
  box-shadow: 0 0 8px 0 rgba(232,237,250,.6), 0 2px 4px 0 rgba(232,237,250,.5);
}
.demo-block .demo-block-control {
  border-top: 1px solid $--border-color-light;
  height: 44px;
  box-sizing: border-box;
  background-color: $--color-white;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  text-align: center;
  margin-top: -1px;
  color: #d3dce6;
  cursor: pointer;
  position: relative;
}
.demo-block .demo-block-control:hover {
  color: $--color-primary;
  background-color: #f9fafc;
}
.demo-block .demo-block-control i {
   font-size: 16px;
   line-height: 44px;
   transition: .3s;
 }
.demo-block .demo-block-control i.hovering {
  transform: translateX(-40px);
 }
.demo-block .demo-block-control>span {
  position: absolute;
  transform: translateX(-30px);
  font-size: 12px;
  line-height: 44px;
  transition: .3s;
  display: inline-block;
}
</style>

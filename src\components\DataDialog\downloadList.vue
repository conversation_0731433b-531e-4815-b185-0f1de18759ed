<template>
  <div class="hw-download-list">
    <a
      v-for="(item, index) in fileList"
      :key="`${item[nameCode]}${index}`"
      title="点击下载"
      class="download-link"
      :href="`/api/inner/acc/file/downloadFile/${item.files.id}`"
      :download="item[nameCode]"
    >
      <i class="el-icon-document" />
      <span class="file-name">{{ item[nameCode] }}</span>
      <slot name="downloadBtn">
        <div class="download-btn">
          <el-image
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAihJREFUeNqkVTlPG1EQnj2MD6EobmzAuEmo0kUJBQWiokBBwghMkSI/IoUj6IMQ6aKkS5UqYhFHQUkNRRASJcpWDkK2LBmkyOce+T60S3w8GSKPNHrjeTOfZ2Zn5mmtVkt6qVgsjuDIBTwDngDT8Df4HLwPPsxms33OWi8gwJZxbMdisal4PC44xTCMuzvXdaXRaEi9Xuf5C6oPAN1TAgKIXpuRSKSQTCYlGo3KIGo2m1KtVqXdbm/j5waAXer1DptNgBRSqdSDYCTapNNpngX6dkWI6FZN07RooOu6/A95niflcpmR5hHlrmbbNj+AjcgmeyO7+qPJzHezS3fyzpHMqN+XPkCvID5jOHkUfvIxaQ5KHxgZiGsEzCUSCRmWAowcAaeHia4zStBrFmgs7LOQjmxdbpsiN41+x8NLTZ7GNHmCyi9Oeff6AGPcDCagK8QJFP39sSl1px9w69SQOLx+LDnKSJnyteN0X75M+/JtwZGIooOo4x1tOolTRCy6XKjmeTbry+d5Vwztn44ydbxTTQ7oJwEParWaMvw3zz35OOcKMcmUqVNRgHHAGloY9C38g7IX377w7j8OZRUxQ2Cwsa1w9PJYCjuc4yFGbw2jZ915U4DiU6VSEd/3Hw1GW/rQlxi922YdaX8plUphgR9cX4HtV/oOWrArbDcuWI4T6xo2PtuL9vwAwYJd54YZuLE7noA8mNv7FTgTLiDwWfAEWKon4K8AAwBgewD2FJVkogAAAABJRU5ErkJggg=="
            class="img"
          />
          <span>下载</span>
        </div>
      </slot>
    </a>
  </div>
</template>

<script>
export default {
  name: 'DownloadList',
  components: {},
  props: {
    fileList: {
      type: Array,
      required: true
    },
    nameCode: {
      type: String,
      default: `name`
    },
    pathCode: {
      type: String,
      default: `path`
    },
    urlPrefix: {
      type: String,
      default: ``
    }
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  methods: {}
}
</script>
<style lang="scss" scoped>
.hw-download-list {
  font-size: 12px;
  width: 100%;
  .download-link {
    width: 100%;
    padding: 5px 0;
    display: flex;
    align-items: center;
    color: #409eff;
    &:hover {
      color: #409eff;
      cursor: pointer;
      background-color: #f5f7fa;
    }
    i {
      margin-right: 7px;
    }
    span {
      display: inline-block;
    }
    .file-name {
      flex: 1;
      margin-right: 20px;
    }
    .download-btn {
      display: flex;
      align-items: center;
      .img {
        width: 20px;
        height: 20px;
      }
      span {
        margin-left: 5px;
      }
    }
  }
}
</style>

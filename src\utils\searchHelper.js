import constant from '@/utils/constant'
import Vue from 'vue'

const defaultOptions = {
  api: () => { return Promise.resolve() },
  handleSearchParams: (params) => { return params },
  success: () => {},
  failure: () => {},
  enablePage: true,
  pageSize: constant.initPaginationOption.pageSize
};
(function(root, factory) {
  Vue.prototype.$searchHelper = factory()
})(window, function() {
  function SearchHelper(options = {}) {
    this.options = Object.assign({}, defaultOptions, options)
    this.searchParams = {}
    this.dataList = Vue.observable([])
    this.pagination = Vue.observable({
      pageNum: 1,
      pageSize: this.options.pageSize,
      total: 0
    })
  }
  SearchHelper.prototype.handleQuery = function() {
    let pagination
    if (this.options.enablePage) {
      pagination = {
        pageNum: this.pagination.pageNum,
        pageSize: this.pagination.pageSize
      }
      if (this.pagination.orderName) {
        pagination['orderName'] = this.pagination.orderName
        pagination['orderType'] = this.pagination.orderType
      }
    }
    const _params = Object.assign({}, this.options.handleSearchParams(this.searchParams), pagination)

    this.options.api(_params).then(res => {
      Vue.set(this.pagination, 'total', res.data.total)
      Vue.set(this, 'dataList', res.data.list || [])
      this.options.success(res)
    }).catch(res => {
      this.options.failure(res)
    })
  }

  SearchHelper.prototype.search = function(params) {
    this.searchParams = params || {}
    this.handleQuery()
  }

  SearchHelper.prototype.reset = function() {
    this.searchParams = {}
    Vue.set(this.pagination, 'total', 0)
    Vue.set(this.pagination, 'pageNum', 1)
    this.search()
  }

  SearchHelper.prototype.getSearchParams = function() {
    return this.searchParams
  }
  return SearchHelper
})

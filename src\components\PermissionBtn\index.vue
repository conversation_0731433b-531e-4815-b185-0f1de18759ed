<template>
  <span>
    <span
      v-for="(item,$index) in innerButtonData"
      :key="$index"
      class="permissionBtn"
    >
      <el-button
        v-if="!item.slotName"
        :type="item.type"
        @click="emitEvent($event,item.action)"
      >{{ item.label }}
      </el-button>
      <template v-else>
        <slot
          :data="item"
        />

      </template>
    </span>
    <el-dropdown v-if="innerDropDownBtn && innerDropDownBtn.length>0" placement="bottom-start" @command="handleCommand">
      <el-button size="mini">
        {{ $t('common.moreOperations') }}<i class="el-icon-arrow-down el-icon-arrow-down-query el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="(item, $otherIndex) in innerDropDownBtn" :key="$otherIndex" :command="item">{{ item.label }}</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </span>
</template>

<script>
export default {
  name: 'PermissionBtn',
  props: {
    buttonData: {
      type: Array,
      default: () => {
        return []
      }
    },
    buttonIndex: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      innerButtonData: {
        type: Array,
        default: () => {
          return []
        }
      },
      innerDropDownBtn: {
        type: Array,
        default: () => {
          return []
        }
      }
    }
  },
  computed: {
    permissionBtn() {
      // eslint-disable-next-line no-unused-vars
      var that = this
      // eslint-disable-next-line eqeqeq
      return (rows) => {
        return rows.filter((item) => {
          return that.$checkBtnPermission(item.permission)
        })
      }
    }
  },
  watch: {
    buttonData: {
      handler(val) {
        const that = this
        this.innerButtonData = []
        this.innerDropDownBtn = []
        this.permissionBtn(val).forEach((value, index) => {
          value.type = value.type ? value.type : 'primary'
          if (index === that.buttonIndex - 1 && that.buttonData.length === that.buttonIndex) {
            that.innerButtonData.push(value)
            return
          }
          if (index >= that.buttonIndex - 1) that.innerDropDownBtn.push(value)
          else that.innerButtonData.push(value)
        })
        // this.innerButtonData = this.buttonData
      },
      deep: true
    }
  },
  created() {
    const that = this
    this.innerButtonData = []
    this.innerDropDownBtn = []
    this.permissionBtn(this.buttonData).forEach((value, index) => {
      value.type = value.type ? value.type : 'primary'
      if (index === that.buttonIndex - 1 && that.buttonData.length === that.buttonIndex) {
        that.innerButtonData.push(value)
        return
      }
      if (index >= that.buttonIndex - 1) that.innerDropDownBtn.push(value)
      else that.innerButtonData.push(value)
    })
  },
  methods: {
    handleCommand(item, instance) {
      if (typeof item.action === 'function') item.action()
    },
    emitEvent($event, click, data) {
      if (typeof click === 'function') click(data, $event)
    }
  }
}
</script>

<style scoped>
.permissionBtn{
  display:inline-block;
  margin-left: 10px;
}
.permissionBtn  + .el-dropdown {
    margin-left: 15px;
}
</style>

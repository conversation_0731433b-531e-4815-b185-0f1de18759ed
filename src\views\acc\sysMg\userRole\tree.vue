/* eslint-disable vue/require-default-prop */
<!--  -->
<template>
  <div>
    <el-input
      v-model="treeInputValue"
      v-bind="initInputOption.option"
      :placeholder="$t('common.filiter')"
      suffix-icon="el-icon-search"
    />
    <div class="role-tree-container">
      <el-scrollbar
        ref="scrollbar"
        tag="div"
        :wrap-class="wrapClass"
        class="tree-style"
      >
        <el-tree
          ref="treeForm"
          :data="treeData"
          :props="props"
          v-bind="treeProps"
          class="showTree"
          :node-key="nodeKey"
          :show-checkbox="showCheckbox"
          :check-strictly="checkStrictly"
          :expand-on-click-node="expandOnClickNode"
          :filter-node-method="filterNode"
          @check="handleChecked"
        >
          <span
            slot-scope="{node}"
            :title="node.label"
            v-text="node.label"
          />
        </el-tree>
      </el-scrollbar>
    </div>

  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import emitter from 'element-ui/src/mixins/emitter'
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  mixins: [emitter],
  props: {
    treeData: {
      type: Array,
      default: () => {
        return []
      }
    },
    nodeKey: {
      type: String,
      default: () => {
        return null
      }
    },
    props: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          children: 'children'
        }
      }
    },
    singleCheck: {
      type: Boolean,
      default: true
    },
    checkStrictly: {
      type: Boolean,
      default: true
    },
    treeProps: {
      type: Object,
      default: null
    },
    showCheckbox: {
      type: Boolean,
      default: true
    },
    expandOnClickNode: {
      type: Boolean,
      default: true
    }
  },
  data() {
    // 这里存放数据
    return {
      wrapClass: 'role-tree-height',
      initInputOption: this.$constant.initInputOption,
      // 输入框的名字
      treeInputValue: '',
      // 树形选中的id
      checkedId: '',
      // 树形选中的数据
      selectTreeData: []
    }
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {
    treeInputValue: {
      handler(val) {
        this.$refs.treeForm.filter(val)
      },
      deep: true
    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {}, // 生命周期 - 创建之前
  beforeMount() {}, // 生命周期 - 挂载之前
  beforeUpdate() {}, // 生命周期 - 更新之前
  updated() {}, // 生命周期 - 更新之后
  beforeDestroy() {}, // 生命周期 - 销毁之前
  destroyed() {}, // 生命周期 - 销毁完成
  activated() {
  },
  // 方法集合
  methods: {
    // 初始化树的半选状态
    initHalfTree(treeData) {
      const that = this
      const treeArr = this.getFirstNodeArr(treeData).reverse()
      treeArr.forEach((item) => {
        const node = that.$refs.treeForm.getNode(item)
        if (node?.parent && node.parent.checked) {
          node.parent.indeterminate = that.getIndeterminate(node)
        }
      })
    },
    // 获取每层的第一个节点
    getFirstNodeArr(treeData) {
      const that = this
      let treeArr = []
      treeData.forEach((item, index) => {
        if (index === 0) {
          treeArr.push(item[that.nodeKey])
        }
        if (item[that.props.children] && item[that.props.children].length > 0) {
          treeArr = treeArr.concat(this.getFirstNodeArr(item[that.props.children]))
        }
      })
      return treeArr
    },
    // 树形结构点击
    handleChecked(data) {
      const node = this.$refs.treeForm.getNode(data[this.nodeKey])
      this.setNode(node)
      this.setHalfSelect(node)
    },
    // 设置半选状态
    setHalfSelect(node) {
      if (node.parent) {
        node.parent.indeterminate = this.getIndeterminate(node)
        if (node.parent.parent) {
          this.setHalfSelect(node.parent)
        }
      }
    },
    // 获取父节点的状态  半选或者全选
    getIndeterminate(node) {
      if (node.parent && node.parent.childNodes) {
        let checkedAll = true
        for (let i = 0; i < node.parent.childNodes.length; i++) {
          if (node.parent.childNodes[i].checked === false || node.parent.childNodes[i].indeterminate) {
            checkedAll = false
            break
          }
        }
        return !checkedAll
      }
    },
    // 递归设置子节点和父节点
    setNode(node) {
      if (node.checked) {
        // 如果当前是选中checkbox,则递归设置父节点和父父节点++选中
        this.setParentNode(node)
        this.setChildNode(node)
      } else {
        // 如果当前是取消选中checkbox,则递归设置子节点全部取消选中
        this.cleanChildNode(node)
      }
    },
    // 递归设置父节点全部选中
    setParentNode(node) {
      if (node.parent) {
        for (const key in node) {
          if (key === 'parent') {
            node[key].checked = true
            this.setParentNode(node[key])
          }
        }
      }
    },
    // 递归设置子节点全部选中
    setChildNode(node) {
      if (node.childNodes && node.childNodes.length) {
        node.childNodes.forEach(item => {
          item.checked = true
          this.setChildNode(item)
        })
      }
    },
    // 递归设置子节点全部取消选中
    cleanChildNode(node) {
      if (node.childNodes && node.childNodes.length) {
        node.childNodes.forEach(item => {
          item.checked = false
          item.indeterminate = false
          this.cleanChildNode(item)
        })
      }
    },
    resetSearch() {
      this.treeInputValue = null
    },
    // 过滤
    filterNode(value, data) {
      if (!value) return true
      return data[this.props.label].indexOf(value) !== -1
    }
  } // 如果页面有keep-alive缓存功能，这个函数会触发
}
</script>
<style lang="scss" scoped>
.tree-style{
  padding: 10px 0 0 0 ;
}
.showTree{
  ::v-deep.el-tree-node__content{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
  }
}
</style>
<style>
  .role-tree-height{
    height: 45vh;
  }
</style>


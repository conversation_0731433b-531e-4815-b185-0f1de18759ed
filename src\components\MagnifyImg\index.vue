<template>
  <div class="pop">
    <div
      class="pop-box"
      :style="{
        width: boxStyle.width + 'px',
        height: boxStyle.height + 'px',
        cursor: flag ? 'move' : '',
      }"
    >
      <img :src="src" ref="moveDom" alt />
      <div
        v-if="flag"
        class="border"
        :style="{ left: styleObj.left + 'px', top: styleObj.top + 'px' }"
      ></div>
    </div>
    <div class="pop-image" v-if="flag">
      <img :style="popStyle" :src="src" alt />
    </div>
  </div>
</template>

<script>
export default {
  name: "MagnifyImg",
  props: {
    src: {
      type: String,
      required: true,
    },
    width: {
      type: Number,
      default: 300,
    },
    height: {
      type: Number,
      default: 300,
    },
    zoom: {
      type: Number,
      default: 2,
    },
  },
  data() {
    return {
      moveDom: null,
      img: new Image(),
      flag: false,
      popStyle: "",
      watchWidth: 100,
      boxStyle: {
        width: this.width,
        height: this.height,
      },
      styleObj: {
        left: 0,
        top: 0,
      },
    };
  },
  mounted() {
    this.moveDom = this.$refs.moveDom;
    this.img.src = this.src;
    this.moveDom.addEventListener("mousemove", this.moveEvent);
    this.moveDom.addEventListener("mouseleave", this.mouseleaveEvent);
  },
  beforeDestroy() {
    this.moveDom.removeEventListener("mouseleave", this.mouseleaveEvent);
    this.moveDom.removeEventListener("mousemove", this.moveEvent);
  },
  methods: {
    rafThrottle(fn) {
      let locked = false;
      return function (...args) {
        if (locked) return;
        locked = true;
        window.requestAnimationFrame((_) => {
          fn.apply(this, args);
          locked = false;
        });
      };
    },
    moveEvent(e) {
      let event = e || window.event;
      if (event.preventDefault) {
        event.preventDefault();
      } else {
        event.returnValue = false;
      }
      this.flag = true;
      let { offsetX, offsetY } = e;
      let { height, width } = this.boxStyle;
      this.styleObj = {
        left:
          offsetX - this.watchWidth / 2 > 0
            ? offsetX + this.watchWidth / 2 >= width
              ? width - this.watchWidth
              : offsetX - this.watchWidth / 2
            : 0,
        top:
          offsetY - this.watchWidth / 2 > 0
            ? offsetY + this.watchWidth / 2 >= height
              ? height - this.watchWidth
              : offsetY - this.watchWidth / 2
            : 0,
      };
      let { left, top } = this.styleObj;
      this.popStyle = `left:-${left * this.zoom}px;top:-${top * this.zoom}px`;
    },
    mouseleaveEvent(e) {
      this.flag = false;
    },
  },
};
</script>

<style scoped lang="scss">
.pop {
  position: relative;
  &-box {
    position: relative;
    box-sizing: border-box;
    overflow: hidden;
    border: 1px dashed rgb(253, 253, 253);
    img {
      width: 100%;
      height: 100%;
    }
    .border {
      position: absolute;
      width: 100px;
      height: 100px;
      pointer-events: none;
      background-color: rgba(127, 255, 212, 0.315);
    }
  }
  &-image {
    width: 200px;
    height: 200px;
    position: absolute;
    top: 0;
    overflow: hidden;
    left: 300px;
    img {
      position: absolute;
      width: 500px;
      height: 500px;
    }
  }
}
</style>

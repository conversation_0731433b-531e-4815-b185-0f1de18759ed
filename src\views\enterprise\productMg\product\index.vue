<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="(data) => {searchHelper.search(data)}"
      @return-reset="searchHelper.reset"
    />
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="() => {searchHelper.handleQuery()}"
    />

  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import acc from '@/api/acc/acc'
import accApi from '@/api/acc/acc'
import query from '@/components/mixin/query'
const StatusMapping = {
  10: '未同步',
  20: '已同步',
  30: '同步异常'
}
export default {
  name: 'Product',
  components: { DataSelect, DataTable },
  mixins: [query],
  data() {
    return {
      buttonData: [
        {
          label: '添加',
          action: this.onAddClick,
          permission: 'all'
        }
      ],
      searchHelper: new this.$searchHelper({ api: acc.productPageListList }),
      dataList: [],
      search: {
        searchText: {
          label: '产品条码/产品名称',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入产品条码/产品名称'
          }
        }
      },
      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            sortable: false
          },
          {
            label: '产品编号',
            prop: 'productNo',
            sortable: false
          },
          {
            label: '产品条码',
            prop: 'barCode',
            sortable: false
          },
          {
            label: '产品名称',
            prop: 'productName',
            sortable: false
          },
          {
            label: '显示名称',
            prop: 'packageName',
            sortable: false
          },
          {
            label: '产品分类',
            prop: 'categoryName',
            sortable: false
          },
          {
            label: '单位',
            prop: 'unit',
            sortable: false
          },
          {
            label: '单价',
            prop: 'price',
            sortable: false
          },
          {
            label: '同步状态',
            prop: 'syncStatus',
            format(row) {
              return StatusMapping[row.syncStatus]
            },
            sortable: false
          },
          {
            label: '创建人',
            prop: 'createUserName',
            sortable: false
          }
        ],
        operation: {
          label: '操作',
          width: '120px',
          data: [
            {
              label: '编辑',
              action: this.onEditClick,
              permission: 'all'
            }, {
              label: '删除',
              action: this.onDeleteClick,
              permission: 'all'
            }]
        }
      }
    }
  },
  methods: {
    onAddClick() {
      this.$router.replace({ path: 'productDetail', query: { action: 'add' }})
    },
    onDeleteClick(row) {
      this.$confirm('请确认是否删除?', '提示').then(() => {
        accApi.delProduct(row).then(res => {
          this.$message.success(res.message)
          this.searchHelper.handleQuery()
        })
      })
    },
    onEditClick(row) {
      this.$router.replace({ path: 'productDetail', query: { action: 'edit', id: row.id }})
    }
  }
}
</script>

<style scoped lang="scss">
</style>

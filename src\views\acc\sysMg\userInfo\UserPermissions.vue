<template>
  <simple-data-dialog
    title="权限授权"
    :visible="visible"
    size="middle"
  >
    <el-row :gutter="10" style="margin-right: 15px">
      <el-col
        v-for="(item) in formItem"
        :key="item.prop"
        :span="12"
      >
        <div>
          <label :for="item.prop" class="el-form-item__label">{{ item.label + '：' }}</label>
        </div>
        <el-input :id="item.prop" :value="item.value.join(',')" disabled />
      </el-col>
    </el-row>
    <data-table
      :table-data="tableData"
      :column="columns"
      :table-option="{option: {
        border: true,
        height: '40vh'
      }}"
    >
      <template v-slot:dataType="{ row, $index }">
        <el-checkbox
          :value="dataTypeCheckedOrIndeterminate($index, true)"
          :indeterminate="dataTypeCheckedOrIndeterminate($index)"
          @change="(val) => {checkAllChange(val, $index)}"
        >{{ row.dataType }}</el-checkbox>
      </template>
      <template v-slot:dataValue="{ row }">
        <div class="data-checkbox-group">
          <template v-for="item in row.dataValue">
            <el-checkbox :key="item.value" v-model="item.checked">{{ item.label }}</el-checkbox>
          </template>
        </div>
      </template>
    </data-table>
    <template v-slot:footer>
      <div class="dialog_btn">
        <el-button @click="visible=false">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" @click="onSaveClick">{{ $t('common.ok') }}</el-button>
      </div>
    </template>
  </simple-data-dialog>
</template>

<script>
import SimpleDataDialog from '@/components/SimpleDataDialog'
import DataTable from '@/components/DataTable'
import accApi from '@/api/acc/acc'

export default {
  name: 'UserPermissions',
  components: { DataTable, SimpleDataDialog },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    users: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      columns: {
        data: [
          {
            label: '行数据类型',
            prop: 'dataType',
            width: 150,
            slotName: 'dataType',
            sortable: false
          },
          {
            label: '行数据权限',
            prop: 'dataValue',
            slotName: 'dataValue',
            sortable: false
          }
        ]
      },
      // 0:中心库  1:科室库
      tableData: [
        {
          dataType: '中心仓库',
          dataValue: []
        },
        {
          dataType: '科室仓库',
          dataValue: []
        }
      ],
      formItem: [
        { prop: 'emplCode', label: this.$t('acc.userInfo.emplCode'), value: [] },
        { prop: 'userName', label: this.$t('acc.userInfo.userName'), value: [] }
      ]
    }
  },
  computed: {
    dataTypeCheckedOrIndeterminate() {
      return (index, isChecked) => {
        let checkedCount = 0
        const dataValue = this.tableData[index].dataValue
        dataValue.forEach(item => {
          if (item.checked) {
            checkedCount++
          }
        })
        if (isChecked) {
          return checkedCount > 0 && checkedCount === dataValue.length
        }
        return checkedCount > 0 && checkedCount < dataValue.length
      }
    },
    visible: {
      get() {
        if (this.value) {
          this.initIdName()
          this.initList()
        } else {
          this.tableData.forEach(item => {
            item.dataValue = []
          })
        }
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    userIdArr() {
      return this.users.map(item => item.emplCode)
    },
    userNameArr() {
      return this.users.map(item => item.userName)
    }
  },
  methods: {
    onSaveClick() {
      accApi.userPermission.savePermissions({
        userIds: this.userIdArr,
        centerWarehouseListSelected: this.tableData[0].dataValue.filter(item => item.checked).map(item => item.value),
        departmentWarehouseListSelected: this.tableData[1].dataValue.filter(item => item.checked).map(item => item.value)
      }).then(res => {
        if (this.$util.resolveResult(res)) {
          this.visible = false
        }
      })
    },
    initList() {
      accApi.userPermission.getAllPermissions(this.userIdArr).then(res => {
        const cwSelected = new Set((res.data.centerWarehouseListSelected || []).map(item => item.code))
        const dwSelected = new Set((res.data.departmentWarehouseListSelected || []).map(item => item.code))
        this.tableData[0].dataValue = (res.data.centerWarehouseList || []).map(item => {
          return {
            checked: cwSelected.has(item.code),
            label: item.codeName,
            value: item.code
          }
        })
        this.tableData[1].dataValue = (res.data.departmentWarehouseList || []).map(item => {
          return {
            checked: dwSelected.has(item.code),
            label: item.codeName,
            value: item.code
          }
        })
      })
    }, initIdName() {
      const idName = {
        emplCode: this.userIdArr,
        userName: this.userNameArr
      }
      this.formItem.forEach(item => {
        item.value = idName[item.prop]
      })
    }, checkAllChange(val, index) {
      this.tableData[index].dataValue.forEach(item => {
        item.checked = val
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.data-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
::v-deep .el-table__body-wrapper {
  overflow-y: auto;
}
</style>

<template>
  <div class="select-check-box">
    <el-input
      v-model="inputValue"
      v-bind="initInputOption.option"
      suffix-icon="el-icon-search"
    />
    <div v-if="selectAllDisplay" class="all-select-header el-select-dropdown__item">
      <el-checkbox :value="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">{{ $t('common.allSelect') }}</el-checkbox>
    </div>
    <el-scrollbar
      v-show="showList.length > 0"
      ref="scrollbar"
      tag="ul"
      wrap-class="el-select-dropdown__wrap"
      view-class="el-select-dropdown__list"
      :class="{ 'is-empty': showList.length === 0 }"
    >
      <el-checkbox-group :value="groupValue" @input="handleCheckedChange">
        <el-checkbox v-for="(item,index) in showList" :key="item.value || index" :true-label="trueLabel(item[id])" :false-label="null" :label="item[id]" @change="setSingleCheck">{{ item[label] }}</el-checkbox>
      </el-checkbox-group>
    </el-scrollbar>
  </div>

</template>

<script>
export default {
  name: 'SelectCheckBox',
  components: {},
  props: {
    value: {
      type: [Array, String, Number],
      default() {
        return []
      }
    },
    list: {
      type: Array,
      default() {
        return []
      }
    },
    multiple: {
      type: Boolean,
      default() {
        return false
      }
    },
    id: {
      type: String,
      default() {
        return 'value'
      }
    },
    label: {
      type: String,
      default() {
        return 'label'
      }
    }
  },
  data() {
    // 这里存放数据
    return {
      // checkAll: false,
      // isIndeterminate: false,
      initInputOption: this.$constant.initInputOption,
      inputValue: null
    }
  },
  // 监听属性 类似于data概念
  computed: {
    checkAll() {
      return this.value.length === this.list.length
    },
    isIndeterminate() {
      return this.value?.length > 0 && this.value.length < this.list.length
    },
    selectAllDisplay() {
      return this.multiple && this.showList?.length > 0
    },
    showList() {
      return this.inputValue ? this.list?.length > 0 ? this.list.filter(value => value[this.label].indexOf(this.inputValue) >= 0) : [] : this.list
    },
    groupValue() {
      return this.multiple ? this.value : this.value ? [this.value] : []
    },
    trueLabel() {
      const that = this
      return (value) => {
        return that.multiple ? null : value
      }
    }
  },
  // 监控data中的数据变化
  watch: {},
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {
  },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
  },
  // 方法集合
  methods: {
    handleCheckAllChange(val) {
      this.$emit('input', val ? this.list.map(value => value[this.id]) : [])
      this.$emit('change', val)
      this.$emit('selectAll', val)
    },
    handleCheckedChange(value) {
      if (this.multiple) {
        this.$emit('input', value)
        this.$emit('change', value)
      }
    },
    setSingleCheck(value) {
      if (!this.multiple) {
        this.$emit('input', value)
        this.$emit('change', value)
      }
    }
  } // 如果页面有keep-alive缓存功能，这个函数会触发
}
</script>

<style lang="scss" scoped>
@import "~element-ui/packages/theme-chalk/src/mixins/mixins";
.el-checkbox{
  display: block;
}
.el-select-dropdown__item{
  padding: 0;
}
.select-check-box{
  padding: 0 10px;
}
.all-select-header{
  border-bottom: 1px solid $--checkbox-disabled-checked-input-border-color;
}
</style>

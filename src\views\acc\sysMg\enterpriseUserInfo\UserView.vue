<template>
  <el-row type="flex" style="flex-direction: column;height: 100%">
    <div class="div-head">
      <el-row :gutter="10" type="flex" justify="space-between">
        <el-col style="width: auto">
          <upload-img
            ref="uploadImg"
            :img-list.sync="imgList"
            upload-icon="el-icon-camera-solid"
            use-cropper
            param="acc"
            :cropper-option="cropperOption"
            :limit-count="1"
            @upload-success="onImageSuccess"
            @after-clear-img="onAfterClearImg"
          >
            <template v-slot:tip>{{ }}</template>
          </upload-img>
          <!--          <div class="div-user-avatars" @click="onAvatarsClick">
            <el-row class="div-avatars-inner" type="flex" justify="center" align="center">
              <i class="el-icon-camera-solid" style="font-size: 50px;color: #d9d9d9;" />
              <div class="jp-avatars-text">{{ $t('common.uploadImg') }}</div>
            </el-row>
          </div>-->
        </el-col>
        <el-col style="text-align: left;position: relative">
          <el-row>
            <el-col><span class="jp-user-name">{{ formItem.basic.userName.value }}</span>
            </el-col>
          </el-row>
          <el-row style="bottom: 0;position: absolute">
            <el-col><span class="jp-user-meta">{{
              formItem.basic.emplCode.value
            }}</span></el-col>
            <el-col><span class="jp-user-meta">{{ $t('acc.userInfo.mail') }}：{{ formItem.basic.mail.value }}</span>
            </el-col>
            <el-col><span class="jp-user-meta">{{ $t('acc.userInfo.phone') }}：{{ formItem.basic.phone.value }}</span>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="16" style="text-align: right">
          <el-button v-if="$checkBtnPermission('userInfo:edit')" type="primary" @click="onEditClick">{{ $t('common.edit') }}</el-button>
          <el-button v-if="$checkBtnPermission('userInfo:disabled')" @click="onChangeStatusClick">{{ isDisableText }}</el-button>
          <el-button v-if="$checkBtnPermission('userInfo:resetpwd')" @click="onResetPwdClick">{{ $t('acc.userInfo.resetPwd') }}</el-button>
          <el-button @click="onReturnClick">{{ $t('common.back') }}</el-button>
        </el-col>
      </el-row>
    </div>

    <el-scrollbar class="jp-scrollbar" wrap-class="jp-scrollbar__wrap">
      <el-collapse :value="['1','2','3']">
        <el-collapse-item class="collapse-item" :title="$t('acc.userInfo.basicInfo')" name="1">
          <el-row :gutter="10">
            <el-col
              v-for="(item, prop) in formItem.basic"
              :key="prop"
              :xs="12"
              :sm="8"
            >
              <div>
                <label :for="prop" class="el-form-item__label">{{ item.label + '：' }}</label>
              </div>
              <el-input :id="prop" :value="item.value" disabled />
            </el-col>
          </el-row>
        </el-collapse-item>
        <el-collapse-item class="collapse-item" :title="$t('acc.userInfo.otherInfo')" name="3">
          <el-row :gutter="10">
            <el-col
              v-for="(item, prop) in formItem.other"
              :key="prop"
              :xs="12"
              :sm="8"
            >
              <div>
                <label :for="prop" class="el-form-item__label">{{ item.label + '：' }}</label>
              </div>
              <el-input :id="prop" :value="item.value" disabled />
            </el-col>
          </el-row>
        </el-collapse-item>
      </el-collapse>
    </el-scrollbar>
    <!-- 明细弹出框 -->
    <simple-data-dialog
      :title="dialog.title"
      :visible.sync="dialog.visible"
      size="middle"
    >
      <template v-slot:default>
        <el-scrollbar class="scrollbar" wrap-class="scrollbar__wrap">
          <user-detail ref="userDetail" :empl-code="dialog.emplCode" :guid="guid" />
        </el-scrollbar>
      </template>
      <template v-slot:footer>
        <div class="dialog_btn">
          <el-button @click="dialog.visible=false">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="onSaveClick">{{ $t('common.ok') }}</el-button>
        </div>
      </template>
    </simple-data-dialog>
  </el-row>
</template>

<script>
import SimpleDataDialog from '@/components/SimpleDataDialog'
import UploadImg from '@/components/DataDialog/uploadImg'
import UserDetail from './UserDetail'

import accApi from '@/api/acc/acc'

export default {
  name: 'UserView',
  components: {
    SimpleDataDialog,
    UserDetail,
    UploadImg
  },
  data() {
    return {
      imgList: [],
      cropperOption: {
        aspectRatio: 5 / 6
      },
      emplCode: this.$route.query.emplCode,
      guid: this.$route.query.guid,
      isDisable: null,
      formItem: {
        basic: {
          emplCode: { label: this.$t('acc.userInfo.emplCode'), value: null },
          userName: { label: this.$t('acc.userInfo.userName'), value: null },
          // deptName: { label: '科室', value: null },
          mail: { label: this.$t('acc.userInfo.mail'), value: null },
          phone: { label: this.$t('acc.userInfo.phone'), value: null },
          // spaceName: { label: '部门名称', value: null },
          enterpriseWechatUserId: { label: '企业微信', value: null }
        },
        other: {
          certificateId: { label: this.$t('acc.userInfo.identityId'), value: null },
          sexName: { label: this.$t('acc.userInfo.sex'), value: null },
          birthdate: { label: this.$t('acc.userInfo.birthdate'), value: null }
        }
      },
      dialog: {
        // 弹出框组件
        title: '',
        visible: false,
        emplCode: ''
      },
      saveParam: {}
    }
  },
  computed: {
    isDisableText: function() {
      return this.isDisable === 'Y' ? this.$t('common.enable') : this.$t('common.unenable')
    }
  },
  mounted() {
    this.queryUserDetail()
  },
  methods: {
    queryUserDetail() {
      accApi.userInfo.queryUserDetail(this.emplCode).then(result => {
        this.setFormData(this.formItem.basic, result.data)
        this.setFormData(this.formItem.other, result.data)
        this.dialog.userDetail = result.data
        this.isDisable = result.data['isDisable']
        const filePath = result.data['userPictureUrl']
        if (filePath != null && filePath !== '') {
          this.imgList = [{
            url: result.data['userPictureUrl']
          }]
        }
      })
    },
    setFormData(formItem, data) {
      Object.keys(formItem).forEach(prop => {
        formItem[prop].value = data[prop]
      })
    },
    onReturnClick() {
      this.$router.replace('userInfo')
    },
    onEditClick() {
      this.dialog.title = this.$t('common.edit')
      this.dialog.emplCode = this.emplCode
      this.dialog.visible = true
    },
    onResetPwdClick() {
      accApi.userInfo.resetPassword(this.emplCode).then(() => {
        this.$message.success({
          dangerouslyUseHTMLString: true,
          message: this.$t('acc.userInfo.resetPasswordSuccess').toString()
        })
      })
    },
    onChangeStatusClick() {
      const isDisable = this.isDisable === 'Y' ? 'N' : 'Y'
      accApi.userInfo.isResetStatus(this.emplCode, isDisable).then(() => {
        this.isDisable = isDisable
      })
    },
    onSaveClick() {
      this.$refs.userDetail.$emit('submit', result => {
        this.$message.success(this.$t('common.editSuccess').toString())
        this.dialog.visible = false
        this.queryUserDetail()
      })
    },
    onImageSuccess(img) {
      if (img && Array.isArray(img)) {
        const filePath = img[0]['url']
        accApi.userInfo.editUserAvatar({
          emplCode: this.emplCode,
          userPictureUrl: filePath
        })
      }
    },
    onAfterClearImg() {
      accApi.userInfo.editUserAvatar({
        emplCode: this.emplCode,
        userPictureUrl: ''
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.collapse-item {
  background-color: white;
  margin: 10px;
}

.div-head {
  margin: 10px;
}

::v-deep .img-container {
  width: 100px;
  height: 120px !important;
  padding: 0;

  .img-list {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;

    .el-image {
      width: 94px;
      height: 114px;
      margin: 2px;
    }
  }

  .upload-list {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;

    .uploader {
      width: 100%;
      height: 100%;
      border: 2px dashed #d9d9d9;

      .uploader-icon {
        width: 96px;
        height: 116px;
        line-height: 116px;
        font-size: 50px;
        color: #d9d9d9;
      }
    }
  }
}

.img-cropper {
  width: 100%;
  height: 300px;
}

.div-user-avatars {
  height: 120px;
  text-align: center;
  width: 100px;
  border: 2px dashed #d9d9d9;
  position: relative;
  cursor: pointer;
}

.div-avatars-inner {
  flex-direction: column;
  height: 100%;
}

.jp-scrollbar {
  height: 100%;
}

.jp-scrollbar > > > .jp-scrollbar__wrap {
  overflow-x: hidden;
}

.jp-user-name {
  font-size: 18px;
  line-height: 2em;
  font-weight: 600;
  color: #333;
  padding: 5px 0
}

.jp-user-meta {
  font-size: 12px;
  line-height: 2em;
  color: #565656;
  padding: 5px 0
}

.jp-avatars-text {
  color: #565656;
  font-size: 12px;
  line-height: 1.5em
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden
}
.scrollbar {
  height: calc(60vh);

  ::v-deep .scrollbar__wrap {
    overflow-x: hidden;
  }
}
</style>

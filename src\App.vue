<template>
  <div id="app">
    <image-viewer
      v-if="opened"
      :z-index="imageViewerZIndex"
      :on-close="closeImageViewer"
      :url-list="imageUrlList"
    />
    <keep-alive :exclude="['Layout','Login']">
      <router-view />
    </keep-alive>
  </div>
</template>

<script>
import ImageViewer from 'element-ui/packages/image/src/image-viewer'
import { PopupManager } from 'element-ui/lib/utils/popup'
import VueEvent from '@/utils/vue-event'
import { watermark } from './utils/watermark'
import { mapActions } from 'vuex'

export default {
  name: 'App',
  components: {
    ImageViewer
  },
  data() {
    return {
      opened: false,
      imageViewerZIndex: 2000,
      imageUrlList: []
    }
  },
  created() {
    VueEvent.$on('app.openImage', this.openImageViewer)
    if (this.$store.getters.token) {
      // this.getAllDictList()
    }
  },
  methods: {
    ...mapActions([`getAllDictList`]),
    openImageViewer(urlList) {
      if (Array.isArray(urlList) && urlList.length > 0) {
        this.imageUrlList = urlList
        this.imageViewerZIndex = PopupManager.nextZIndex()
        this.opened = true
      }
    }, closeImageViewer() {
      this.opened = false
    }
  }
}
</script>


<template>
  <div
    class="el-form-item el-form-item--table"
    :class="[{
               'el-form-item--feedback': elForm && elForm.statusIcon,
               'is-error': validateState === 'error',
               'is-validating': validateState === 'validating',
               'is-success': validateState === 'success',
               'is-required': isRequired || required,
               'is-no-asterisk': elForm && elForm.hideRequiredAsterisk
             },
             sizeClass ? 'el-form-item--' + sizeClass : ''
    ]"
  >

    <div class="el-form-item__content">
      <el-popover
        ref="validatePop"
        :disabled="validatePopDisabled"
        popper-class="el-popover--error"
        transition="el-zoom-in-center"
        placement="top"
        trigger="hover"
      >
        <span v-text="validateMessage" />
        <slot slot="reference" />
      </el-popover>
    </div>

  </div>
</template>

<script>
import { getPropByPath, noop } from 'element-ui/src/utils/util'
import AsyncValidator from 'async-validator'
import emitter from 'element-ui/src/mixins/emitter'
import objectAssign from 'element-ui/src/utils/merge'

export default {
  name: 'FormTableItem',
  componentName: 'ElFormItem',
  mixins: [emitter],
  inject: ['elForm'],
  props: {
    prop: {
      type: String,
      default: undefined
    },
    required: {
      type: Boolean,
      default: undefined
    },
    rules: {
      type: [Object, Array],
      default: undefined
    },
    error: {
      type: String,
      default: undefined
    },
    validateStatus: {
      type: String,
      default: undefined
    },
    showMessage: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'mini'
    }
  },
  data() {
    return {
      validateState: '',
      validateMessage: '',
      validateDisabled: false,
      validator: {},
      computedLabelWidth: ''
    }
  },
  computed: {
    validatePopDisabled() {
      const disabled = !(this.validateState === 'error' && this.showMessage && this.form.showMessage)
      this.$nextTick(() => {
        this.$refs.validatePop.updatePopper()
      })
      return disabled
    },
    form() {
      let parent = this.$parent
      let parentName = parent.$options.componentName
      while (parentName !== 'ElForm') {
        parent = parent.$parent
        parentName = parent.$options.componentName
      }
      return parent
    },
    fieldValue() {
      const model = this.form.model
      if (!model || !this.prop) { return }

      let path = this.prop
      if (path.indexOf(':') !== -1) {
        path = path.replace(/:/, '.')
      }

      return getPropByPath(model, path, true).v
    },
    isRequired() {
      const rules = this.getRules()
      let isRequired = false

      if (rules && rules.length) {
        rules.every(rule => {
          if (rule.required) {
            isRequired = true
            return false
          }
          return true
        })
      }
      return isRequired
    },
    elFormItemSize() {
      return this.size || this._formSize
    },
    sizeClass() {
      return this.elFormItemSize || (this.$ELEMENT || {}).size
    }
  },
  watch: {
    error: {
      immediate: true,
      handler(value) {
        this.validateMessage = value
        this.validateState = value ? 'error' : ''
      }
    },
    validateStatus(value) {
      this.validateState = value
    }
  },
  mounted() {
    if (this.prop) {
      this.dispatch('ElForm', 'el.form.addField', [this])

      let initialValue = this.fieldValue
      if (Array.isArray(initialValue)) {
        initialValue = [].concat(initialValue)
      }
      Object.defineProperty(this, 'initialValue', {
        value: initialValue
      })

      this.addValidateEvents()
    }
  },
  beforeDestroy() {
    this.dispatch('ElForm', 'el.form.removeField', [this])
  },
  methods: {
    validate(trigger, callback = noop) {
      this.validateDisabled = false
      const rules = this.getFilteredRule(trigger)
      if ((!rules || rules.length === 0) && this.required === undefined) {
        callback()
        return true
      }

      this.validateState = 'validating'

      const descriptor = {}
      if (rules && rules.length > 0) {
        rules.forEach(rule => {
          delete rule.trigger
        })
      }
      descriptor[this.prop] = rules

      const validator = new AsyncValidator(descriptor)
      const model = {}

      model[this.prop] = this.fieldValue

      validator.validate(model, { firstFields: true }, (errors, invalidFields) => {
        this.validateState = !errors ? 'success' : 'error'
        this.validateMessage = errors ? errors[0].message : ''

        callback(this.validateMessage, invalidFields)
        this.elForm && this.elForm.$emit('validate', this.prop, !errors, this.validateMessage || null)
      })
    },
    clearValidate() {
      this.validateState = ''
      this.validateMessage = ''
      this.validateDisabled = false
    },
    getRules() {
      let formRules = this.form.rules
      const selfRules = this.rules
      const requiredRule = this.required !== undefined ? { required: !!this.required } : []

      const prop = getPropByPath(formRules, this.prop || '')
      formRules = formRules ? (prop.o[this.prop || ''] || prop.v) : []

      return [].concat(selfRules || formRules || []).concat(requiredRule)
    },
    getFilteredRule(trigger) {
      const rules = this.getRules()

      return rules.filter(rule => {
        if (!rule.trigger || trigger === '') return true
        if (Array.isArray(rule.trigger)) {
          return rule.trigger.indexOf(trigger) > -1
        } else {
          return rule.trigger === trigger
        }
      }).map(rule => objectAssign({}, rule))
    },
    onFieldBlur() {
      this.validate('blur')
    },
    onFieldChange() {
      if (this.validateDisabled) {
        this.validateDisabled = false
        return
      }

      this.validate('change')
    },
    addValidateEvents() {
      const rules = this.getRules()
      if (rules.length || this.required !== undefined) {
        this.$on('el.form.blur', this.onFieldBlur)
        this.$on('el.form.change', this.onFieldChange)
      }
    },
    removeValidateEvents() {
      this.$off()
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form-item--table {
  margin-bottom: 0;
  .el-form-item__content {
    display: flex;
  }
  &.is-required .el-form-item__content:after {
    content: '*';
    color: #F56C6C;
    margin-left: 4px;
    display: flex;
    align-items: center;
  }
}
</style>

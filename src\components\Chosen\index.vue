<template>
  <el-select
    :id="id"
    ref="jSelect"
    v-model="chosenData"
    :name="name"
    :automatic-dropdown="automaticDropdown"
    :size="size"
    :disabled="disabled"
    :clearable="clearable"
    :allow-create="allowCreate"
    :loading="loading"
    :popper-class="popperClass"
    :loading-text="loadingText"
    :no-match-text="noMatchText"
    :no-data-text="noDataText"
    :filter-method="filterMethod"
    :multiple="multiple"
    :multiple-limit="multipleLimit"
    :placeholder="placeholder"
    :default-first-option="defaultFirstOption"
    :reserve-keyword="reserveKeyword"
    :value-key="valueKey"
    :collapse-tags="collapseTags"
    :popper-append-to-body="popperAppendToBody"
    :full-selection="fullSelection"
    @visible-change="onSelectVisibleChange"
    @change="onChange($event)"
  >
    <template v-slot:header>
      <div :class="['chosen-container', {'chosen-header':hasHeader}]">
        <el-input
          v-if="filterable"
          ref="input"
          v-model="searchKey"
          type="text"
          size="small"
          :placeholder="$t('common.enter') + $t('common.keyword') + $t('common.search')"
          suffix-icon="el-icon-search"
          @input="debouncedOnInputChange"
          @paste.native="debouncedOnInputChange"
          @keydown.native.enter.prevent="selectOption"
        />
      </div>
      <div
        v-show="fullSelection && multiple"
        class="all-select-header el-select-dropdown__item"
        :class="{selected: allSelected}"
        @click.stop="selectAllClick"
      >

        <!--
        <label class="el-checkbox">
          <span class="el-checkbox__input">
            <span class="el-checkbox__inner" />
          </span>
          <span class="el-checkbox__label">{{ $t('common.allSelect') }}</span>
        </label>
        -->

        <span>{{ $t('common.allSelect') }}</span>
      </div>
      <div v-show="fullSelection && multiple" class="chosen-container">
        <el-divider class="chosen-divider" />
      </div>
    </template>
    <div
      v-infinite-scroll="loadOptions"
      class="infinite-list"
      :class="{selected: remote && remoteAllSelected}"
      :infinite-scroll-disabled="noMore || scrollLoading"
    >
      <el-option
        v-for="(item, index) in chosenOptions"
        :key="index"
        :label="item[labelField]"
        :value="item[valueField]"
      >
        <slot name="option" :item="item">
          <span>{{ item[labelField] }}</span>
        </slot>
        <!--
        <template v-if="multiple">
          <label class="el-checkbox">
            <span class="el-checkbox__input">
              <span class="el-checkbox__inner" />
            </span>
            <span class="el-checkbox__label">
              <slot name="option" :item="item">
                {{ item[labelField] }}
              </slot>
            </span>
          </label>
        </template>
        <template v-else>
          <slot name="option" :item="item">
            <span>{{ item[labelField] }}</span>
          </slot>
        </template>
        -->
      </el-option>
      <div v-if="scrollLoading" class="span-loading"><i class="el-icon-loading" /></div>
    </div>
  </el-select>
</template>

<script>
import debounce from 'throttle-debounce/debounce'
import { isEqual } from 'element-ui/src/utils/util'

export default {
  name: 'Chosen',
  props: {
    name: {
      type: String,
      default: null
    },
    id: {
      type: String,
      default: null
    },
    value: {
      type: [String, Array],
      default: ''
    },
    valueLabel: {
      type: String,
      default: ''
    },
    automaticDropdown: Boolean,
    size: {
      type: String,
      default: null
    },
    disabled: Boolean,
    clearable: Boolean,
    filterable: Boolean,
    allowCreate: Boolean,
    loading: Boolean,
    popperClass: {
      type: String,
      default: null
    },
    remote: Boolean,
    loadingText: {
      type: String,
      default: null
    },
    noMatchText: {
      type: String,
      default: null
    },
    noDataText: {
      type: String,
      default: null
    },
    remoteMethod: {
      type: Function,
      default: null
    },
    filterMethod: {
      type: Function,
      default: null
    },
    multiple: {
      type: Boolean,
      default: false
    },
    multipleLimit: {
      type: Number,
      default: 0
    },
    placeholder: {
      type: String,
      default() {
        return this.$t('el.select.placeholder')
      }
    },
    defaultFirstOption: Boolean,
    reserveKeyword: Boolean,
    valueKey: {
      type: String,
      default: 'value'
    },
    collapseTags: Boolean,
    popperAppendToBody: {
      type: Boolean,
      default: true
    },
    options: {
      type: Array,
      default: () => {
        return []
      }
    },
    fullSelection: Boolean,
    valueField: {
      type: String,
      default: 'code'
    },
    labelField: {
      type: String,
      default: 'codeName'
    },
    queryKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchKey: '',
      chosenOptions: [],
      debouncedOnInputChange: null,
      scrollLoading: false,
      noMore: false,
      isOnComposition: false,
      remoteAllSelected: false,
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    debounce() {
      return this.remote ? 300 : 0
    },
    chosenData: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
        let valueLabel
        if (this.remote && this.remoteAllSelected && value.length === 0) {
          valueLabel = [this.$t('common.all')]
        } else {
          this.remoteAllSelected = false
          const chooseMap = {}
          this.chosenOptions.forEach(item => {
            chooseMap[item[this.valueField]] = item[this.labelField]
          })
          valueLabel = Array.isArray(value) ? value.map(v => chooseMap[v]) : [chooseMap[value]]
        }
        this.$emit('update:valueLabel', valueLabel.join(','))
      }
    },
    hasHeader() {
      return this.filterable || (this.fullSelection && this.multiple)
    },
    allSelected() {
      let selected = false
      if (this.remote) {
        selected = this.remoteAllSelected
      } else {
        if (this.fullSelection && this.chosenOptions.length > 0) {
          selected = !this.chosenOptions.some(option => !this.chosenData?.includes(option[this.valueField]))
        }
      }
      return selected
    }
  },
  watch: {
    value(val) {
      if (!isEqual(val, this.chosenData)) {
        if (this.remote) {
          this.remoteDefaultOptions(val)
        } else {
          this.chosenData = val
          // this.$emit('input', val)
        }
      }
    },
    options(val) {
      this.chosenOptions = Object.assign([], val)
    }
  },
  created() {
    this.chosenOptions = Object.assign([], this.options)
    this.debouncedOnInputChange = debounce(this.debounce, () => {
      this.onInputChange()
    })
  },
  methods: {
    selectOption() {
      this.$refs.jSelect.selectOption()
    },
    remoteDefaultOptions(val) {
      const includes = this.chosenOptions.some(option => option[this.valueField] === val)
      if (val !== '' && !includes) {
        const paramKey = this.queryKey === '' ? this.labelField : this.queryKey
        const params = {
          [paramKey]: val
        }
        this.remoteMethod(params, options => {
          let result
          if (options.length > 0) {
            options.forEach(option => {
              this.chosenOptions.push({
                [this.valueField]: option[this.valueField],
                [this.labelField]: option[this.labelField]
              })
            })
            result = val
          } else {
            result = ''
          }
          this.$nextTick(() => {
            this.chosenData = result
          })
        })
      } else {
        this.chosenData = val
      }
    },
    loadOptions() {
      if (this.remote) {
        const paramKey = this.queryKey === '' ? this.labelField : this.queryKey
        const params = {
          [paramKey]: this.searchKey === '' ? null : this.searchKey
        }
        Object.assign(params, this.pagination)
        if (!this.noMore) {
          this.scrollLoading = true
          this.remoteMethod(params, (options, total) => {
            this.scrollLoading = false
            if (!options || options.length < this.pagination.pageSize) {
              this.noMore = true
            }
            if (options.length > 0) {
              options.forEach(option => {
                this.chosenOptions.push({
                  [this.valueField]: option[this.valueField],
                  [this.labelField]: option[this.labelField]
                })
              })
              if (total && this.chosenOptions.length >= total) {
                this.noMore = true
              }
              this.$emit('update:options', this.chosenOptions)
            }
            this.$nextTick(() => {
              this.pagination.pageNum++
            })
          })
        }
      }
    },
    onInputChange() {
      if (this.remote) {
        this.onSelectVisibleChange(true)
      } else {
        this.$refs.jSelect.handleQueryChange(this.searchKey)
      }
    },
    selectAllClick() {
      if (this.remote) {
        this.remoteAllSelected = !this.remoteAllSelected
        this.chosenData = []
      } else {
        this.chosenData = this.allSelected ? [] : this.chosenOptions.map(option => option[this.valueField])
      }
    },
    resetOption() {
      this.pagination.pageNum = 1
      this.chosenOptions = []
      this.noMore = false
    },
    onSelectVisibleChange(visible) {
      if (this.remote && visible) {
        this.resetOption()
        this.loadOptions()
      }
      if (this.remote && !visible) {
        this.searchKey = ''
      }
    },
    onChange(val) {
      this.$emit('change', val)
    }
  }
}
</script>

<style scoped>
.chosen-header {
  margin-top: 12px;
  padding-left: 20px;
  padding-right: 20px;
}

.chosen-container {
  /*padding-left: 20px;*/
  /*padding-right: 20px;*/
}

.chosen-divider {
  margin: 0 !important;
}

</style>
<style lang="scss" scoped>
@import "~element-ui/packages/theme-chalk/src/mixins/mixins";

@include b(select-dropdown) {
  .span-loading {
    padding: 0 20px;
    height: $--select-option-height;
    line-height: $--select-option-height;
    text-align: center;
  }
  .all-select-header {
    margin: 3px 0;

    &:hover {
      background-color: unset;
    }
    &.selected {
      .el-checkbox__inner {
        background-color: $--checkbox-checked-background-color;
        border-color: $--checkbox-checked-input-border-color;

        &::after {
          transform: rotate(45deg) scaleY(1);
        }
      }
    }
  }
  @include when(multiple) {
    & .infinite-list.selected li{
      color: $--select-option-selected-font-color;
      font-weight: bold;
      &::after {
        position: absolute;
        right: 20px;
        font-family: 'element-icons';
        content: "\e6da";
        font-size: 12px;
        font-weight: bold;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
    }
    & .el-select-dropdown__item {
      &.selected {
        .el-checkbox__inner {
          background-color: $--checkbox-checked-background-color;
          border-color: $--checkbox-checked-input-border-color;

          &::after {
            transform: rotate(45deg) scaleY(1);
          }
        }
      }

      //&::after {
      //  content: ''
      //}
    }
  }
}
</style>

<template>
  <div :class="{ 'search-container' : isSimple, 'white' : isSimple, }">
    <div :class="{ 'search-container-main' : isSimple, 'white' : isSimple }">
      <el-form
        ref="queryForm"
        :inline="inline"
        :model="validatorDate"
        :label-position="labelPosition"
        :rules="rules"
        @submit.native.prevent
      >
        <div class="search-container-main-pop" :class="{ 'white' : isSimple }">
          <from-item
            v-if="!selectMultLineCheckBox"
            ref="fromItemRef"
            :form-data="searchData"
            :auto="auto"
            :is-show-lable="isShowLable"
            is-select
            :show-special-slot="showSpecialSlot"
            @get-data="getData"
            @update:form-data="updateFormData"
            @update-form-data-init="updateFormDataInit"
          >
            <template slot-scope="scope">
              <slot
                :data="scope.data"
              />
            </template>
            <template v-if="!showSmallButtom && showButton" slot="end">
              <el-form-item>
                <label class="el-form-item__label btn_right" v-html="nbsp" />
                <el-button
                  type="primary"
                  @click="search"
                >{{ $t('common.query') }}
                </el-button>
                <el-button @click="resetSelect">{{ $t('common.reset') }}</el-button>
              </el-form-item>
            </template>
          </from-item>
          <select-from-item
            v-if="selectMultLineCheckBox"
            ref="fromItemRef2"
            :form-data="searchData"
            :auto="auto"
            :is-show-lable="isShowLable"
            :show-special-slot="showSpecialSlot"
            :select-mult-line-check-box="selectMultLineCheckBox"
            @get-data="getData"
            @update:form-data="updateFormData"
            @update-form-data-init="updateFormDataInit"
            @show="show"
            @after-enter="afterEnter"
            @after-leave="afterLeave"
          >
            <template slot-scope="scope">
              <slot
                :data="scope.data"
              />
            </template>
            <template v-if="!showSmallButtom && showButton" slot="end">
              <el-form-item>
                <label class="el-form-item__label btn_right" v-html="nbsp" />
                <el-button
                  type="primary"
                  @click="search"
                >{{ $t('common.query') }}
                </el-button>
                <el-button @click="resetSelect">{{ $t('common.reset') }}</el-button>
              </el-form-item>
            </template>
          </select-from-item>
        </div>
      </el-form>
      <div v-if="showSmallButtom && showButton" slot="end" class="btn_bottom_right">
        <el-button
          type="primary"
          @click="search"
        >{{ $t('common.query') }}
        </el-button>
        <el-button @click="resetSelect">{{ $t('common.reset') }}</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import 'element-ui/lib/theme-chalk/base.css'
import FromItem from '@/components/DataForm/formItem'
import SelectFromItem from '@/components/SelectFormItem/selectFormItem'
export default {
  name: 'PopDataSelect',
  components: {
    FromItem,
    SelectFromItem
  },
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    rules: {
      type: Object,
      default: null
    },
    isSimple: {
      type: Boolean,
      default: true
    },
    isSelect: {
      type: Boolean,
      default: false
    },
    inline: {
      type: Boolean,
      default: true
    },
    labelPosition: {
      type: String,
      default: 'top'
    },
    isShowLable: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    auto: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    showSmallButtom: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    showButton: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    showSpecialSlot: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    selectMultLineCheckBox: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data() {
    return {
      size: 'medium',
      nbsp: this.$constant.nbsp,
      validatorDate: {}
    }
  },
  create() {
    this.$nextTick(() => {
      this.validatorDate = this.$refs.fromItemRef.getAllParams()
    })
  },
  mounted() {
  },
  methods: {
    getData(data) {
      this.validatorDate = data
    },
    updateFormData(val) {
      this.$emit('update:search-data', val)
    },
    updateFormDataInit(val) {
      this.$emit('update-search-data-init', val)
    },
    // 查询
    search() {
      this.$refs.queryForm.validate(valid => {
        if (valid) {
          this.$refs.fromItemRef.doClose()
          const params = this.$refs.fromItemRef.getAllParams()
          this.$emit('return-search', params)
        }
      })
    },
    doClose() {
      this.$refs.fromItemRef.doClose()
    },
    focus(name) {
      this.$refs.fromItemRef.focus(name)
    },
    setFormData(val) {
      this.$refs.fromItemRef.setFormData(val)
    },
    blur(name) {
      this.$refs.fromItemRef.blur(name)
    },
    select(name) {
      this.$refs.fromItemRef.select(name)
    },
    resetSelect() {
      this.$refs.fromItemRef.resetAllField()
      this.$emit('return-reset')
    },
    show() {
      this.$emit('show')
    },
    afterEnter() {
      this.$emit('after-enter')
    },
    afterLeave() {
      this.$emit('after-leave')
    },
    initFormData() {
      if (!this.selectMultLineCheckBox) {
        this.$refs.fromItemRef.initFormItemData()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.btn_right {
  margin-bottom: 3px;
}
.app-container .search-container .white{
  background-color: #FFFFFF;
}
.app-container .search-container .search-container-main .white{
  background-color: #FFFFFF;
}
.btn_bottom_right {
  position: absolute;
  right: 12px;
  bottom: 10px;
}
.btn_bottom_right .el-button{
  font-size: 12px;
  padding: 1px 8px;
  height: 28px;
  line-height: 24px;
}
</style>

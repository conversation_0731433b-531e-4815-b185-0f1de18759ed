import Cookies from 'js-cookie'
import storage from 'good-storage'

const Token<PERSON>ey = 'sp_token'
const langKey = 'lang'
const roleKey = 'role'
const nameKey = 'name'

const projectInfoKey = 'projectInfo'
const userIdKey = 'userId'

export function getProjectInfo() {
  return storage.session.get(projectInfoKey)
}
export function setProjectInfo(projectInfo) {
  return storage.session.set(projectInfoKey, projectInfo)
}

export function getToken() {
  return storage.session.get(TokenKey)
}

export function setToken(token) {
  return storage.session.set(TokenKey, token)
}

export function removeToken() {
  return storage.session.remove(TokenKey)
}

export function getLang() {
  return Cookies.get(langKey)
}

export function setLang(lang) {
  return Cookies.set(langKey, lang)
}

export function getRole() {
  return storage.session.get(roleKey)
}

export function setRole(role) {
  return storage.session.set(roleKey, role)
}

export function getName() {
  return storage.session.get(name<PERSON><PERSON>)
}

export function setName(name) {
  return storage.session.set(name<PERSON><PERSON>, name)
}

export function setUserId(brandCode) {
  return storage.session.set(userIdKey, brandCode)
}
export function getUserId() {
  return storage.session.get(userIdKey)
}

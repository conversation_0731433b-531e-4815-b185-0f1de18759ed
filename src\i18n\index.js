import Vue from 'vue'
import VueI18n from 'vue-i18n'
import Cookies from 'js-cookie'

import zhLocale from '@/i18n/lang/zh.js'
import enLocale from '@/i18n/lang/en.js'
import elementZhLocale from '@/i18n/lang/element-ui-zh'
import elementEnLocale from '@/i18n/lang/element-ui-en'

Vue.use(VueI18n)

const i18n = new VueI18n({
  locale: Cookies.get('language') || 'zh',
  messages: {
    en: {
      ...enLocale,
      ...elementEnLocale
    },
    zh: {
      ...zhLocale,
      ...elementZhLocale
    }
  }
})

export default i18n

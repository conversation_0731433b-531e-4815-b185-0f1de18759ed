<template>
  <div class="app-container statistic">
    <div class="statistic_left">
      <el-card class="card_box">
        <div slot="header" class="clearfix">
          <div class="title">扫码数量展示图</div>
          <el-button-group style="position: absolute;right: 20px;top: 20px">
            <el-button :style="{backgroundColor:clickIndex===7?'#0D6CE4':'#fff',color:clickIndex===7?'#fff':'black',marginRight:'8px'}" @click="changeTime(7)">近7天</el-button>
            <el-button :style="{backgroundColor:clickIndex===15?'#0D6CE4':'#fff',color:clickIndex===15?'#fff':'black',marginRight:'8px'}" @click="changeTime(15)">近15天</el-button>
            <el-button :style="{backgroundColor:clickIndex===30?'#0D6CE4':'#fff',color:clickIndex===30?'#fff':'black'}" @click="changeTime(30)">近30天</el-button>
          </el-button-group>
        </div>

        <div style="height: 310px">
          <div>
            <chart v-if="chartOne.xAxis.data&&chartOne.xAxis.data.length>0" style="height: 310px" id-name="chartBarTwo" :reset-options="chartOne" type="line" />
            <el-empty v-else class="empty" description="暂无数据" />
          </div>
        </div>
      </el-card>
      <div class="flex-row">
        <el-card class="card_box title-count-card">
          <div slot="header" class="clearfix">
            <span class="title">区域统计扫码数</span>
          </div>
          <div style="height: 350px">
            <chart
              v-if="chartTwo.xAxis.data&&chartTwo.xAxis.data.length>0"
              style="height: 350px"
              id-name="chartBarOne1"
              :reset-options="chartTwo"
              type="bar"
            />
            <el-empty v-else class="empty" description="暂无数据" />
          </div>
        </el-card>
        <el-card class="card_box title-count-card">
          <div slot="header" class="clearfix">
            <span class="title">近一周标识生成量</span>
          </div>
          <div style="height: 350px">
            <chart
              v-if="chartThree.xAxis.data&&chartThree.xAxis.data.length>0"
              style="height: 350px"
              id-name="chartBarOne11"
              :reset-options="chartThree"
              type="bar"
            />
            <el-empty v-else class="empty" description="暂无数据" />
          </div>
        </el-card>
      </div>

    </div>
    <div class="statistic_right">
      <el-card class="card_box title-count-card">
        <div slot="header" class="clearfix">
          <span class="title">标识排行</span>
        </div>
        <div v-if="scanCodeRankingList.length>0" style="height: 252px">
          <div v-for="(e,i) in scanCodeRankingList" :key="i" class="title-count-box">
            <div
              v-if="i<3"
              class="icon"
              :style="{'background-image':`url(${require(`@/assets/img/icon_${i+1}.png`)})`}"
            />
            <div v-else class="iconLast">{{ i+1 }}</div>
            <!--            <div><img src="@/assets/icon/logo1.png"></div>-->
            <div class="text">
              <p>{{ e.idisCode }}</p>
              <p>{{ e.productName }}</p>
            </div>
            <div class="count" :style="{'color':i<3?`${colorList[i]}`:'#666',fontWeight:i<3?'bolder':''}">{{ e.scanCount }}次</div>
          </div>
        </div>
        <el-empty v-else style="height: 305px" class="empty" description="暂无数据" />
      </el-card>
      <el-card class="card_box title-count-card">
        <div slot="header" class="clearfix">
          <span class="title">产品排行</span>
        </div>
        <div v-if="scanCodeRankingList.length>0" style="height: 200px">
          <div v-for="(e,i) in productRankingList" :key="i" class="product-sort-box">
            <div
              v-if="i<3"
              class="iconimg"
              :style="{'background-image':`url(${require(`@/assets/img/Frame-${i+1}.png`)})`}"
            />
            <div v-else class="icon" :style="{'background-color':i<3?'#e88316':'#b5b5b5'}">{{ i + 1 }}</div>
            <div class="text">
              {{ e.productName }}
            </div>
            <div class="count" :style="{'color':i<3?`${colorList[i]}`:'#666',fontWeight:i<3?'bolder':''}">{{ e.scanQuantity }}次</div>
          </div>
        </div>
        <div v-else style="height: 120px;">
          <el-empty style="height: 140px;width: 87px ;padding-top: 0px" class="empty" description="暂无数据" />
        </div>
      </el-card>
      <el-card class="card_box title-count-card">
        <div slot="header" class="clearfix">
          <span class="title">客户端类型</span>
        </div>
        <div style="height: 169px ;position: relative">
          <div v-if="chartFour.series[0].data&&chartFour.series[0].data.length>0">
            <chart style="height: 169px" id-name="chartFour" :reset-options="chartFour" type="bar" />
            <div style="position: absolute;right: 0px;top: 35px;display: flex;width: 250px;flex-wrap: wrap">
              <div v-for="(item,i) in chartFour.series[0].data" :key="item.name" style="flex: 50%;margin-bottom: 20px">
                <div style="height: 36px;margin-bottom: 10px;display: flex;align-items: center;">
                  <div :style="{backgroundColor:colorPieList[i]}" style="width: 3px;background-color: black;height: 100%;margin-right: 15px;border-radius: 2px;" />
                  <div>
                    <div>{{ item.name }}</div>
                    <div style="font-size: 16px;margin-top: 2px">{{ item.percentage?item.percentage:'-' }}</div>
                  </div>
                </div>
              </div>

            </div>
          </div>
          <el-empty v-else style="padding-top: 0 !important;" class="empty" description="暂无数据" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>

import Chart from '@/components/Chart/Chart.vue'
import acc from '@/api/acc/acc'

export default {
  name: 'Statistic',
  components: {
    Chart

  },
  data() {
    return {
      colorList: ['#FF9900', '#0A9CEA', '#EA6444'],
      colorPieList: ['#0FDFD8', '#20A3E5', '#264BE5', '#6E1CE4'],
      scanCodeRankingList: [],
      productRankingList: [],
      chartOne: {
        xAxis: {
          type: 'category',
          axisLabel: {
            color: 'black'// x轴字
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'black' // x轴线条
            }
          },
          boundaryGap: false, // 不保留边界间隙，确保x轴从头开始显示数据
          data: []
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: 'black'// x轴字
          },
          axisTick: {
            show: true
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'black' // y轴线条
            }
          }

        },
        grid: {
          left: '30px',
          right: '40px',
          bottom: '10px',
          top: '40px',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          formatter: `
            {b} <br />
             <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#4BB4AA;"></span>数量：{c}`
        },
        series: [
          {
            data: [],
            type: 'line',
            smooth: true,
            areaStyle: {
              color: {
                type: 'linear', // 定义渐变类型为线性渐变
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 1, color: '#fff' // 渐变起始颜色
                  },
                  {
                    offset: 0.5, color: '#CADCFF' // 渐变起始颜色
                  },
                  {
                    offset: 0, color: '#0D6CE4' // 渐变结束颜色
                  }
                ],
                global: false // 设置为 false 表示使用局部坐标系
              }
            },

            itemStyle: {
              normal: {
                color: {
                  type: 'radial', // 定义渐变类型为径向渐变
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [
                    {
                      offset: 1, color: '#0D6CE4' // 渐变结束颜色
                    }
                  ],
                  global: false // 设置为 false 表示使用局部坐标系
                }
              }

            }
          }
        ]
      },
      clickIndex: 7,
      chartTwo: {
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: true
          },
          axisLine: {
            show: true
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: `
      {b} <br />
      <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#67B2FF;"></span>扫码数量：{c}`
        },
        grid: {
          left: '30px',
          right: '30px',
          bottom: '10px',
          top: '30px',
          containLabel: true
        },
        series: [
          {
            data: [],
            showBackground: false,
            type: 'bar',
            barWidth: '40%', // 这里设置了宽度为容器宽度的40%
            barMaxWidth: 30, // 设置柱子的最大宽度为30
            color: {
              type: 'linear', // 定义渐变类型为线性渐变
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 1, color: '#fff' // 渐变起始颜色
                },
                {
                  offset: 0.5, color: '#CADCFF' // 渐变起始颜色
                },
                {
                  offset: 0, color: '#0D6CE4' // 渐变结束颜色
                }
              ],
              global: false // 设置为 false 表示使用局部坐标系
            }
          }
        ]
      },

      chartThree: {
        xAxis: {
          type: 'category',
          axisLine: {
            show: true
          },
          boundaryGap: false, // 不保留边界间隙，确保x轴从头开始显示数据
          data: []
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: true
          },
          axisLine: {
            show: true
          }

        },
        grid: {
          left: '30px',
          right: '30px',
          bottom: '10px',
          top: '30px',
          containLabel: true
        },
        series: [
          {
            data: [],
            type: 'line',
            smooth: true,
            areaStyle: {
              color: {
                type: 'linear', // 定义渐变类型为线性渐变
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 1, color: '#fff' // 渐变起始颜色
                  },
                  {
                    offset: 0.5, color: '#CADCFF' // 渐变起始颜色
                  },
                  {
                    offset: 0, color: '#0D6CE4' // 渐变结束颜色
                  }
                ],
                global: false // 设置为 false 表示使用局部坐标系
              }
            },

            itemStyle: {
              normal: {
                color: {
                  type: 'radial', // 定义渐变类型为径向渐变
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [
                    {
                      offset: 1, color: '#0D6CE4' // 渐变结束颜色
                    }
                  ],
                  global: false // 设置为 false 表示使用局部坐标系
                }
              }

            },
            lineStyle: {
              color: '#228AE6' // 折线颜色
            }
          }
        ]
      },
      chartFour: {
        tooltip: {
          trigger: 'item',
          color: ['#0FDFD8', '#20A3E5', '#264BE5', '#6E1CE4'],
          confine: true, // 解决超出外部容器后被遮挡问题
          formatter: `
            客户端类型<br />
            {b} ：{c}({d}%)`
        },
        legend: {
          show: false

        },
        xAxis: {
          show: false
        },
        yAxis: {
          show: false
        },
        series: [
          {
            type: 'pie',
            center: ['30%', '50%'],
            radius: ['40%', '70%'],
            minAngle: 15, // 最小角度
            startAngle: 270, // 起始角度
            itemStyle: {
              borderRadius: 5,
              borderWidth: 2, // 设置扇区间隙
              borderColor: '#fff' // 设置间隙颜色为白色
            },
            avoidLabelOverlap: false,
            labelLine: {
              normal: {
                length: 5
              }
            },
            label: {
              show: false, // 关闭 label 显示
              position: 'center'
            },
            data: []
          }
        ]
      }
    }
  },
  watch: {},
  mounted() {

  },
  created() {
    this.getChartOne()
    this.getChartTwo()
    this.getCharThree()
    this.getChartFour()
    this.getScanCodeRanking()
    this.getProductRanking()
  },
  methods: {
    changeTime(val) {
      this.clickIndex = val
      this.getChartOne()
    },
    getChartOne() {
      acc.quantityDisplayListApi({ queryDays: this.clickIndex }).then((res) => {
        this.$set(this.chartOne.xAxis, 'data', res.data.xaxis)
        this.$set(this.chartOne.series[0], 'data', res.data.yaxis)
      })
    },
    getChartTwo() {
      acc.areaScanningCodeList().then((res) => {
        this.$set(this.chartTwo.xAxis, 'data', res.data.xaxis)
        this.$set(this.chartTwo.series[0], 'data', res.data.yaxis)
      })
    },
    getCharThree() {
      acc.weeklyCodeList().then((res) => {
        this.$set(this.chartThree.xAxis, 'data', res.data.xaxis)
        this.$set(this.chartThree.series[0], 'data', res.data.yaxis)
      })
    },
    getChartFour() {
      acc.clientTypeList().then((res) => {
        this.$set(this.chartFour.series[0], 'data', res.data)
        console.log(this.chartFour.series[0].data,888)
      })
    },
    getScanCodeRanking() {
      acc.scanCodeRankingList().then((res) => {
        this.scanCodeRankingList = res.data
      })
    },
    getProductRanking() {
      acc.productRankingList().then((res) => {
        this.productRankingList = res.data
      })
    }
  }

}
</script>

<style scoped lang="scss">
.statistic {
  overflow: auto;
  display: flex;

  .title {
    font-size: 14px;
    font-weight: bold;
  }

  ::v-deep .el-card__header {
    border: 0;
  }

  ::v-deep .el-button {
    border-radius: 0;
  }

  ::v-deep .chart-box {
    margin-top: 0 !important;
    border-bottom: none !important;
  }

  .card_box {
    position: relative;
    margin-bottom: 10px;
    flex: 1;
    margin-right: 10px;
    .title{
      position: absolute;
      top: 20px;
    }
  }

  .empty {
    text-align: center;
    width: 100px;
    margin-left: calc(50% - 50px);
    padding-top: 50px;
    overflow: hidden;
  }

  .statistic_left {
    flex: 1;
    width: 0;
  }

  .statistic_right {
    width: 33.3%;

    .title-count-card {
      ::v-deep .el-card__body {
        padding: 12px 20px !important;
      }
    }

    .title-count-box {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #dedede;
      padding: 2px 0;
      .icon {
        flex-shrink: 0;
        width: 20px;
        height: 26px;
        padding-top: 13px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        text-align: center;
        color: #FFFFFF;
        vertical-align: bottom;
      }

      .iconLast {
        width: 18.5px;
        height: 18.5px;
        border-radius: 50%;
        flex-shrink: 0;
        color: #FFFFFF;
        background-color: #b5b5b5;
        padding-top: 3px;
        text-align: center;
      }

      img {
        width: 40px;
        margin: 0 10px
      }

      .text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        min-width: 100px;
        p {
          margin:5px 8px;
          font-size: 14px;
          color: #666;
        }
      }

      .count {
        flex-shrink: 0;
        min-width: 30px;
        font-size: 16px;
        color: orange;
      }
    }

    .title-count-box:last-child {
      border-bottom: 0px
    }

    .product-sort-box {
      display: flex;
      align-items: center;
      color: #666;
      border-bottom: 1px solid #dedede;
      padding: 8px;
      .iconimg {
        width: 16px;
        height: 19.556px;

      }
      .icon {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        color: #FFFFFF;
        padding-top: 2px;
        text-align: center;
      }

      .text {
        flex: 1;
        font-size: 14px;
        margin: 4px 10px;
      }

      .count {
        font-size: 16px;
      }

      ::v-deep .el-card__body {
        padding: 0 20px;
      }
    }
    .product-sort-box:last-child{
      border-bottom: 0px;
    }
  }
}
</style>

@import "~element-ui/packages/theme-chalk/src/mixins/mixins";
@import "~element-ui/packages/theme-chalk/src/common/var";
// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}




// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}


//树形结构
.el-tree{
  .el-icon-caret-right:before{     // 未展开
   content: '\e6d9';
   font-size: 18px;
 }
  .expanded:before{     // 展开
   content: "\e6d8";
   font-size: 18px;
 }
  .expanded {   // 展开不旋转
   -webkit-transform: rotate(0deg);
   transform: rotate(0deg);
 }
}

.el-table__expand-column .cell {
        .el-table__expand-icon--expanded{
           transform: rotate(180deg);
          }
       .el-table__expand-icon{
         .el-icon-arrow-right:before {
            content: "\e78f";
            color: $--color-primary;
          }
     }
 }

// 增加周范围选择样式
@include b(date-editor) {
  @include m((daterange, timerange, weekrange)) {
    &.el-input,
    &.el-input__inner {
      width: 350px;
    }
  }
}

@include b(date-table) {
  @include when(week-mode) {
    .el-date-table__row {
      &:hover {
        td:first-child.in-range div {
          margin-left: 0;
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }

        td:last-child.in-range div {
          margin-right: 0;
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }
      }
    }
  }
}

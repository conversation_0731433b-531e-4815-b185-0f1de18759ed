<script>
import { Table } from 'vxe-table'
import dom from 'vxe-table/packages/tools/dom'
const getEventTargetNode = dom.getEventTargetNode
const hasClass = dom.hasClass
function findParentWithClass(element, className) {
  if (!element.parentNode) {
    // 如果已经到达了根节点仍然没有找到匹配的class，则返回false
    return false
  }

  if (element.parentNode.classList?.contains(className)) {
    // 如果父元素的class包含目标class，则返回true
    return true
  }

  // 继续递归查找父元素的父元素
  return findParentWithClass(element.parentNode, className)
}
export default {
  name: 'VxeTable',
  extends: Table,
  methods: {
    handleGlobalMousedownEvent(evnt) {
      if (findParentWithClass(evnt.target, 'el-popper')) {
        return
      }

      var _this27 = this
      var $el = this.$el
      var $refs = this.$refs
      var $xegrid = this.$xegrid
      var $toolbar = this.$toolbar
      var mouseConfig = this.mouseConfig
      var editStore = this.editStore
      var ctxMenuStore = this.ctxMenuStore
      var editOpts = this.editOpts
      var filterStore = this.filterStore
      var getRowNode = this.getRowNode
      var actived = editStore.actived
      var ctxWrapper = $refs.ctxWrapper
      var filterWrapper = $refs.filterWrapper
      var validTip = $refs.validTip
      if (filterWrapper) {
        if (getEventTargetNode(evnt, $el, 'vxe-cell--filter').flag) {
          // 如果点击了筛选按钮
        } else if (getEventTargetNode(evnt, filterWrapper.$el).flag) {
          // 如果点击筛选容器
        } else {
          if (!getEventTargetNode(evnt, document.body, 'vxe-table--ignore-clear').flag) {
            this.preventEvent(evnt, 'event.clearFilter', filterStore.args, this.closeFilter)
          }
        }
      }
      // 如果已激活了编辑状态
      if (actived.row) {
        if (!(editOpts.autoClear === false)) {
          // 如果是激活状态，点击了单元格之外
          var cell = actived.args.cell
          if (!cell || !getEventTargetNode(evnt, cell).flag) {
            if (validTip && getEventTargetNode(evnt, validTip.$el).flag) {
              // 如果是激活状态，且点击了校验提示框
            } else if (!this.lastCallTime || this.lastCallTime + 50 < Date.now()) {
              if (!getEventTargetNode(evnt, document.body, 'vxe-table--ignore-clear').flag) {
                // 如果手动调用了激活单元格，避免触发源被移除后导致重复关闭
                this.preventEvent(evnt, 'event.clearActived', actived.args, function() {
                  var isClearActived
                  if (editOpts.mode === 'row') {
                    var rowNode = getEventTargetNode(evnt, $el, 'vxe-body--row')
                    // row 方式，如果点击了不同行
                    isClearActived = rowNode.flag ? getRowNode(rowNode.targetElem).item !== actived.args.row : false
                  } else {
                    // cell 方式，如果是非编辑列
                    isClearActived = !getEventTargetNode(evnt, $el, 'col--edit').flag
                  }
                  // 如果点击表头行，则清除激活状态
                  if (!isClearActived) {
                    isClearActived = getEventTargetNode(evnt, $el, 'vxe-header--row').flag
                  }
                  // 如果点击表尾行，则清除激活状态
                  if (!isClearActived) {
                    isClearActived = getEventTargetNode(evnt, $el, 'vxe-footer--row').flag
                  }
                  // 如果固定了高度且点击了行之外的空白处，则清除激活状态
                  if (!isClearActived && _this27.height && !_this27.overflowY) {
                    var bodyWrapperElem = evnt.target
                    if (hasClass(bodyWrapperElem, 'vxe-table--body-wrapper')) {
                      isClearActived = evnt.offsetY < bodyWrapperElem.clientHeight
                    }
                  }
                  if (isClearActived ||
                      // 如果点击了当前表格之外
                      !getEventTargetNode(evnt, $el).flag) {
                    setTimeout(function() {
                      return _this27.clearEdit(evnt)
                    })
                  }
                })
              }
            }
          }
        }
      } else if (mouseConfig) {
        if (!getEventTargetNode(evnt, $el).flag && !($xegrid && getEventTargetNode(evnt, $xegrid.$el).flag) && !(ctxWrapper && getEventTargetNode(evnt, ctxWrapper.$el).flag) && !($toolbar && getEventTargetNode(evnt, $toolbar.$el).flag)) {
          this.clearSelected()
          if (!getEventTargetNode(evnt, document.body, 'vxe-table--ignore-areas-clear').flag) {
            this.preventEvent(evnt, 'event.clearAreas', {}, function() {
              _this27.clearCellAreas()
              _this27.clearCopyCellArea()
            })
          }
        }
      }
      // 如果配置了快捷菜单且，点击了其他地方则关闭
      if (ctxMenuStore.visible && ctxWrapper && !getEventTargetNode(evnt, ctxWrapper.$el).flag) {
        this.closeMenu()
      }
      // 最后激活的表格
      this.isActivated = getEventTargetNode(evnt, ($xegrid || this).$el).flag
    }
  }
}
</script>

module.exports =
/** ****/ (function(modules) { // webpackBootstrap
    /** ****/ 	// The module cache
    /** ****/ 	var installedModules = {}
    /** ****/
    /** ****/ 	// The require function
    /** ****/ 	function __webpack_require__(moduleId) {
      /** ****/
      /** ****/ 		// Check if module is in cache
      /** ****/ 		if (installedModules[moduleId]) {
        /** ****/ 			return installedModules[moduleId].exports
        /** ****/ 		}
      /** ****/ 		// Create a new module (and put it into the cache)
      /** ****/ 		var module = installedModules[moduleId] = {
        /** ****/ 			i: moduleId,
        /** ****/ 			l: false,
        /** ****/ 			exports: {}
        /** ****/ 		}
      /** ****/
      /** ****/ 		// Execute the module function
      /** ****/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__)
      /** ****/
      /** ****/ 		// Flag the module as loaded
      /** ****/ 		module.l = true
      /** ****/
      /** ****/ 		// Return the exports of the module
      /** ****/ 		return module.exports
      /** ****/ 	}
    /** ****/
    /** ****/
    /** ****/ 	// expose the modules object (__webpack_modules__)
    /** ****/ 	__webpack_require__.m = modules
    /** ****/
    /** ****/ 	// expose the module cache
    /** ****/ 	__webpack_require__.c = installedModules
    /** ****/
    /** ****/ 	// define getter function for harmony exports
    /** ****/ 	__webpack_require__.d = function(exports, name, getter) {
      /** ****/ 		if (!__webpack_require__.o(exports, name)) {
        /** ****/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter })
        /** ****/ 		}
      /** ****/ 	}
    /** ****/
    /** ****/ 	// define __esModule on exports
    /** ****/ 	__webpack_require__.r = function(exports) {
      /** ****/ 		if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {
        /** ****/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' })
        /** ****/ 		}
      /** ****/ 		Object.defineProperty(exports, '__esModule', { value: true })
      /** ****/ 	}
    /** ****/
    /** ****/ 	// create a fake namespace object
    /** ****/ 	// mode & 1: value is a module id, require it
    /** ****/ 	// mode & 2: merge all properties of value into the ns
    /** ****/ 	// mode & 4: return value when already ns object
    /** ****/ 	// mode & 8|1: behave like require
    /** ****/ 	__webpack_require__.t = function(value, mode) {
      /** ****/ 		if (mode & 1) value = __webpack_require__(value)
      /** ****/ 		if (mode & 8) return value
      /** ****/ 		if ((mode & 4) && typeof value === 'object' && value && value.__esModule) return value
      /** ****/ 		var ns = Object.create(null)
      /** ****/ 		__webpack_require__.r(ns)
      /** ****/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value })
      /** ****/ 		if (mode & 2 && typeof value !== 'string') for (var key in value) __webpack_require__.d(ns, key, function(key) { return value[key] }.bind(null, key))
      /** ****/ 		return ns
      /** ****/ 	}
    /** ****/
    /** ****/ 	// getDefaultExport function for compatibility with non-harmony modules
    /** ****/ 	__webpack_require__.n = function(module) {
      /** ****/ 		var getter = module && module.__esModule
      /** ****/ 			? function getDefault() { return module['default'] }
      /** ****/ 			: function getModuleExports() { return module }
      /** ****/ 		__webpack_require__.d(getter, 'a', getter)
      /** ****/ 		return getter
      /** ****/ 	}
    /** ****/
    /** ****/ 	// Object.prototype.hasOwnProperty.call
    /** ****/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property) }
    /** ****/
    /** ****/ 	// __webpack_public_path__
    /** ****/ 	__webpack_require__.p = '/dist/'
    /** ****/
    /** ****/
    /** ****/ 	// Load entry module and return exports
    /** ****/ 	return __webpack_require__(__webpack_require__.s = 80)
    /** ****/ })({

    /***/ 1:
    /***/ function(module, __webpack_exports__, __webpack_require__) {
      'use strict'
      /* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, 'a', function() { return normalizeComponent })
      /* globals __VUE_SSR_CONTEXT__ */

      // IMPORTANT: Do NOT use ES2015 features in this file (except for modules).
      // This module is a runtime utility for cleaner component module output and will
      // be included in the final webpack user bundle.

      function normalizeComponent(
        scriptExports,
        render,
        staticRenderFns,
        functionalTemplate,
        injectStyles,
        scopeId,
        moduleIdentifier, /* server only */
        shadowMode /* vue-cli only */
      ) {
        // Vue.extend constructor export interop
        var options = typeof scriptExports === 'function'
          ? scriptExports.options
          : scriptExports

        // render functions
        if (render) {
          options.render = render
          options.staticRenderFns = staticRenderFns
          options._compiled = true
        }

        // functional template
        if (functionalTemplate) {
          options.functional = true
        }

        // scopedId
        if (scopeId) {
          options._scopeId = 'data-v-' + scopeId
        }

        var hook
        if (moduleIdentifier) { // server build
          hook = function(context) {
            // 2.3 injection
            context =
        context || // cached call
        (this.$vnode && this.$vnode.ssrContext) || // stateful
        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional
            // 2.2 with runInNewContext: true
            if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {
              context = __VUE_SSR_CONTEXT__
            }
            // inject component styles
            if (injectStyles) {
              injectStyles.call(this, context)
            }
            // register component module identifier for async chunk inferrence
            if (context && context._registeredComponents) {
              context._registeredComponents.add(moduleIdentifier)
            }
          }
          // used by ssr in case component is cached and beforeCreate
          // never gets called
          options._ssrRegister = hook
        } else if (injectStyles) {
          hook = shadowMode
            ? function() {
              injectStyles.call(
                this,
                (options.functional ? this.parent : this).$root.$options.shadowRoot
              )
            }
            : injectStyles
        }

        if (hook) {
          if (options.functional) {
            // for template-only hot-reload because in that case the render fn doesn't
            // go through the normalizer
            options._injectStyles = hook
            // register for functional component in vue file
            var originalRender = options.render
            options.render = function renderWithStyleInjection(h, context) {
              hook.call(context)
              return originalRender(h, context)
            }
          } else {
            // inject component registration as beforeCreate hook
            var existing = options.beforeCreate
            options.beforeCreate = existing
              ? [].concat(existing, hook)
              : [hook]
          }
        }

        return {
          exports: scriptExports,
          options: options
        }
      }
      /***/ },

    /***/ 2:
    /***/ function(module, exports) {
      module.exports = require('element-ui/lib/utils/dom')
      /***/ },

    /***/ 3:
    /***/ function(module, exports) {
      module.exports = require('element-ui/lib/utils/util')
      /***/ },

    /***/ 6:
    /***/ function(module, exports) {
      module.exports = require('@/utils/vue-popper')
      /***/ },

    /***/ 7:
    /***/ function(module, exports) {
      module.exports = require('vue')
      /***/ },

    /***/ 80:
    /***/ function(module, __webpack_exports__, __webpack_require__) {
      'use strict'
      // ESM COMPAT FLAG
      __webpack_require__.r(__webpack_exports__)

      // CONCATENATED MODULE: ./node_modules/_vue-loader@15.9.7@vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/_vue-loader@15.9.7@vue-loader/lib??vue-loader-options!./packages/popover/src/main.vue?vue&type=template&id=52060272&
      var render = function() {
        var _vm = this
        var _h = _vm.$createElement
        var _c = _vm._self._c || _h
        return _c(
          'span',
          {
            directives: [
              {
                name: 'clickoutside',
                rawName: 'v-clickoutside',
                value: _vm.clickOutSide,
                expression: 'clickOutSide'
              }
            ]
          },
          [
            _c(
              'transition',
              {
                attrs: { name: _vm.transition },
                on: {
                  'after-enter': _vm.handleAfterEnter,
                  'after-leave': _vm.handleAfterLeave
                }
              },
              [
                _c(
                  'div',
                  {
                    directives: [
                      {
                        name: 'show',
                        rawName: 'v-show',
                        value: !_vm.disabled && _vm.showPopper,
                        expression: '!disabled && showPopper'
                      }
                    ],
                    ref: 'popper',
                    staticClass: 'el-popover el-popper',
                    class: [_vm.popperClass, _vm.content && 'el-popover--plain'],
                    style: { width: _vm.width + 'px' },
                    attrs: {
                      role: 'tooltip',
                      id: _vm.tooltipId,
                      'aria-hidden':
                  _vm.disabled || !_vm.showPopper ? 'true' : 'false'
                    }
                  },
                  [
                    _vm.title
                      ? _c('div', {
                        staticClass: 'el-popover__title',
                        domProps: { textContent: _vm._s(_vm.title) }
                      })
                      : _vm._e(),
                    _vm._t('default', [_vm._v(_vm._s(_vm.content))])
                  ],
                  2
                )
              ]
            ),
            _c(
              'span',
              { ref: 'wrapper', staticClass: 'el-popover__reference-wrapper' },
              [_vm._t('reference')],
              2
            )
          ],
          1
        )
      }
      var staticRenderFns = []
      render._withStripped = true

      // CONCATENATED MODULE: ./packages/popover/src/main.vue?vue&type=template&id=52060272&

      // EXTERNAL MODULE: external "element-ui/lib/utils/vue-popper"
      var vue_popper_ = __webpack_require__(6)
      var vue_popper_default = /* #__PURE__*/__webpack_require__.n(vue_popper_)

      // EXTERNAL MODULE: external "element-ui/lib/utils/dom"
      var dom_ = __webpack_require__(2)

      // EXTERNAL MODULE: external "element-ui/lib/utils/clickoutside"
      var clickoutside_ = __webpack_require__(9)
      var clickoutside_default = /* #__PURE__*/__webpack_require__.n(clickoutside_)

      // EXTERNAL MODULE: external "element-ui/lib/utils/util"
      var util_ = __webpack_require__(3)

      // CONCATENATED MODULE: ./node_modules/_babel-loader@7.1.5@babel-loader/lib!./node_modules/_vue-loader@15.9.7@vue-loader/lib??vue-loader-options!./packages/popover/src/main.vue?vue&type=script&lang=js&
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //
      //

      /* harmony default export */ var mainvue_type_script_lang_js_ = ({
        name: 'ElPopover',
        componentName: 'ElPopover',
        directives: { Clickoutside: clickoutside_default.a },
        mixins: [vue_popper_default.a],
        data: function data() {
          return {
            isFocus: false
          }
        },

        props: {
          trigger: {
            type: String,
            default: 'click',
            validator: function validator(value) {
              return ['click', 'focus', 'hover', 'manual', 'contextmenu'].indexOf(value) > -1
            }
          },
          openDelay: {
            type: Number,
            default: 0
          },
          closeDelay: {
            type: Number,
            default: 200
          },
          title: String,
          disabled: Boolean,
          content: String,
          reference: {},
          popperClass: String,
          width: {},
          visibleArrow: {
            default: true
          },
          arrowOffset: {
            type: Number,
            default: 0
          },
          transition: {
            type: String,
            default: 'fade-in-linear'
          },
          tabindex: {
            type: Number,
            default: 0
          },
          scrollHide: {
            type: Boolean,
            default: false
          },
          initCreate: {
            type: Boolean,
            default: false
          }
        },

        computed: {
          tooltipId: function tooltipId() {
            return 'el-popover-' + Object(util_['generateId'])()
          }
        },
        watch: {
          showPopper: function showPopper(val) {
            if (this.disabled) {
              return
            }
            val ? this.$emit('show') : this.$emit('hide')
          }
        },

        mounted: function mounted() {
          var _this = this

          var reference = this.referenceElm = this.reference || this.$refs.reference
          var popper = this.popper || this.$refs.popper

          if (!reference && this.$refs.wrapper.children) {
            reference = this.referenceElm = this.$refs.wrapper.children[0]
          }
          // 可访问性
          if (reference) {
            Object(dom_['addClass'])(reference, 'el-popover__reference')
            reference.setAttribute('aria-describedby', this.tooltipId)
            reference.setAttribute('tabindex', this.tabindex) // tab序列
            popper.setAttribute('tabindex', 0)

            if (this.trigger !== 'click' && this.trigger !== 'contextmenu') {
              Object(dom_['on'])(reference, 'focusin', function() {
                _this.handleFocus()
                var instance = reference.__vue__
                if (instance && typeof instance.focus === 'function') {
                  instance.focus()
                }
              })
              Object(dom_['on'])(popper, 'focusin', this.handleFocus)
              Object(dom_['on'])(reference, 'focusout', this.handleBlur)
              Object(dom_['on'])(popper, 'focusout', this.handleBlur)
            }
            Object(dom_['on'])(reference, 'keydown', this.handleKeydown)
            Object(dom_['on'])(reference, 'click', this.handleClick)
          }
          if (this.trigger === 'click') {
            Object(dom_['on'])(reference, 'click', this.doToggle)
            Object(dom_['on'])(document, 'click', this.handleDocumentClick)
          } else if (this.trigger === 'contextmenu') {
            Object(dom_['on'])(document, 'click', this.handleDocumentClick)
            Object(dom_['on'])(document, 'contextmenu', this.handleDocumentClick)
          } else if (this.trigger === 'hover') {
            Object(dom_['on'])(reference, 'mouseenter', this.handleMouseEnter)
            Object(dom_['on'])(popper, 'mouseenter', this.handleMouseEnter)
            Object(dom_['on'])(reference, 'mouseleave', this.handleMouseLeave)
            Object(dom_['on'])(popper, 'mouseleave', this.handleMouseLeave)
          } else if (this.trigger === 'focus') {
            if (this.tabindex < 0) {
              console.warn('[Element Warn][Popover]a negative taindex means that the element cannot be focused by tab key')
            }
            if (reference.querySelector('input, textarea')) {
              Object(dom_['on'])(reference, 'focusin', this.doShow)
              Object(dom_['on'])(reference, 'focusout', this.doClose)
            } else {
              Object(dom_['on'])(reference, 'mousedown', this.doShow)
              Object(dom_['on'])(reference, 'mouseup', this.doClose)
            }
          }
          this.$on('popper-scroll', function() {
            if (_this.showPopper && _this.scrollHide) _this.showPopper = !_this.showPopper
          })
          this.$nextTick(function() {
            if (_this.initCreate) _this.createPopper()
          })
          this.$on('el.popover.focus', function() {
            _this.isFocus = true
          })
          this.$on('el.popover.blur', function() {
            _this.isFocus = false
          })
        },
        beforeDestroy: function beforeDestroy() {
          this.cleanup()
        },
        deactivated: function deactivated() {
          this.cleanup()
        },

        methods: {
          clickOutSide: function clickOutSide() {
            this.$emit('click-out-side')
          },
          doToggle: function doToggle() {
            this.showPopper = !this.showPopper
          },
          doShow: function doShow() {
            this.showPopper = true
          },
          doClose: function doClose() {
            this.showPopper = false
          },
          handleFocus: function handleFocus() {
            Object(dom_['addClass'])(this.referenceElm, 'focusing')
            if (this.trigger === 'click' || this.trigger === 'focus') this.showPopper = true
          },
          handleClick: function handleClick() {
            Object(dom_['removeClass'])(this.referenceElm, 'focusing')
          },
          handleBlur: function handleBlur() {
            Object(dom_['removeClass'])(this.referenceElm, 'focusing')
            if (this.trigger === 'click' || this.trigger === 'focus') this.showPopper = false
          },
          handleMouseEnter: function handleMouseEnter() {
            var _this2 = this

            clearTimeout(this._timer)
            if (this.openDelay) {
              this._timer = setTimeout(function() {
                _this2.showPopper = true
              }, this.openDelay)
            } else {
              this.showPopper = true
            }
          },
          handleKeydown: function handleKeydown(ev) {
            if (ev.keyCode === 27 && this.trigger !== 'manual') {
              // esc
              this.doClose()
            }
          },
          handleMouseLeave: function handleMouseLeave() {
            var _this3 = this

            clearTimeout(this._timer)
            if (this.closeDelay) {
              this._timer = setTimeout(function() {
                _this3.showPopper = false
              }, this.closeDelay)
            } else {
              this.showPopper = false
            }
          },
          handleDocumentClick: function handleDocumentClick(e) {
            var reference = this.reference || this.$refs.reference
            var popper = this.popper || this.$refs.popper

            if (!reference && this.$refs.wrapper.children) {
              reference = this.referenceElm = this.$refs.wrapper.children[0]
            }
            if (!this.$el || !reference || this.$el.contains(e.target) || reference.contains(e.target) || !popper || popper.contains(e.target) || this.isFocus) return
            this.showPopper = false
          },
          handleAfterEnter: function handleAfterEnter() {
            this.setUpdateInit()
            this.$emit('after-enter')
          },
          handleAfterLeave: function handleAfterLeave() {
            this.$emit('after-leave')
            this.doDestroy()
          },
          cleanup: function cleanup() {
            if (this.openDelay || this.closeDelay) {
              clearTimeout(this._timer)
            }
            this.$off('el.popover.focus')
            this.$off('el.popover.blur')
          }
        },

        destroyed: function destroyed() {
          var reference = this.reference

          Object(dom_['off'])(reference, 'click', this.doToggle)
          Object(dom_['off'])(reference, 'mouseup', this.doClose)
          Object(dom_['off'])(reference, 'mousedown', this.doShow)
          Object(dom_['off'])(reference, 'focusin', this.doShow)
          Object(dom_['off'])(reference, 'focusout', this.doClose)
          Object(dom_['off'])(reference, 'mousedown', this.doShow)
          Object(dom_['off'])(reference, 'mouseup', this.doClose)
          Object(dom_['off'])(reference, 'mouseleave', this.handleMouseLeave)
          Object(dom_['off'])(reference, 'mouseenter', this.handleMouseEnter)
          Object(dom_['off'])(document, 'click', this.handleDocumentClick)
          Object(dom_['off'])(document, 'contextmenu', this.handleDocumentClick)
        }
      })
      // CONCATENATED MODULE: ./packages/popover/src/main.vue?vue&type=script&lang=js&
      /* harmony default export */ var src_mainvue_type_script_lang_js_ = (mainvue_type_script_lang_js_)
      // EXTERNAL MODULE: ./node_modules/_vue-loader@15.9.7@vue-loader/lib/runtime/componentNormalizer.js
      var componentNormalizer = __webpack_require__(1)

      // CONCATENATED MODULE: ./packages/popover/src/main.vue

      /* normalize component */

      var component = Object(componentNormalizer['a' /* default */])(
        src_mainvue_type_script_lang_js_,
        render,
        staticRenderFns,
        false,
        null,
        null,
        null

      )

      /* hot reload */
      // eslint-disable-next-line no-constant-condition,no-unused-vars
      if (false) { var api }
      component.options.__file = 'packages/popover/src/main.vue'
      /* harmony default export */ var main = (component.exports)
      // CONCATENATED MODULE: ./packages/popover/src/directive.js
      var getReference = function getReference(el, binding, vnode) {
        var _ref = binding.expression ? binding.value : binding.arg
        var popper = vnode.context.$refs[_ref]
        if (popper) {
          if (Array.isArray(popper)) {
            popper[0].$refs.reference = el
          } else {
            popper.$refs.reference = el
          }
        }
      }

      /* harmony default export */ var directive = ({
        bind: function bind(el, binding, vnode) {
          getReference(el, binding, vnode)
        },
        inserted: function inserted(el, binding, vnode) {
          getReference(el, binding, vnode)
        }
      })
      // EXTERNAL MODULE: external "vue"
      var external_vue_ = __webpack_require__(7)
      var external_vue_default = /* #__PURE__*/__webpack_require__.n(external_vue_)

      // CONCATENATED MODULE: ./packages/popover/index.js

      external_vue_default.a.directive('popover', directive)

      /* istanbul ignore next */
      main.install = function(Vue) {
        Vue.directive('popover', directive)
        Vue.component(main.name, main)
      }
      main.directive = directive

      // eslint-disable-next-line no-unused-vars
      /* harmony default export */ var popover = __webpack_exports__['default'] = (main)
      /***/ },

    /***/ 9:
    /***/ function(module, exports) {
      module.exports = require('element-ui/lib/utils/clickoutside')
      /***/ }

    /** ****/ })

<template>
  <div class="login-regiester">
    <div class="login-box">

      <div  style="font-size: 30px;line-height:3vh;position: absolute;top: -70px;color: #fff;text-align: center;width: 100%">
        <div >中药行业工业互联网标识应用平台</div>
      </div>
      <div class="login-left">
        <div class="title-container">
          <div style="font-size: 26px;line-height:3vh">立即注册</div>
        </div>
        <el-form ref="loginForm" size="medium" :model="loginForm">
          <el-form-item
            prop="account"
            :rules="[
              { required: true, trigger: 'blur', message: '请输入账号' }
            ]"
          >
            <el-input v-model="loginForm.account" placeholder="账号">
              <span slot="prefix" class="el-icon-user" />
            </el-input>
          </el-form-item>
          <el-form-item
            style="margin-top: 20px"
            prop="password"
            :rules="[
              { required: true, trigger: 'blur', message:'请输入密码' }
            ]"
          >
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="登录密码"
              name="password"
              auto-complete="on"
              @keyup.enter.native="handleLogin"
            >
              <span slot="prefix" :class="passwordType?'el-icon-lock':'el-icon-unlock'" @click="showPwd()" />
            </el-input>

          </el-form-item>
          <el-form-item
            style="margin-top: 20px"
            prop="passwordConfirm"
            :rules="[
              { required: true, trigger: 'blur', message:'请输入确认密码' }
            ]"
          >
            <el-input
              :key="passwordTypeConfirm"
              ref="passwordConfirm"
              v-model="loginForm.passwordConfirm"
              :type="passwordTypeConfirm"
              placeholder="确认密码"
              name="password"
              auto-complete="on"
              @keyup.enter.native="handleLogin"
            >
              <span slot="prefix" :class="passwordTypeConfirm?'el-icon-lock':'el-icon-unlock'" @click="showPwdConfirm()" />
            </el-input>

          </el-form-item>
          <el-form-item
            style="margin-top: 20px"
            prop="phone"
            :rules="[
              { required: true, trigger: 'blur', message: '请输入电话号码' },
              { pattern: /^1[3456789]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
            ]"
          >
            <el-input
              v-model="loginForm.phone"
              placeholder="电话号码"
            >
              <span slot="prefix" class="el-icon-mobile-phone" />
            </el-input>
          </el-form-item>
          <el-form-item
            style="margin-top: 20px"
            prop="verifyCode"
            :rules="[
              { required: true, trigger: 'blur', message: '请输入验证码' }
            ]"
          >
            <el-col :span="15">
              <el-input v-model="loginForm.verifyCode" placeholder="验证码">
                <span slot="prefix"> <img
                    class="svg-img"
                    src="../../assets/icon/code.png"
                    alt
                ></span>
              </el-input>

            </el-col>
            <el-col :span="1">
              <h1 />
            </el-col>
            <el-col :span="8">
              <div class="login-code">
                <img
                  :src="loginForm.image"
                  class="login-code-img"
                  @click="refreshCode"
                >
              </div>
            </el-col>
          </el-form-item>
        </el-form>
        <div class="btn">
          <div class="login-btn" style="background: rgb(137 147 158 / 32%)" @click="handleBack">
            <span>返回登录</span>
          </div>
          <div class="login-btn" @click="handleLogin">
            <span>注册</span>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getLang } from '@/utils/auth'
import { mapActions } from 'vuex'
import { getCaptcha, signUp } from '@/api/login'

export default {
  name: 'Login',

  data() {
    return {
      isQr: false,
      lang: getLang() || 'zh',
      loginForm: {
        account: '',
        password: ''
      },
      loading: false,
      passwordType: 'password',
      passwordTypeConfirm: 'password',
      redirect: undefined
    }
  },
  computed: {},
  watch: {},
  async mounted() {
    this.refreshCode()
  },
  methods: {
    ...mapActions([`getAllDictList`]),
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    showPwdConfirm() {
      if (this.passwordTypeConfirm === 'password') {
        this.passwordTypeConfirm = ''
      } else {
        this.passwordTypeConfirm = 'password'
      }
      this.$nextTick(() => {
        this.$refs.passwordConfirm.focus()
      })
    },
    refreshCode() {
      getCaptcha().then((res) => {
        const data = res.data
        this.$set(this.loginForm, 'verifyKey', data.key)
        this.$set(this.loginForm, 'image', data.captchaImg)
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          if (this.loginForm.passwordConfirm !== this.loginForm.password) {
            this.$message.error('两次密码不一致！请重新输入')
            return false
          }
          const password = this.$util.MD5(this.loginForm.password)

          signUp({ ...this.loginForm, password, image: undefined, passwordConfirm: undefined })
            .then((res) => {
              this.loading = false
              if (res.code === 'SUCCESS') {
                this.$message.success('注册成功！')
                this.$destroy()
                this.$router.push({ path: '/login' })
              }
            })
            .catch(() => {
              this.loading = false
              this.refreshCode()
            })

          //   })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleBack() {
      this.$destroy()
      this.$router.push({ path: '/login' })
    }
  }
}
</script>
<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

// @supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
//   .login-container .el-input input {

//   }
// }

/* reset element-ui css */
.login-regiester {

  .el-input {
    border: 0px;
    input {

      height: 40px;
      background: #ffffff;

      -webkit-appearance: none;
      border-radius: 0px;
      padding: 0px 5px 0px 10px;
      color: #313131;
    }
    :-webkit-autofill,
    :-webkit-autofill:hover,
    :-webkit-autofill:focus,
    :-webkit-autofill:active {
      // 回填文字颜色设置
      -webkit-text-fill-color: #313131 !important;
      transition: background-color 5000s ease-in-out 0s;
    }
  }
  .el-input__inner {
    border: none;
    border-bottom: 1px solid #DCDFE6;
    border-radius: 0;}
  .el-input__prefix{
    top: 2px;
    left: -2px;
    font-size: 20px;color: black;
  }
  .el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding: 0;
    top: auto;
    position: absolute;
    left: 10px !important;
    bottom: -20px !important;
  }
}
</style>
<style lang="scss" scoped>
@import "~element-ui/packages/theme-chalk/src/common/var.scss";
@import "../../styles/variables";

.el-carousel {
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
}

.login-code-img {
  width: 100%;
  height: 36px;
}

.login-regiester {
  min-height: 100%;
  width: 100%;
  overflow: hidden;
  background: #f5f5f5 url("../../assets/404_images/login-bg.png") no-repeat fixed center center / cover;
  .login-box {
    width: 452px;
    height: 40vh;
    min-height: 500px;
    position: absolute;
    right: 8%;
    border-radius: 25px;
    top: 50%;
    transform: translate(0%, -50%);
    box-shadow: -2px 2px 5px rgba(0, 0, 0, 0.13),
    2px 2px 5px rgba(0, 0, 0, 0.13);
  }

  .login-right {
    width: 60%;
    height: 100%;
    float: left;
    display: inline-block;
    border-top-left-radius: 25px;
    border-bottom-left-radius: 25px;
    overflow: hidden;

    .div-login-img {
      height: 100%;
      width: 100%;
      background-color: #FFFFFF;
      border-radius: inherit;
    }

    .login-right-img {
      width: 100%;
      height: 100%;
    }
  }

  .login-left {
    width: 100%;
    height: 100%;
    float: left;
    padding:0 50px;
    display: inline-block;
    background-color: #fff;
    position: relative;
    border-radius: 25px;
    .svg-img {
      width: 20px;
      height: 20px;
      vertical-align: middle;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .qr-login-switch {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 0;
      width: 80px;
      height: 80px;
      opacity: .6;
      transition: all .3s cubic-bezier(.645, .045, .355, 1);
      border-color: $primary-color $primary-color rgba(0, 0, 0, 0) rgba(0, 0, 0, 0);
      border-style: solid;
      border-width: 40px;
      background-size: cover;
      border-top-right-radius: 25px;
      box-sizing: border-box;

      &:hover {
        opacity: 1;
      }

      & .anticon {
        position: absolute;
        font-size: 48px;
        top: -24px;
        right: -24px;
        color: white;
      }
    }

    .title-container {
      width: 65%;
      height: 90px;
      text-align: center;
      color: #1770B5;
      padding-top: 10%;
      line-height: 5vh;
      font-size: 3vh;
      background-size: 30px;
      margin: 0px auto;
      margin-bottom: 20px;

    }

    .el-form-item {
      margin-bottom: 0;
    }

    //input:-internal-autofill-selected {
    //  background-color: #ffffff !important;
    //}

    .input-box {
      border: 1px solid #dcdcdc;
      background: #ffffff;
      border-radius: 5px;
      color: #313131;
      margin: 0 0 12px 17%;
      width: 65%;
    }
  }

  .tips {
    font-size: 12px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding-left: 10px;
    vertical-align: middle;
    width: 30px;
    display: inline-block;

    .svg-img {
      width: 15px;
      height: 19px;
      vertical-align: middle;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    cursor: pointer;
    user-select: none;
  }

  .btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    margin-top: 20px;
  }

  .login-btn {
    width: 126px;
    height: 40px;
    margin-top: 10px;
    line-height: 40px;
    font-size: 16px;
    color: #fff;
    cursor: pointer;
    text-align: center;
    background: linear-gradient(90deg, #62F8FF 0%, #0149A0 100%);
    border-radius: 5px;
  }

  .lang-box {
    margin: 20px 0 0 20%;
  }
}

.el-form-item + .el-form-item {
  margin-top: 10px;
}

.touchKey {
  display: inline-block;
  width: calc(100% - 30px);
  position: relative;
}

.touchKetchIcon {
  position: absolute;
  bottom: 3px;
  cursor: pointer;
  right: 6%;

  i {
    font-size: 25px;
    color: #8a8989;
  }
}

</style>

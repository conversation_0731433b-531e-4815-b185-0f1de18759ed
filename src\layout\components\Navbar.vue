<template>
  <div class="navbar">
    <hamburger
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <!-- <breadcrumb class="breadcrumb-container" /> -->
    <tags-view v-if="needTagsView" class="breadcrumb-container" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import TagsView from '../components/TagsView'

export default {
  components: {
    // eslint-disable-next-line vue/no-unused-components
    Breadcrumb,
    Hamburger,
    TagsView
  },
  data() {
    return {
      value: '',
      options: [],
      langcn: 'zh',
      langen: 'en'
    }
  },
  computed: {
    needTagsView() {
      return this.$store.state.settings.tagsView
    },
    ...mapGetters(['sidebar', 'name'])
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  min-height: 38px;
  overflow: hidden;
  position: relative;
  background: rgba(73, 135, 229, 0.16);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  border-bottom: 1px solid #EDEDED;

  .hamburger-container {
    padding-top: 5px !important;
    line-height: 32px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }
}

.el-input--small .el-input__inner {
  height: 25px;
  line-height: 25px;
  color: #000000;
}

.el-dropdown-menu__item {
  color: #000000;
  width: 120px;

  span {
    padding-left: 5px;
  }
}
</style>

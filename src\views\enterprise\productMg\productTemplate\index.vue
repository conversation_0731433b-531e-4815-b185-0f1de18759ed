<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="(data) => {searchHelper.search(data)}"
      @return-reset="searchHelper.reset"
    />
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="() => {searchHelper.handleQuery()}"
    />

  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import acc from '@/api/acc/acc'
import query from '@/components/mixin/query'
import accApi from '@/api/acc/acc'

export default {
  name: 'ProductTemplate',
  components: { DataSelect, DataTable },
  mixins: [query],
  data() {
    return {
      buttonData: [
        {
          label: '系统默认模版配置',
          action: () => {
            return this.onAddClick(1)
          },
          permission: 'all'
        },
        {
          label: '添加',
          action: this.onAddClick,
          permission: 'all'
        }
      ],
      searchHelper: new this.$searchHelper({ api: acc.productTemplatePageList }),
      dataList: [],
      search: {
        searchText: {
          label: '模板名称/产品名称',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入模板名称/产品名称'
          }
        }
      },
      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            sortable: false
          },
          {
            label: '模板名称',
            prop: 'name',
            sortable: false
          },
          {
            label: '绑定产品',
            prop: 'productName',
            sortable: false
          },
          {
            label: '创建人',
            prop: 'createUserName',
            sortable: false
          },
          {
            label: '创建时间',
            prop: 'createTime',
            sortable: false
          }

        ],
        operation: {
          label: '操作',
          width: '150px',
          data: [
            {
              label: '修改',
              action: this.onEditClick,
              permission: 'all'
            },
            {
              label: '复制',
              action: this.onCopyClick,
              permission: 'all'
            }, {
              label: '删除',
              action: this.onDeleteClick,
              permission: 'all'
            }]
        }
      }
    }
  },
  mounted() {
  },
  methods: {
    onAddClick(type) {
      this.$router.replace({ path: 'productTemplateDetail', query: { action: 'edit', addType: type, id: '1899708712015675393' }})
    },
    onCopyClick(row) {
      this.$router.replace({ path: 'productTemplateDetail', query: { action: 'copy', id: row.id }})
    },
    onDeleteClick(row) {
      this.$confirm('请确认是否删除?', '提示').then(() => {
        accApi.deleteProductTemplate(row).then(res => {
          this.$message.success('删除成功')
          this.searchHelper.handleQuery()
        })
      })
    },
    onEditClick(row) {
      this.$router.replace({ path: 'productTemplateDetail', query: { action: 'edit', id: row.id }})
    }
  }
}
</script>

<style scoped lang="scss">
</style>

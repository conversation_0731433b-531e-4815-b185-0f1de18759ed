<template>
  <div class="ui-fas">
    <!-- 弹出框 -->
    <el-popover
      ref="popover"
      placement="bottom"
      popper-class="el-icon-popper"
      :width="popoverWidth"
    >
      <template #reference>
        <el-input
          ref="input"
          :value="value"
          placeholder="请选择图标"
          clearable
          @focus="_popoverShowFun()"
        >
          <template #prepend>
            <i :class="value" />
          </template>
        </el-input>
      </template>
      <el-scrollbar
        ref="eScrollbar"
        tag="div"
        wrap-class="el-select-dropdown__wrap"
        view-class="el-select-dropdown__list"
        :class="'is-empty-'+id"
      >
        <ul
          ref="fasIconList"
          class="fas-icon-list"
        >
          <li
            v-for="(item, index) in iconList"
            :key="index"
            :style="value === item && highLightColor !== '' ? {color: highLightColor} : ''"
            @click="_selectedIcon(item)"
          >
            <i :class="item" />
          </li>
        </ul>
      </el-scrollbar>
    </el-popover>
  </div>
</template>

<script>
import ElIcon from './elementUI'

export default {
  name: 'IconPicker',
  props: {
    // 是否禁用文本框
    disabled: {
      type: Boolean,
      // false
      default() {
        return false
      }
    },
    readonly: {
      type: Boolean,
      // false
      default() {
        return false
      }
    },
    clearable: {
      type: Boolean,
      // true
      default() {
        return true
      }
    },
    // e-icon-picker 样式
    styles: {
      type: Object,
      default() {
        return {}
      }
    },
    value: {
      type: String,
      default() {
        return ''
      }
    },
    width: {
      type: Number,
      default() {
        return -1
      }
    },
    highLightColor: {
      type: String,
      default() {
        return '#409EFF'
      }
    }
  },
  data() {
    return {
      iconList: ElIcon,
      popoverWidth: 200,
      id: new Date().getTime()
    }
  },
  computed: {
  },
  watch: {
  },
  mounted() {
    this._updateW()
  },
  created() {
  },
  methods: {
    _selectedIcon(item) {
      this._emitFun(item)
      document.body.click()
    },
    // 更新宽度
    _updateW() {
      this.$nextTick(() => {
        if (this.width === -1 && this.$refs.input && this.$refs.input.$el) {
          this.popoverWidth = this.$refs.input.$el.getBoundingClientRect().width - 34
        } else {
          this.popoverWidth = this.width
        }
        if (this.$refs.eScrollbar && this.$refs.eScrollbar.wrap) {
          this.$refs.eScrollbar.wrap.scrollTop = 0
          this.$refs.eScrollbar.handleScroll()
          this.$refs.eScrollbar.update()
        }
      })
    },
    // 显示弹出框的时候容错，查看是否和el宽度一致
    _popoverShowFun() {
      const _this = this
      _this._updateW()
    },
    // 判断类型，抛出当前选中id
    _emitFun(val) {
      this.$emit('input', val)
      this.$emit('change', val)
    }
  }
}
</script>

<style lang="css" scoped>

.fas-icon-list {
  list-style-type: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.ui-fas .el-input__inner {
  cursor: pointer;
}

.fas-icon-list li {
  width: 30px;
  height: 30px;
  margin: 5px;
}

.fas-icon-list li i, .fas-icon-list li svg {
  font-size: 20px;
  cursor: pointer;
}

.el-icon-popper {
  max-height: 400px;
  overflow: auto;
  overflow-x: hidden;
  overflow-y: hidden;
}

.el-icon-popper[x-placement^="bottom"] {
  margin-top: 5px;
}

.fas-no-data {
  display: block;
}

.e-icon {
  font-size: 16px;
}
</style>

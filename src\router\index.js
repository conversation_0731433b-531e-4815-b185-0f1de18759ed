import Vue from 'vue'
import Router from 'vue-router'
import acc from './acc'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        name: 'redirect', hidden: true,
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/loginPage',
    component: () => import('@/views/login/index'),
    name: 'loginPage',
    hidden: true
  }, {
    path: '/register',
    component: () => import('@/views/register/index'),
    name: 'Register',
    hidden: true
  }, {
    path: '/mobilePhone',
    component: () => import('@/views/enterprise/productMg/productTemplate/mobilePhone'),
    name: 'MobilePhone',
    hidden: true
  }, {
    path: '/wx',
    component: () => import('@/views/enterprise/productMg/productTemplate/wx'),
    name: 'Wx',
    hidden: true
  },
  {
    path: '/test',
    component: () => import('@/views/enterprise/productMg/productTemplate/test.vue'),
    name: 'Test',
    hidden: true
  }, {
    path: '/antiCounterfeiting',
    component: () => import('@/views/enterprise/productMg/productTemplate/antiCounterfeiting.vue'),
    name: 'AntiCounterfeiting',
    hidden: true
  },
  {
    path: '/test1',
    component: () => import('@/views/enterprise/productMg/productTemplate/test1.vue'),
    name: 'Test1',
    hidden: true
  },
  // {
  //   path: '/portalPage',
  //   component: () => import('@/views/tenant/portal/index'),
  //   name: 'PortalPage',
  //   hidden: true
  // },
  {
    path: '/dashboard',
    component: Layout,
    hidden: true,
    children: [{
      path: 'dashboard',
      name: 'Dashboard',
      component: () => import('@/views/dashboard/index'),
      meta: {
        title: '首页',
        titleEn: 'Dashboard',
        icon: 'el-icon-s-home',
        affix: true
      }
    }
    ]
  },
  {
    path: '/',
    redirect: '/dashboard/dashboard',
    hidden: true
  },
  // 404 page must be placed at the end !!!
  {
    path: '*',
    component: () => import('@/views/404'),
    hidden: true
  }
]
const asyncRoute = []
export const asyncRoutes = asyncRoute.concat(acc)
function handleKeepAlive(to) {
  if (to.matched && to.matched.length > 2) {
    for (let i = 0; i < to.matched.length; i++) {
      const element = to.matched[i]
      if (element.components.default.name === 'Common') {
        to.matched.splice(i, 1)
        handleKeepAlive(to)
      }
    }
  }
}
const router = new Router({
  base: '/',
  mode: 'history', // 后端支持可开
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
router.beforeEach((to, from, next) => {
  handleKeepAlive(to)
  document.body.click() // 切换页面popver不关闭bug
  next()
})
// router.onError((error) => {
//   const pattern = /Loading chunk (\d)+ failed/g
//   const isChunkLoadFailed = error.message.match(pattern)
//   const targetPath = router.history.pending.fullPath
//   if (isChunkLoadFailed) {
//     router.replace(targetPath)
//   }
// })
export default router

import Router from '../router'
import { MD5 } from './md5'
import _ from 'lodash'
import i18n from '@/i18n'
import math from '@/utils/math'
const _dom = require('element-ui/lib/utils/dom')
import store from '@/store'
import VueEvent from '@/utils/vue-event'
import {
  Message,
  // eslint-disable-next-line no-unused-vars
  Loading,
  MessageBox
} from 'element-ui'
import storage from 'good-storage'

export default {
  isEmpty(val) {
    return val === null || val === undefined || ((val + '').trim() === '')
  },
  setStyle(element, styleName, value) {
    _dom.setStyle(element, styleName, value)
  },
  hasClass(el, cls) {
    return _dom.hasClass(el, cls)
  },
  addClass(el, cls) {
    _dom.addClass(el, cls)
  },
  removeClass(el, cls) {
    _dom.removeClass(el, cls)
  },
  cloneDeep(object) {
    return _.cloneDeep(object)
  },
  MD5(val) {
    return MD5(val)
  },
  dealGetData(data) {
    let param = ''
    if (data) {
      param = '?'
      for (const key in data) {
        // eslint-disable-next-line eqeqeq
        if (data[key] || data[key] == 0) {
          param += key + '=' + data[key] + '&'
        }
      }
      param = param.substring(0, param.length - 1)
    }
    return param
  },
  tip(flag, msg, path) { // 提示消息：成功1，提醒0，报错-1;path路由跳转
    if (flag > -1) {
      Message({
        message: msg,
        center: true,
        // eslint-disable-next-line eqeqeq
        type: flag == 1 ? 'success' : 'warning',
        duration: '2000',
        onClose() {
          path ? Router.push({
            path: path
          }) : null
        }
      })
    } else {
      Message({
        message: msg,
        center: true,
        type: 'error',
        duration: '2000'
      })
    }
    // Message.error(msg);
  },
  validRule(rule, value) {
    let reg = null
    let txt = i18n.t('common.format')
    switch (rule) {
      case 'tel': // 匹配手机号
        reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/g
        txt = i18n.t('common.valiTel')
        break
      case 'fixTelAndTel': // 匹配电话号
        reg = /^(1[3|4|5|6|7|8|9])\d{9}$|^\d{3}-\d{7,8}|\d{4}-\d{7,8}$/
        txt = i18n.t('common.fixTelAndTel')
        break
      case 'fixTel': // 国内电话
        reg = /^(((0\d{3}[\-])?\d{7}|(0\d{3}[\-])?\d{8}|(0\d{2}[\-])?\d{7}|(0\d{2}[\-])?\d{8}))([\-]\d{2,4})?$/
        break
      case 'identity': // 匹配身份证
        reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
        break
      case 'num': // 匹配非负整数
        reg = /^\d*$/
        break
      case 'int':
        reg = /^\d+$/
        txt = i18n.t('common.seqIdValidator')
        break
      case 'seq':
        reg = /^[0-9]\d{0,5}$/
        txt = i18n.t('common.valiSeq')
        break
      case 'integer': // 匹配整数
        reg = /^-?\d+$/g
        break
      case 'floatNum': // 匹配浮点数
        reg = /^(-?\d+)(\.\d{1,2})?$/
        break
      case 'floatPositiveNum': // 匹配非负浮点数
        reg = /^(\d+)(\.\d{1,2})?$/
        break
      case 'name': // 姓名
        reg = /^[\u4E00-\u9FA5]{2,10}$/
        txt = i18n.t('common.valiName')
        break
      case 'username': // 会员昵称
        reg = /^[\u4E00-\u9FA5A-Za-z0-9_]{2,10}$/
        txt = i18n.t('common.valiUsername')
        break
      case 'nickname': // 名称，如活动名称
        reg = /^([\u4E00-\u9FA5A-Za-z0-9]|_(?!_)){2,20}$/ // 2~20位名称
        txt = i18n.t('common.valiNickname')
        break
      case 'commonname':
        reg = /^[\u4E00-\u9FA5A-Za-z0-9]{1,6}$/ // 级别称呼有用到
        txt = i18n.t('common.valiCommonname')
        break
      case 'scale':
        reg = /^\d\.([1-9]{1,2}|[0-9][1-9])$|^[1-9]\d{0,1}(\.\d{1,2}){0,1}$|^100(\.0{1,2}){0,1}$/ // 大于0，小于100，并且保留两位数
        // reg1= /^\d\.([1-9]{1,2}|[0-9][1-9])$|^[1-9]\d{0,1}(\.\d{1,2}){0,1}$|^100(\.0{1,2}){0,1}$/;
        txt = i18n.t('common.valiScale')
        break
      case 'email':
        // reg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
        reg = /^\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}$/
        txt = i18n.t('common.incorrectMailbox')
        break
      case 'numberVerify':
        reg = /^[0-9]*$/
        txt = i18n.t('common.seqIdValidator')
        break
      case 'numberLetters':
        reg = /^\w+$/
        txt = i18n.t('common.numberLetters')
        break
      case 'code':
        reg = /^[A-Za-z0-9_@.]+$/
        txt = i18n.t('common.codeValidate')
        break
      default:
        break
    }
    return {
      valid: reg.test(value),
      tip: txt
    }
  },
  // 必填项已有required为空提示，只需验证格式
  // 使用格式如：this.$util.validRequiredIpt(value, callback,"tel")
  validRequiredIpt(value, callback, rule) {
    let valid
    // eslint-disable-next-line eqeqeq
    if (!value && parseInt(value) != 0) { // 为空不管
      return
    } else {
      const validObj = this.validRule(rule, value)
      valid = validObj.valid
      if (!valid) {
        // this.log({
        //     rule: rule,
        //     valid: valid,
        //     tip: validObj.tip
        // })
        callback(new Error(validObj.tip))
      } else {
        callback()
      }
    }
  },
  // 传isNeed时必填，不传可以不填。此时字段没有required*标识
  // 使用格式如：this.$util.validIpt(value, callback,"tel",true)
  validIpt(value, callback, rule, isNeed) {
    let valid
    // eslint-disable-next-line eqeqeq
    if (!value && parseInt(value) != 0) { // 为空
      if (isNeed) {
        callback(new Error(i18n.t('common.notNull')))
      } else {
        callback()
      }
    } else {
      const validObj = this.validRule(rule, value)
      valid = validObj.valid
      if (!valid) {
        // this.log({
        //     rule: rule,
        //     valid: valid,
        //     tip: validObj.tip
        // })
        callback(new Error(validObj.tip))
      } else {
        callback()
      }
    }
  },
  log(msg) {
    console.log('%c%s', 'color: red; font-size: 16px;', JSON.stringify(msg))
  },
  // 节流函数
  varthrottle(func, delay) {
    var startTime = Date.now()
    return function() {
      var currTime = Date.now()
      var self = this
      var args = this.arguments
      if (currTime - startTime >= delay) {
        func.apply(self, args)
        startTime = Date.now()
      }
    }
  },
  // 获取某月的天数
  getMonthDays(myMonth) {
    const now = new Date()
    var monthStartDate = new Date(now.getFullYear(), now.getMonth(), 1)
    var monthEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 1)
    var days = (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24)
    return days
  },
  formatNumber(n) {
    n = n.toString()
    return n[1] ? n : '0' + n
  },
  // 将日期转换成周
  getYearWeek(date) {
    var now = this.formatTime(date, 'Y-M-D')
    var date1 = new Date(now)
    var date2 = new Date(date1.getFullYear(), 0, 1)
    var day1 = date.getDay()
    // eslint-disable-next-line eqeqeq
    if (day1 == 0) day1 = 7
    var day2 = date2.getDay()
    // eslint-disable-next-line eqeqeq
    if (day2 == 0) day2 = 7
    var d = Math.round((date1.getTime() - date2.getTime() + (day2 - day1) * (24 * 60 * 60 * 1000)) / 86400000)
    return date1.getFullYear() + '-' + (Math.ceil(d / 7) + 1)
  },
  // 格式化日期
  formatTime(date, format = 'Y-M-D') {
    var formateArr = ['Y', 'M', 'D', 'h', 'm', 's']
    var returnArr = []
    returnArr.push(date.getFullYear())
    returnArr.push(this.formatNumber(date.getMonth() + 1))
    returnArr.push(this.formatNumber(date.getDate()))
    returnArr.push(this.formatNumber(date.getHours()))
    returnArr.push(this.formatNumber(date.getMinutes()))
    returnArr.push(this.formatNumber(date.getSeconds()))

    for (var i in returnArr) {
      format = format.replace(formateArr[i], returnArr[i])
    }
    return format
  },
  // 日期去掉0
  dislodgeZero(str) {
    let strArray = str.split('-')
    strArray = strArray.map(function(val) {
      if (val[0] === '0') {
        return val.slice(1)
      } else {
        return val
      }
    })
    return strArray.join('-')
  },
  // 获得本周第一天
  weekFirst(data) {
    const time = data.getTime()
    let week = data.getDay() // 星期几
    if (week === 0) {
      week = 7
    }
    const firstDay = time - (week - 1) * 24 * 60 * 60 * 1000
    return new Date(firstDay)
  },
  // 获得本周最后一天
  weekLast(data) {
    const time = data.getTime()
    let week = data.getDay() // 星期几
    if (week === 0) {
      week = 7
    }
    const lastDay = time + (7 - week) * 24 * 60 * 60 * 1000
    return new Date(lastDay)
  },
  formatspdTime(time) {
    const result = {
      dayValue: '1',
      timeValue: ''
    }
    if (time && time !== '') {
      let t = time
      if (time.includes(':')) {
        t = time.replace(':', '')
      }
      if (t.length === 4) {
        let hour = parseInt(t.substring(0, 2))
        const minute = t.substring(2)
        let des = '1'
        if (hour >= 24) {
          des = '2'
          hour -= 24
        }
        result.dayValue = des
        result.timeValue = `${hour.toString().padStart(2, '0')}:${minute}`
      }
    }
    return result
  },
  formatspdTimeStr(time) {
    let result = ''
    const { dayValue, timeValue } = this.formatspdTime(time)
    if (timeValue !== '') {
      const des = dayValue === '1' ? i18n.t('common.sameDay') : i18n.t('common.nextDay')
      result = `${des} ${timeValue}`
    }
    return result
  },
  // 获取指定日期的前后n天
  getLastDate(date, days) {
    const newDate = new Date(date.getTime() + 24 * 3600 * 1000 * days)
    return newDate
  },
  // 自定义选择控件需要的数据（时间）
  getQuickDate(type) {
    const _this = this
    const now = new Date()
    var start = null
    var end = null
    switch (type) {
      case '1':// 今天
        start = new Date(new Date(now.toLocaleDateString()).getTime())
        end = new Date(new Date(now.toLocaleDateString()).getTime() + 24 * 3600 * 1000 - 1)
        return [start, end]
        // eslint-disable-next-line no-unreachable
        break
      case '2': // 昨天
        start = new Date(new Date(now.toLocaleDateString()).getTime() - 3600 * 1000 * 24)
        end = new Date(new Date(now.toLocaleDateString()).getTime() - 1)
        return [start, end]
        // eslint-disable-next-line no-unreachable
        break
      case '3': // 本周
        start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay())
        end = new Date(new Date(new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - now.getDay() - 1)).toLocaleDateString()).getTime() + 24 * 3600 * 1000 - 1)
        return [start, end]
        // eslint-disable-next-line no-unreachable
        break
      case '4': // 上周
        start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay() - 7)
        end = new Date(new Date(new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - now.getDay() - 1) - 7).toLocaleDateString()).getTime() + 24 * 3600 * 1000 - 1)
        return [start, end]
        // eslint-disable-next-line no-unreachable
        break
      case '5': // 本月
        start = new Date(now.getFullYear(), now.getMonth(), 1)
        end = new Date(new Date(new Date(now.getFullYear(), now.getMonth(), _this.getMonthDays(now.getMonth())).toLocaleDateString()).getTime() + 24 * 3600 * 1000 - 1)
        return [start, end]
        // eslint-disable-next-line no-unreachable
        break
      case '6': // 上月
        now.setDate(1)
        now.setMonth(now.getMonth() - 1)
        start = new Date(now.getFullYear(), now.getMonth(), 1)
        end = new Date(new Date(new Date(now.getFullYear(), now.getMonth(), _this.getMonthDays(now.getMonth())).toLocaleDateString()).getTime() + 24 * 3600 * 1000 - 1)
        return [start, end]
        // eslint-disable-next-line no-unreachable
        break
    }
  },
  // 日期时间选择器快捷选项
  setPickerOptions() {
    const _this = this
    return {
      shortcuts: [
        {
          text: i18n.t('common.today'),
          onClick(picker) {
            picker.$emit('pick', _this.getQuickDate('1'))
          }
        },
        {
          text: i18n.t('common.yesterday'),
          onClick(picker) {
            picker.$emit('pick', _this.getQuickDate('2'))
          }
        },
        {
          text: i18n.t('common.thisWeek'),
          onClick(picker) {
            picker.$emit('pick', _this.getQuickDate('3'))
          }
        },
        {
          text: i18n.t('common.lastWeek2'),
          onClick(picker) {
            picker.$emit('pick', _this.getQuickDate('4'))
          }
        },
        {
          text: i18n.t('common.thisMonth'),
          onClick(picker) {
            picker.$emit('pick', _this.getQuickDate('5'))
          }
        },
        {
          text: i18n.t('common.lastMonth'),
          onClick(picker) {
            picker.$emit('pick', _this.getQuickDate('5'))
          }
        },
        {
          text: i18n.t('common.lastWeek'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: i18n.t('common.lastOneMonth'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: i18n.t('common.lastThreeMonths'),
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }
      ]
    }
  },
  // 日期选择器快捷选项
  setPickerDateOptions() {
    return {
      shortcuts: [{
        text: i18n.t('common.lastWeek'),
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
          picker.$emit('pick', [start, end])
        }
      }, {
        text: i18n.t('common.lastOneMonth'),
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
          picker.$emit('pick', [start, end])
        }
      }, {
        text: i18n.t('common.lastThreeMonths'),
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
          picker.$emit('pick', [start, end])
        }
      }]
    }
  },
  // 月份选择器快捷选项
  setPickerMonthOptions() {
    return {
      shortcuts: [{
        text: i18n.t('common.thisMonth'),
        onClick(picker) {
          picker.$emit('pick', [new Date(), new Date()])
        }
      }, {
        text: i18n.t('common.yearToDate'),
        onClick(picker) {
          const end = new Date()
          const start = new Date(new Date().getFullYear(), 0)
          picker.$emit('pick', [start, end])
        }
      }, {
        text: i18n.t('common.lastSixMonths'),
        onClick(picker) {
          const end = new Date()
          const start = new Date()
          start.setMonth(start.getMonth() - 6)
          picker.$emit('pick', [start, end])
        }
      }]
    }
  },
  getSessionCode(key) {
    return store.state.user[key]
  },
  getProjectCode() {
    return this.getProjectInfo().value
  },
  getProjectInfo() {
    return this.getSessionCode('projectInfo')
  },
  getCurrentMenuPath() {
    return storage.session.get('currentMenuPath')
  },
  getUserCode() {
    return this.getSessionCode('userId')
  },
  toFixed(n) {
    if (n > 20 || n < 0) {
      throw new RangeError('toFixed() digits argument must be between 0 and 20')
    }
    const number = this
    if (isNaN(number) || number >= Math.pow(10, 21)) {
      return number.toString()
    }
    // eslint-disable-next-line eqeqeq
    if (typeof (n) === 'undefined' || n == 0) {
      return (Math.round(number)).toString()
    }
    let result = number.toString()
    const arr = result.split('.')
    // 整数的情况
    if (arr.length < 2) {
      result += '.'
      for (let i = 0; i < n; i += 1) {
        result += '0'
      }
      return result
    }
    const integer = arr[0]
    const decimal = arr[1]
    // eslint-disable-next-line eqeqeq
    if (decimal.length == n) {
      return result
    }
    if (decimal.length < n) {
      for (let i = 0; i < n - decimal.length; i += 1) {
        result += '0'
      }
      return result
    }
    result = integer + '.' + decimal.substr(0, n)
    const last = decimal.substr(n, 1)
    // 四舍五入，转换为整数再处理，避免浮点数精度的损失
    if (parseInt(last, 10) >= 5) {
      const x = Math.pow(10, n)
      result = (Math.round((parseFloat(result) * x)) + 1) / x
      result = result.toFixed(n)
    }
    return result
  },
  getSelectOptionbyList(list, valueKey, nameKey) {
    return list.map(value => { return { label: value[(nameKey || this.selectCodeName)], value: value[(valueKey || this.selectCodeKey)] } })
  },
  formatDate(date, format) {
    if (format === undefined) {
      format = 'yyyy-MM-dd hh:mm:ss'
    }
    const map = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'h+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds(),
      'q+': Math.floor((date.getMonth() + 3) / 3),
      'S+': date.getMilliseconds()
    }
    const yearRe = /(y+)/i
    if (yearRe.test(format)) {
      format = format.replace(yearRe, function($1) {
        return (date.getFullYear() + '').substring(4 - $1.length)
      })
    }
    for (const k in map) {
      const kRe = new RegExp('(' + k + ')')
      if (kRe.test(format)) {
        format = format.replace(kRe, function($1) {
          return $1.length === 1 ? map[k] : ('00' + map[k]).substring(('' + map[k]).length)
        })
      }
    }
    return format
  },
  bigNumberAdd(a, b) {
    return String(math.add(math.bignumber(a), math.bignumber(b)))
  },
  bigNumberMultiply(a, b) {
    return String(math.multiply(math.bignumber(a), math.bignumber(b)))
  },
  resolveResult(result, successMsg, errorMsg) {
    if (result.code === 'SUCCESS') {
      Message.success(successMsg || result.msg)
      return true
    } else {
      Message.error(errorMsg || result.msg)
      return false
    }
  },
  resolveParamCheck(result, alert) {
    if (result.code === 'SUCCESS') {
      return true
    } else {
      alert ? MessageBox.alert(result.msg, '提示') : Message.error(result.msg)
      return false
    }
  },
  mapOptions(list, codeKey = 'code', codeNameKey = 'codeName', toCodeKey = 'value', toCodeNameKey = 'label') {
    if (list && list.length > 0) {
      return list.map(item => {
        return {
          [toCodeKey]: item[codeKey],
          [toCodeNameKey]: item[codeNameKey]
        }
      })
    } else {
      return []
    }
  }, viewImage(imageUrlList) {
    VueEvent.$emit('app.openImage', imageUrlList)
  },
  handleTitles(data, titles) {
    Object.keys(titles).forEach(key => {
      if (!this.isEmpty(data[key])) {
        const item = titles[key]
        const { format } = item
        item.value = format ? format(data[key]) : data[key]
      }
    })
  },
  constantFilter(constant, field, value) {
    return constant[value] && constant[value][field] ? constant[value][field] : ''
  },

  getBrowserType() {
  // 获取 userAgent
    const userAgent = navigator.userAgent
    let browser = null

    // 判断浏览器类型
    if (/MicroMessenger/i.test(navigator.userAgent)) {
      browser = 'WeChat'
    } else if (userAgent.indexOf('Edg') > -1) {
      browser = 'Edge'
    } else if (userAgent.indexOf('Firefox') > -1) {
      browser = 'Firefox'
    } else if (userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') === -1) {
      browser = 'Safari'
    } else if (userAgent.indexOf('Chrome') > -1 && userAgent.indexOf('Safari') > -1) {
      browser = 'Chrome'
    } else if (userAgent.indexOf('AppleWebKit') > -1) {
      browser = 'AppleWebKit'
    }

    if (browser) {
      if (userAgent.indexOf('Mobile') > -1) {
        browser += ' Mobile'
      }
    } else {
      browser = '其他'
    }
    return browser
  },
  isEqual(obj1, obj2) {
    if (obj1 === obj2) return true;

    if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {
      return obj1 === obj2;
    }

    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) return false;

    for (let key of keys1) {
      if (!keys2.includes(key)) return false;
      const val1 = obj1[key];
      const val2 = obj2[key];
      // 区分 null 和 undefined
      if (val1 === null && val2 === null) continue;
      if (val1 === undefined && val2 === undefined) continue;
      if (val1 === null || val2 === null || val1 === undefined || val2 === undefined) {
        return false;
      }
      // 对于基本类型直接比较
      if (typeof val1 !== 'object' && typeof val2 !== 'object') {
        if (val1 !== val2) return false;
      } else if (Array.isArray(val1) && Array.isArray(val2)) {
        if (val1.length !== val2.length) return false;
        for (let i = 0; i < val1.length; i++) {
          if (!this.isEqual(val1[i], val2[i])) return false;
        }
      } else if (!this.isEqual(val1, val2)) {
        return false;
      }
    }
    return true;
  }
}
// toFixed兼容方法

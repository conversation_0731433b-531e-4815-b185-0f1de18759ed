import Cookies from 'js-cookie'
import storage from 'good-storage'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  size: Cookies.get('size') || 'medium',
  helpMessage: '',
  drawerComponentName: '',
  drawerClass: '',
  drawerVisible: false,
  drawerInfo: null,
  currentMenuPath: storage.session.get('currentMenuPath'),
  helpClass: ''
}

const mutations = {
  SET_DRAWER_COMPONENT_NAME: (state, drawerComponentName) => {
    state.drawerComponentName = drawerComponentName
  },
  SET_DRAWER_CLASS: (state, drawerClass) => {
    state.drawerClass = drawerClass
  },
  SET_DRAWER_INFO: (state, drawerInfo) => {
    state.drawerInfo = drawerInfo
  },
  SET_DRAWER_VISIBLE: (state, drawerVisible) => {
    state.drawerVisible = drawerVisible
  },
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_SIZE: (state, size) => {
    state.size = size
    Cookies.set('size', size)
  },
  SET_HELP_MESSAGE: (state, helpMessage) => {
    state.helpMessage = helpMessage
  },

  SET_HELP_CLASS: (state, helpClass) => {
    state.helpClass = helpClass
  },
  SET_CURRENT_MENU_PATH: (state, currentMenuPath) => {
    state.currentMenuPath = currentMenuPath
    storage.session.set('currentMenuPath', currentMenuPath)
  }
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setSize({ commit }, size) {
    commit('SET_SIZE', size)
  },
  setHelpMessage({ commit }, helpMessage) {
    commit('SET_HELP_MESSAGE', helpMessage)
  },
  setHelpClassMessage({ commit }, param) {
    commit('SET_HELP_MESSAGE', param.helpMessage)
    commit('SET_HELP_CLASS', param.helpClass)
  },
  setCurrentMenuPath({ commit }, currentMenuPath) {
    commit('SET_CURRENT_MENU_PATH', currentMenuPath)
  },
  setDrawerComponentName({ commit }, drawerComponentName) {
    commit('SET_DRAWER_COMPONENT_NAME', drawerComponentName)
  },
  setDrawerClass({ commit }, drawerClass) {
    commit('SET_DRAWER_CLASS', drawerClass)
  },
  setDrawerVisible({ commit }, drawerVisible) {
    commit('SET_DRAWER_VISIBLE', drawerVisible)
  },
  setDrawerInfo({ commit }, drawerInfo) {
    commit('SET_DRAWER_INFO', drawerInfo)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

import { asyncRoutes, constantRoutes } from '@/router'
import Layout from '@/layout'
import commonLayout from '@/views/common'

/**
 *
 * @param  {Array} userRouter 后台返回的用户权限json
 * @param  {Array} allRouter  前端配置好的所有动态路由的集合
 * @return {Array} realRoutes 过滤后的路由
 */

export function filterAsyncRoutes(allRouter = [], userRouter = [], parent = {}) {
  const allRealRoutes = { realRoutes: [], allRoutes: [] }
  // var realRoutes = []
  userRouter.map(v => {
    const isRoot = v.level === 0 || v.level === '0'
    // eslint-disable-next-line eqeqeq
    v.path = isRoot ? '/' + v.menuCode : v.menuCode
    v.fullMenuPath = `${(parent.fullMenuPath || '')}/${v.menuCode}`
    v.name = v.url
    // eslint-disable-next-line eqeqeq
    v.component = isRoot ? Layout : commonLayout
    let basePath = v.basePath
    if (isRoot) {
      basePath = v.path
    }
    if (v.isShow === '0') {
      v.hidden = true
    }
    v.children.forEach(item => { item.basePath = basePath })
    v.meta = {
      title: v.title, titleEn: v.titleEn, icon: v.menuIcon, basePath: basePath
    }
    const filiterRouter = []
    allRouter.map(item => {
      if (v.url && item.name) {
        if (v.url === item.name) {
          v.name = v.url
          v.hidden = item.hidden
          v.path = item.path
          v.preViewPath = item.preViewPath
          v.preViewName = item.preViewName
          v.meta = {
            title: v.title, titleEn: v.titleEn, icon: v.menuIcon, noCache: false, basePath: basePath, preViewPath: item.preViewPath, preViewName: item.preViewName
          }
          v.component = item.component
          filiterRouter.push(v)
        } else if (v.url === item.preViewName) {
          // 添加前端配置的详情页路由
          const route = { meta: {
            noCache: false, basePath: basePath, preViewPath: item.preViewPath, preViewName: item.preViewName
          }, ...item, basePath, fullMenuPath: `${(parent.fullMenuPath || '')}/${item.path}` }
          userRouter.push(route)
          filiterRouter.push(route)
        }
      }
    })
    allRealRoutes.allRoutes = allRealRoutes.allRoutes.concat(filiterRouter)
    const allRealRoutesItem = filterAsyncRoutes(asyncRoutes, v.children, v)
    allRealRoutes.allRoutes = allRealRoutes.allRoutes.concat(allRealRoutesItem.allRoutes)
    allRealRoutes.realRoutes.push(v)
  })
  return allRealRoutes
}

const permission = {
  state: {
    routes: [],
    routesList: [],
    addRoutes: [],
    selectLevel1Menu: ''
  },
  mutations: {
    SET_ROUTES_LIST: (state, routes) => {
      state.routesList = constantRoutes.concat(routes)
    },
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes
      state.routes = constantRoutes.concat(routes)
    },
    SET_SELECTLEVEL1MENU: (state, selectLevel1Menu) => {
      state.selectLevel1Menu = selectLevel1Menu
    }
  },
  actions: {
    GenerateRoutes({ commit }, data) {
      return new Promise(resolve => {
        // console.log('Do GenerateRoutes')
        // // 使用权限
        // var isDash = false
        // for (var j = 0; j < data.length; j++) {
        //   if (data[j].url === 'Dashboard') {
        //     isDash = true
        //   }
        // }
        // var accessedRoutes = []
        // if (isDash) {
        //   accessedRoutes = dashboardRoutes.concat(filterAsyncRoutes(asyncRoutes, data))
        // } else {
        //   accessedRoutes = filterAsyncRoutes(asyncRoutes, data)
        // }
        var accessedRoutes = filterAsyncRoutes(asyncRoutes, data)
        // console.log('Do GenerateRoutes filter data = {} ', data)
        // console.log('Do GenerateRoutes filter asyncRoutes = {} ', asyncRoutes)
        // console.log('Do GenerateRoutes filter accessedRoutes = {} ', accessedRoutes)
        const accessedRoutesEnd = accessedRoutes.realRoutes
        commit('SET_ROUTES', accessedRoutesEnd)
        commit('SET_ROUTES_LIST', accessedRoutes.allRoutes)
        // console.log(accessedRoutes)
        resolve(accessedRoutes)

        // 不使用权限
        // commit('SET_ROUTES', asyncRoutes)
        // resolve(asyncRoutes)
      })
    },
    SelectLevel1Menu({ commit }, data) {
      commit('SET_SELECTLEVEL1MENU', data)
    }
  }
}

export default permission

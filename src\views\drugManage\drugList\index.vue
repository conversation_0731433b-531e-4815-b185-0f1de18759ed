<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="(data) => {searchHelper.search(data)}"
      @return-reset="searchHelper.reset"
    >
      <template #queryButton>
        <upload-file style="width: 130px" :on-change.sync="onChange" btn-text="批量导入产品信息" />
      </template>

    </data-select>
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="() => {searchHelper.handleQuery()}"
    >
      <template v-slot:status="{ row }">
        <el-tag v-if="row.status==0" type="primary">{{ statusMap[row.status] }}</el-tag>
        <el-tag v-if="row.status==1" type="success">{{ statusMap[row.status] }}</el-tag>
        <el-tag v-if="row.status==2" type="danger">{{ statusMap[row.status] }}</el-tag>

      </template>
    </data-table>
    <simple-data-dialog
      v-if="beianDialogVisible"
      title="产品规格/等级"
      :visible="true"
      size="small"
    >
      <el-divider content-position="left">产品基本信息</el-divider>
      <div><strong>中药材编码：</strong>{{ baseFrom.medicinalMaterialsCode }}</div>
      <div style="margin-top: 10px"><strong>药品产品名称：</strong>{{ baseFrom.medicinalProductName }}</div>
      <el-divider content-position="left">产品规格信息</el-divider>
      <virtual-table
        ref="attr-table"
        :enable-search="false"
        auto-height
        style="padding: 0 !important;"

        :columns="specColumns"
        :table-data="specTableData"
        :edit-rules="specRules"
      >
        <template v-slot:attrNameCn="{row, scope}">
          <el-input
            v-model="row.attrNameCn"
            placeholder="请输入规格名称"
            @change="$refs.table.updateStatus(scope)"
          />
        </template>
        <template v-slot:operate="{$index}">
          <span class="text_button" @click="deleteSpec($index,1)">删除</span>
        </template>
      </virtual-table>
      <demo-block
        message="添加规格"
        :icon-class="'el-icon-plus icon-class'"
        @click.native="addSpec(1)"
      />
      <el-divider content-position="left">产品等级信息</el-divider>
      <virtual-table
        ref="attr-type"
        :enable-search="false"
        auto-height
        style="padding: 0 !important;"

        :columns="leavelColumns"
        :table-data="leavelTableData"
        :edit-rules="leavelRules"
      >
        <template v-slot:attrNameCn="{row, scope}">
          <el-input
            v-model="row.attrNameCn"
            placeholder="请输入等级名称"
            @change="$refs.table.updateStatus(scope)"
          />
        </template>
        <template v-slot:operate="{$index}">
          <span class="text_button" @click="deleteSpec($index,2)">删除</span>
        </template>
      </virtual-table>
      <demo-block
        message="添加等级"
        :icon-class="'el-icon-plus icon-class'"
        @click.native="addSpec(2)"
      />
      <el-footer class="button-container">
        <el-button @click="beianDialogVisible=false">取消</el-button>
        <el-button type="primary" @click="submit(false)">保存</el-button>
      </el-footer>
    </simple-data-dialog>
    <simple-data-dialog
      v-if="aoTuMationDialogVisible"
      title="自动化设置"
      :visible="true"
      size="small"
    >
      <el-form
        ref="aoTuMaTion"
        label-position="top"
        :model="aoTuMationFrom"
        :rules="aoTuMationFormRules"
        :inline="true"
      >
        <el-form-item
          prop="identificationRuleId"
          label="展示模版："
          style="width: 90%"
        >
          <el-select v-model="aoTuMationFrom.templateId" placeholder="请输入展示模版">
            <el-option v-for="item in templateList" :key="item.value" v-bind="item" />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="identificationRuleId"
          label="生码规则："
          style="width: 90%"
        >
          <el-select v-model="aoTuMationFrom.ruleId" placeholder="请输入自动标识规则">
            <el-option v-for="item in ruleList" :key="item.value" v-bind="item" />
          </el-select>
        </el-form-item>
      </el-form>
      <el-footer class="button-container">
        <el-button @click="aoTuMationDialogVisible=false">取消</el-button>
        <el-button type="primary" @click="submitAoToMation">保存</el-button>
      </el-footer>

    </simple-data-dialog>
  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import uploadFile from '@/components/DataDialog/uploadFileList.vue'
import acc from '@/api/acc/acc'
import accApi from '@/api/acc/acc'
import query from '@/components/mixin/query'
import SimpleDataDialog from '@/components/SimpleDataDialog/index.vue'
import VirtualTable from '@/components/VirtualTable/VirtualTable.vue'
import DemoBlock from '@/components/DemoBlock/index.vue'
export default {
  name: 'Product',
  components: { DemoBlock, VirtualTable, SimpleDataDialog, DataSelect, DataTable, uploadFile },
  mixins: [query],
  data() {
    return {
      statusMap: ['同步中', '已同步', '同步异常'],
      beianDialogVisible: false,
      baseFrom: {},
      aoTuMationDialogVisible: false,
      aoTuMationFrom: {},
      templateList: [],
      ruleList: [],
      aoTuMationFormRules: {
        identificationRuleId: [
          { required: true, message: '请输入自动标识规则' }
        ]
      },
      specColumns: [
        {
          title: '规格名称',
          field: 'attrNameCn',
          slotName: 'attrNameCn'
        },
        {
          title: '操作',
          field: 'operate',
          slotName: 'operate',
          width: '50px'
        }
      ],
      specTableData: [],
      specRules: {
        attrNameCn: [
          { required: true, message: '请输入规格名称' }
        ]
      },

      leavelColumns: [
        {
          title: '等级名称',
          field: 'attrNameCn',
          slotName: 'attrNameCn'
        },
        {
          title: '操作',
          field: 'operate',
          slotName: 'operate',
          width: '50px'
        }
      ],
      leavelTableData: [],
      leavelRules: {
        attrNameCn: [
          { required: true, message: '请输入等级名称' }
        ]
      },
      buttonData: [
        {
          label: '添加',
          action: this.onAddClick,
          permission: 'all'
        },
        {
          label: '产品导入模版下载',
          action: this.downFiles,
          permission: 'all'
        }
      ],
      searchHelper: new this.$searchHelper({ api: acc.drugList }),
      dataList: [],
      search: {
        searchText: {
          label: '中药材编码/药材产品名称/药材别名',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入中药材编码/药材产品名称/药材别名'
          }
        },
        status: {
          label: '状态',
          value: null,
          type: 'select',
          option: {
            clearable: false,
            selectOptions: [
              { value: '0', label: '同步中' },
              { value: '1', label: '已同步' },
              { value: '2', label: '同步异常' }
            ],
            placeholder: '请选择同步状态'
          }
        }
      },
      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            sortable: false
          },
          {
            label: '中药材编码',
            prop: 'medicinalMaterialsCode',
            sortable: false
          },
          {
            label: '药材产品名称',
            prop: 'medicinalProductName',
            sortable: false
          },
          {
            label: '药材别名',
            prop: 'medicinalAlias',
            sortable: false
          },
          {
            label: '药材统计单位',
            prop: 'statisticUnits',
            sortable: false
          },
          {
            label: '同步状态',
            prop: 'status',
            sortable: false,
            slotName: 'status'
          },
          {
            label: '备注',
            prop: 'remark',
            sortable: false
          }
        ],
        operation: {
          label: '操作',
          width: '220px',
          data: [
            {
              label: '自动化设置',
              action: this.onAutomation,
              permission: 'all'
            },
            {
              label: '规格等级',
              action: this.onOpenLeavel,
              permission: 'all'
            },
            {
              label: '详情',
              action: this.onDetail,
              permission: 'all'
            },
            {
              label: '编辑',
              action: this.onEditClick,
              permission: 'all'
            }, {
              label: '删除',
              action: this.onDeleteClick,
              permission: 'all'
            }]
        }
      }
    }
  },
  mounted() {
    this.searchHelper.handleQuery()
  },
  methods: {
    addSpec(type) {
      if (type === 1) {
        this.specTableData.push({
          attrType: null
        })
      } else {
        this.leavelTableData.push({
          attrType: null
        })
      }
    },
    deleteSpec(index, type) {
      if (type === 1) {
        this.specTableData.splice(index, 1)
      } else {
        this.leavelTableData.splice(index, 1)
      }
    },
    submit() {
      this.$refs['attr-table'].validate(attrValid => {
        if (attrValid) {
          this.$refs['attr-type'].validate(attrValid => {
            if (attrValid) {
              accApi.editDrug({
                id: this.baseFrom.id,
                specificationName: JSON.stringify(this.specTableData),
                specificationGrade: JSON.stringify(this.leavelTableData)
              }).then(res => {
                this.$message.success(res.message)
                this.beianDialogVisible = false
                this.searchHelper.handleQuery()
              })
            }
          })
        }
      })
    },
    onAddClick() {
      this.$router.replace({ path: 'drugListDetail', query: { action: 'add' }})
    },
    onDeleteClick(row) {
      this.$confirm('请确认是否删除?', '提示').then(() => {
        accApi.delDrug(row).then(res => {
          this.$message.success(res.message)
          this.searchHelper.handleQuery()
        })
      })
    },
    onDetail(row) {
      this.$router.replace({ path: 'drugListDetail', query: { action: 'detail', id: row.id }})
    },
    onEditClick(row) {
      this.$router.replace({ path: 'drugListDetail', query: { action: 'edit', id: row.id }})
    },
    onAutomation(row) {
      this.aoTuMationDialogVisible = true
      this.aoTuMationFrom = row
      accApi.getTemplatesByProductsList({ productId: row.id }).then((res) => {
        this.templateList = res.data.map((e) => {
          return {
            label: e.name,
            value: e.id
          }
        })
      })
      accApi.getRuleProductList({ productId: row.id }).then((res) => {
        this.ruleList = res.data.map((e) => {
          return {
            label: e.ruleName,
            value: e.id
          }
        })
      })
    },
    submitAoToMation() {
      this.$refs.aoTuMaTion.validate(() => {
        accApi.medicinalProductEdit({ id: this.aoTuMationFrom.id, templateId: this.aoTuMationFrom.templateId, ruleId: this.aoTuMationFrom.ruleId }).then((res) => {
          this.$message.success(res.message)
          this.searchHelper.handleQuery()
        })
      })
    },
    onOpenLeavel(row) {
      this.beianDialogVisible = true
      this.baseFrom = row
      this.specTableData = JSON.parse(row.specificationName) || []
      this.leavelTableData = JSON.parse(row.specificationGrade) || []
    },
    deleteBeian(row, index) {
      accApi.delProductAttr(row).then(res => {
        this.$message.success(res.message)
        this.beianTableData.splice(index, 1)
      })
    },
    // 下载
    downFiles() {
      accApi.exportDrugApi({})
    },
    //  导入
    onChange(file, fileList) {
      console.log(file, fileList)
      const formData = new FormData()
      formData.append('file', file.raw)
      accApi.importDrugApi(formData).then((res) => {
        this.$message.success(res.message)
        this.searchHelper.handleQuery()
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-divider__text{
  font-weight: bolder;
  color: #004DA1;
}
::v-deep .el-divider{
 background-color: black;
}
::v-deep .el-query{
display: flex;
  gap: 10px;
}
.button-container {
  line-height: 60px;
  text-align: center;
}
</style>

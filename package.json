{"name": "vue-admin-template", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve --host 0.0.0.0", "build:prod": "vue-cli-service build", "build:uat": "vue-cli-service build --mode uat", "build:test": "vue-cli-service build --mode test", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "axios": "^0.27.2", "core-js": "3.6.5", "dayjs": "^1.10.4", "echarts": "^5.4.3", "element-ui": "2.15.1", "file-saver": "^2.0.5", "good-storage": "~1.1.1", "html2canvas": "^1.4.1", "js-cookie": "2.2.0", "jszip": "^3.10.1", "mathjs": "^10.4.3", "moment": "^2.29.1", "node-sass": "^4.14.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qf-export-excel": "^1.0.4", "qrcode": "^1.5.3", "qs": "^6.9.4", "screenfull": "^5.1.0", "sortablejs": "^1.10.2", "vue": "2.6.10", "vue-barcode": "1.3.0", "vue-cropperjs": "^4.2.0", "vue-i18n": "^8.22.2", "vue-qr": "^4.0.9", "vue-router": "3.0.6", "vue-virtual-scroller": "^1.0.10", "vuedraggable": "^2.24.3", "vuex": "3.1.0", "vxe-table": "3.6.11", "xe-utils": "^3.5.7"}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/eslint-parser": "^7.17.0", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-import": "1.13.6", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "^6.7.2", "eslint-plugin-vue": "6.2.2", "generate-asset-webpack-plugin": "^0.3.0", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "4.3.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-loader": "^15.9.6", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}
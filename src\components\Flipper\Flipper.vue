<template>
  <div
    v-bind="$attrs"
    :class="['Flipper', { 'Flipper--flipped': flipped }]"
    :style="styles.wrapper"
    v-on="$listeners"
  >
    <div
      class="Flipper__face Flipper__face--front"
      :style="styles.face"
    >
      <slot name="front" />
    </div>
    <div
      class="Flipper__face Flipper__face--back"
      :style="styles.face"
    >
      <slot name="back" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'Flipper',
  props: {
    flipped: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    duration: {
      type: String,
      default: '0.5s'
    },
    transition: {
      type: String,
      default: 'ease-in'
    }
  },
  computed: {
    styles() {
      return {
        wrapper: { width: this.width, height: this.height },
        face: {
          transitionDuration: this.duration,
          transitionTimingFunction: this.transition
        }
      }
    }
  }
}
</script>

<style scoped>
.Flipper {
  position: relative;
  -webkit-perspective: 1000px;
  -moz-perspective: 1000px;
  perspective: 1000px
}

.Flipper__face {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: block;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  backface-visibility: hidden
}

.Flipper--flipped .Flipper__face--front, .Flipper__face--back {
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  transform: rotateY(180deg);
  visibility: hidden
}

.Flipper--flipped .Flipper__face--back {
  -webkit-transform: rotateY(360deg);
  -moz-transform: rotateY(360deg);
  transform: rotateY(360deg);
  visibility: visible
}
</style>

/**
 * 数字千分位分割
 * 10000 => "10,000"
 * @param number {number} 需要转换的数字
 * @return number {string} 转换千分位之后的字符串
 */
export const toThousandFilter = (num) => {
  if (typeof num === `number`) {
    // return number.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
    num = num.toString().split('.') // 分隔小数点
    var arr = num[0].split('').reverse() // 转换成字符数组并且倒序排列
    var res = []
    for (var i = 0, len = arr.length; i < len; i++) {
      if (i % 3 === 0 && i !== 0) {
        res.push(',') // 添加分隔符
      }
      res.push(arr[i])
    }
    res.reverse() // 再次倒序成为正确的顺序
    if (num[1]) { // 如果有小数的话添加小数部分
      res = res.join('').concat('.' + num[1])
    } else {
      res = res.join('')
    }
    return res
  } else if (num !== undefined) {
    console.error(`错误：数字分割千分位是需要传入number类型`)
  }
  return num
}


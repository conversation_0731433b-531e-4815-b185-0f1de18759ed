import i18n from '@/i18n'

const constant = {
  searchBtnHeight: 38,
  operationBtnMinWidth: 60,
  operationBtnMiddleWidth: 110,
  operationBtnMaXWidth: 170,
  nbsp: '&nbsp;',
  verticalBar: '~',
  commonChar28: `,${String.fromCharCode(28)}`,
  initTableOption: {
    option: {
      height: null,
      maxHeight: null,
      stripe: true,
      border: false,
      enableSearch: false,
      size: null,
      fit: true,
      showHeader: true,
      highlightCurrentRow: false,
      currentRowKey: null,
      rowClassName: null,
      rowStyle: null,
      cellClassName: null,
      cellStyle: null,
      headerRowClassName: null,
      headerRowStyle: null,
      headerCellClassName: 'header',
      headerCellStyle: null,
      rowKey: 'rowKeyIndex',
      defaultExpandAll: false,
      expandRowKeys: null,
      defaultSort: { order: 'ascending' },
      tooltipEffect: 'dark',
      showSummary: false,
      summaryMethod: null,
      spanMethod: null,
      selectOnIndeterminate: true,
      indent: 16,
      lazy: null,
      load: null,
      treeProps: { hasChildren: 'hasChildren', children: 'children' },
      showPagination: true,
      enableSelected: false,
      cellClickSelected: true, // 点击当前行是否可以选中checkbox
      cellSelectedKey: '', // 点击当前行，选择复选框，唯一标识字段，rowSelected为true时，尽量指定。为兼容原先的使用可以不指定。
      excludeCellSelectList: [], // 不支持点击行选中的字段
      selectedWidth: 55
    },
    event: {
      select: null,
      selectAll: null,
      selectionChange: null,
      cellMouseEnter: null,
      cellMouseLeave: null,
      cellClick: null,
      cellDblclick: null,
      rowClick: null,
      rowContextmenu: null,
      rowDblclick: null,
      headerClick: null,
      headerContextmenu: null,
      sortChange: null,
      filterChange: null,
      headerDragend: null,
      expandChange: null,
      currentRowChange: null, // currentChange更改为currentRowChange（由于表格和分页都有currentChange，避免重名）
      sizeChange: null,
      prevClick: null,
      nextClick: null
    }
  },
  initColumnOption: {
    type: 'default',
    show: true,
    headSlotName: null,
    index: null,
    columnKey: null,
    rules: {},
    label: null,
    prop: null,
    width: null,
    minWidth: 120,
    fixed: false,
    renderHeader: null,
    sortable: 'custom',
    sortMethod: null,
    sortBy: null,
    sortOrders: ['ascending', 'descending', null],
    size: 'medium',
    resizable: true,
    required: undefined,
    formatter: null,
    slotNameShowTooltip: false,
    showOverflowTooltip: true,
    align: 'left',
    headerAlign: null,
    className: null,
    labelClassName: null,
    selectable: null,
    reserveSelection: false,
    filters: null,
    filterPlacement: null,
    filterMultiple: true,
    filteredValue: null,
    columnType: ''// 中文排序后端会乱序，默认不传，中文需指定CHINESE

  }, initPaginationOption: {
    small: false,
    background: false,
    pageSize: 10,
    total: null,
    pageCount: null,
    pagerCount: 7,
    pageNum: 1,
    pageSizes: [5, 10, 20, 50, 100],
    popperClass: null,
    prevText: null,
    nextText: null,
    disabled: false,
    hideOnSinglePage: null
  },
  initSwitchOption: {
    value: null,
    label: null,
    rules: null,
    show: true,
    option: {
      disabled: false,
      width: 40,
      activeIconClass: null,
      inactiveIconClass: null,
      activeText: null,
      inactiveText: null,
      activeValue: true,
      inactiveValue: false,
      activeColor: '#409EFF',
      inactiveColor: '#C0CCDA',
      name: null,
      validateEvent: true
    },
    event: {
      change: null
    }
  },
  initRadioOption: {
    value: null,
    label: null,
    rules: null,
    show: true,
    option: {
      size: 'small',
      disabled: false,
      textColor: '#ffffff',
      fill: '#409EFF',
      isButton: false,
      radioList: []
    },
    event: {
      change: null
    },
    initItemRadio: {
      label: null,
      disabled: false,
      border: false,
      size: 'small',
      name: null
    }
  },
  initTimePickerOption: {
    value: null,
    label: null,
    rules: null,
    show: true,
    option: {
      refName: null,
      readonly: false,
      disabled: false,
      editable: true,
      clearable: true,
      size: 'small',
      placeholder: i18n.t('common.select'),
      startPlaceholder: null,
      endPlaceholder: null,
      isRange: false,
      arrowControl: false,
      align: 'left',
      popperClass: null,
      pickerOptions: {},
      rangeSeparator: null,
      valueFormat: null,
      defaultValue: null,
      name: null,
      prefixIcon: 'el-icon-time',
      clearIcon: 'el-icon-circle-close'
    },
    event: {
      change: null,
      blur: null,
      focus: null
    }
  },
  initDatePickerOption: {
    value: null,
    label: null,
    rules: null,
    show: true,
    option: {
      refName: null,
      readonly: false,
      disabled: false,
      editable: true,
      clearable: true,
      size: 'small',
      placeholder: i18n.t('common.select'),
      startPlaceholder: null,
      endPlaceholder: null,
      type: 'date',
      format: 'yyyy-MM-dd',
      align: 'left',
      popperClass: null,
      pickerOptions: {},
      rangeSeparator: '-',
      defaultValue: null,
      defaultTime: null,
      valueFormat: null,
      name: null,
      unlinkPanels: true,
      prefixIcon: 'el-icon-date',
      clearIcon: 'el-icon-circle-close',
      validateEvent: true
    },
    event: {
      change: null,
      blur: null,
      focus: null
    }
  },
  initTimeSelectOption: {
    value: null,
    label: null,
    rules: null,
    show: true,
    option: {
      refName: null,
      readonly: false,
      disabled: false,
      editable: true,
      clearable: true,
      size: 'small',
      placeholder: i18n.t('common.select'),
      startPlaceholder: null,
      endPlaceholder: null,
      isRange: false,
      arrowControl: false,
      align: 'left',
      popperClass: null,
      pickerOptions: {},
      rangeSeparator: '-',
      defaultValue: null,
      defaultTime: null,
      valueFormat: null,
      name: null,
      prefixIcon: 'el-icon-time',
      clearIcon: 'el-icon-circle-close'
    },
    event: {
      change: null,
      blur: null,
      focus: null
    }
  },
  initSelectOption: {
    value: null,
    label: null,
    rules: null,
    show: true,
    option: {
      refName: null,
      slotName: null,
      multiple: false,
      disabled: false,
      valueKey: 'value',
      size: 'small',
      clearable: true,
      collapseTags: false,
      placeholder: i18n.t('common.select'),
      multipleLimit: 0,
      name: null,
      selectOptions: [],
      autocomplete: 'off',
      autoComplete: 'off',
      filterable: false,
      allowCreate: false,
      filterMethod: null,
      remote: false,
      remoteMethod: null,
      loading: false,
      popperClass: null,
      reserveKeyword: false,
      defaultFirstOption: false,
      popperAppendToBody: true,
      automaticDropdown: false
    },
    selectOption: {
      disabled: false
    },
    event: {
      change: null,
      visibleChange: null,
      removeTag: null,
      clear: null,
      blur: null,
      focus: null
    }
  },
  initInputOption: {
    value: null,
    label: null,
    rules: null,
    show: true,
    option: {
      type: 'text',
      refName: null,
      maxlength: null,
      minlength: null,
      showWordLimit: false,
      placeholder: i18n.t('common.enter'),
      clearable: true,
      showPassword: false,
      disabled: false,
      size: 'small',
      prefixIcon: null,
      suffixIcon: null,
      rows: 2,
      autosize: false,
      autocomplete: 'off',
      autoComplete: 'off',
      name: null,
      readonly: false,
      max: null,
      min: null,
      resize: null,
      autofocus: false,
      form: null,
      label: null,
      tabindex: null,
      validateEvent: true
    },
    event: {
      blur: null,
      focus: null,
      change: null,
      input: null,
      clear: null
    }
  },
  initNumberOption: {
    value: undefined,
    label: null,
    rules: null,
    show: true,
    option: {
      step: 1,
      stepStrictly: false,
      precision: 2,
      size: 'small',
      disabled: false,
      controls: false,
      controlsPosition: null,
      name: null,
      label: null,
      placeholder: i18n.t('common.enter')
    },
    event: {
      blur: null,
      focus: null,
      change: null
    }
  },
  initCascaderOption: {
    option: {
      options: null,
      props: null,
      size: 'small',
      disabled: false,
      clearable: false,
      showAllLevels: true,
      collapseTags: false,
      separator: '/',
      filterable: null,
      filterMethod: null,
      debounce: 300,
      beforeFilter: null,
      popperClass: null
    },
    event: {
      change: null,
      expandChange: null,
      blur: null,
      focus: null,
      visibleChange: null,
      removeTag: null
    }
  },
  initSlotOption: {
    value: null,
    label: null,
    show: true,
    option: {
      type: 'slot',
      refName: null
    }
  },
  defaultKeys: ['option', 'event', 'show', 'key', 'fullWidth', 'label', 'type', 'rules'],
  errorKeys: {
    '500': i18n.t('common.appWarning'),
    JWT_VERIFY_FAILED: i18n.t('common.jwtVerifyFailed'),
    LOGIN_FAILED: i18n.t('common.loginFail'),
    NO_LOGIN_ERROR: i18n.t('common.reLogin'),
    ACCESS_DENIED_ERROR: i18n.t('common.noPermisson')
  },
  weekKey: {
    0: i18n.t('common.Sunday'),
    1: i18n.t('common.Monday'),
    2: i18n.t('common.Tuesday'),
    3: i18n.t('common.Wednesday'),
    4: i18n.t('common.Thursday'),
    5: i18n.t('common.Friday'),
    6: i18n.t('common.Saturday')
  }
}
Object.keys(constant).forEach((key) => {
  Object.defineProperty(constant, key, { configurable: false, writable: false })
})
export default constant

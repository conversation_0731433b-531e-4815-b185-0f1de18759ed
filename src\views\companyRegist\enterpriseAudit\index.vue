<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      @return-search="(data) => {searchHelper.search(data)}"
      @return-reset="searchHelper.reset"
    />
    <!-- 表格 -->
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="() => {searchHelper.handleQuery()}"
    >

      <template
        v-slot:auditStatus="{row}"
      >
        <el-tag
          :type="statusMapping[row.auditStatus-1].tag"
          plain
          size="medium"
        >{{ statusMapping[row.auditStatus-1].label }}</el-tag>
      </template>
    </data-table>
  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import DataTable from '@/components/DataTable'
import acc from '@/api/acc/acc'

export default {
  name: 'DictMaintenanceDetail',
  components: {
    DataSelect,
    DataTable
  },
  data() {
    const statusMapping = [{
      label: '待审核',
      tag: 'warning',
      value: 1
    }, {
      label: '审核通过',
      tag: 'success',
      value: 2
    }, {
      label: '审核驳回',
      tag: 'danger',
      value: 3
    }]
    return {
      statusMapping,
      searchHelper: new this.$searchHelper({ api: acc.queryAuditList, handleSearchParams: this.handleSearchParams }),
      dialogVisible: false,
      dialogType: 0,
      detailForm: {},
      detailFormRules: {
        deptName: [
          { required: true, message: '请输入部门名称', trigger: 'blur' }
        ],
        deptCode: [
          { required: true, message: '请输入部门编码', trigger: 'blur' },
          {
            pattern: /^([0-9]{4})$/,
            message: `请输入4位数值！`,
            trigger: `blur`
          }
        ]
      },
      search: {
        account: {
          label: '用户账号',
          value: null,
          type: 'input',
          option: {
            placeholder: '请输入用户账号'
          }
        },
        auditStatus: {
          label: '状态',
          type: 'select',
          value: null,
          option: {
            selectOptions: statusMapping,
            placeholder: '请选择状态'
          }
        }
      },
      buttonData: [
        {
          label: '新增',
          action: this.onAddClick,
          permission: 'all'
        }
      ],
      column: {
        // 表头
        data: [
          {
            label: '序号',
            prop: 'index',
            sortable: false
          },
          {
            label: '用户账号',
            prop: 'account',
            sortable: false
          },
          {
            label: '状态',
            prop: 'auditStatus',
            slotName: 'auditStatus',
            sortable: false
          }
        ],
        // 操作
        operation: {
          label: '操作',
          width: '150px',
          data: (row) => {
            return row.auditStatus === '1'
              ? [
                {
                  label: '审核',
                  action: this.goAudit,
                  permission: 'all'
                }

              ] : [{
                label: '查看',
                action: this.onViewClick,
                permission: 'all'
              }]
          }

        }
      }
    }
  },
  mounted() {
    this.searchHelper.handleQuery()
  },
  methods: {
    // 表格查询条件
    handleSearchParams(params) {
      return Object.assign({}, params, { 'accountType': '2' })
    },
    // 投入明细表
    onAddClick() {
      this.detailForm = {}
      this.dialogType = 0
      this.dialogVisible = true
    },
    onEditClick(row) {
      this.dialogVisible = true
      this.dialogType = 1
      acc.detailDepartmentApi({ id: row.id }).then((res) => {
        this.detailForm = res.data
      })
    },
    // 去审核
    goAudit(row) {
      this.$router.replace({ path: 'enterpriseAuditView', query: { action: 'audit', id: row.id }})
    },
    // 去审核
    onViewClick(row) {
      this.$router.replace({ path: 'enterpriseAuditView', query: { action: 'view', id: row.id }})
    }
  }
}
</script>

<style scoped lang="scss">
.button-container {
  line-height: 60px;
  text-align: center;
}
</style>

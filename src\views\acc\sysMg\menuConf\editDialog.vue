<template>
  <data-dialog
    :dialog-visible.sync="dialogVisible"
    :dialog-data.sync="dialogData"
    :dialog-title="dialogTitle"
    :dialog-rule="dialogRule"
    :confirm-func="confirm"
  />
</template>

<script>
import DataDialog from '@/components/DataDialog'

export default {
  name: 'EditDialog',
  components: {
    DataDialog
  },
  props: {
  },
  data() {
    return {
      dialogType: 'edit',
      dialogVisible: false,
      dialogData: {
        menuId: {
          label: 'menuId',
          type: 'input',
          value: null,
          show: false
        },
        parentMenuCode: {
          label: '父级菜单编码',
          type: 'input',
          value: null,
          show: false
        },
        menuLevel: {
          label: '菜单层级',
          type: 'input',
          value: null,
          show: false
        },
        parentMenuName: {
          label: '上级菜单',
          type: 'input',
          value: null,
          option: {
            placeholder: ' ',
            readonly: true
          }
        },
        menuCode: {
          label: '菜单编码',
          type: 'input',
          value: null,
          option: {
            placeholder: '请输入菜单编码',
            maxlength: 30,
            disabled: this.dialogType === 'edit'
          }
        },
        menuName: {
          label: '菜单名称',
          type: 'input',
          value: null,
          option: {
            placeholder: '请输入菜单名称',
            maxlength: 30
          }
        },
        menuUrl: {
          label: '菜单URL',
          type: 'input',
          value: null,
          option: {
            placeholder: '请输入菜单URL',
            maxlength: 30
          }
        },
        sort: {
          label: '排序号',
          type: 'number',
          value: null,
          option: {
            placeholder: '请输入排序号',
            maxlength: 10
          }
        },
        menuType: {
          label: '菜单类型',
          type: 'select',
          value: null,
          option: {
            clearable: false,
            placeholder: '请选择菜单类型',
            selectOptions: [
              { value: '1', label: '菜单' },
              { value: '2', label: '按钮' }
            ]
          }
        }
      },
      dialogRule: {
        menuCode: [
          { required: true, message: '请输入菜单编码', trigger: 'blur' }
        ],
        menuName: [
          { required: true, message: '请输入菜单名称', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ],
        menuType: [
          { required: true, message: '请选择菜单类型', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogType === 'add' ? '新增' : '编辑'
    }
  },
  mounted() {
  },
  methods: {
    confirm(data) {
      this.$emit('confirm', data)
    },
    // type: add or edit
    open(type, dialogData) {
      this.dialogType = type

      if (type === 'add') {
        Object.keys(this.dialogData).forEach(key => {
          const item = this.dialogData[key]
          item.value = null
        })
        this.dialogData.parentMenuName.value = dialogData.menuName
        this.dialogData.parentMenuCode.value = dialogData.menuCode
        this.dialogData.menuLevel.value = dialogData.menuLevel + 1
        this.dialogData.menuCode.option.disabled = false
        this.dialogData.parentMenuName.show = true
        this.dialogData.menuType.fullWidth = false
        this.dialogData.menuType.value = dialogData.menuLevel < 3 ? '1' : '2'
      } else {
        Object.keys(this.dialogData).forEach(key => {
          const item = this.dialogData[key]
          item.value = dialogData[key]
        })
        this.dialogData.menuCode.option.disabled = true
        this.dialogData.parentMenuName.show = false
        this.dialogData.menuType.fullWidth = true
      }

      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>

</style>

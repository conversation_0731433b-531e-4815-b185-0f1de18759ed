<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="searchData"
      :button-data="buttonData"
      @return-search="onSearchClick"
      @return-reset="reset"
    />
    <!-- 表格 -->
    <data-table
      ref="dataTable"
      :table-data="tableData"
      :column="column"
      :pagination.sync="pagination"
      :table-option="tableOption"
      @search-event="queryUserList"
    >
      <template v-slot:isDisable="{ row }">
        {{ row.isDisable==='Y'?'禁用':'启用' }}
      </template>
      <template v-slot:userName="{ row }">
        <span v-if="$store.state.user.userCode!=='admin'&&row.emplCode==='admin'">{{ row['userName'] }}</span>
        <el-link v-else type="primary" @click="onDetailClick(row)">{{ row['userName'] }}</el-link>
      </template>
    </data-table>
    <!-- 明细弹出框 -->
    <simple-data-dialog
      v-if="dialogVisible"
      :title="dialogTitle"
      :visible="true"
      size="middle"
    >
      <template v-slot:default>
        <el-scrollbar class="scrollbar" wrap-class="scrollbar__wrap">
          <user-detail ref="userDetail" />
        </el-scrollbar>
      </template>
      <template v-slot:footer>
        <div class="dialog_btn">
          <el-button @click="dialogVisible=false">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="onSaveClick">{{ $t('common.ok') }}</el-button>
        </div>
      </template>
    </simple-data-dialog>
    <user-permissions
      v-model="userPermissions.showUserPermissions"
      :permission-type="userPermissions.permissionType"
      :users="selection"
    />
  </div>
</template>

<script>
import DataSelect from '@/components/DataSelect'
import SimpleDataDialog from '@/components/SimpleDataDialog'
import DataTable from '@/components/DataTable'
import UserDetail from './UserDetail'
import accApi from '@/api/acc/acc'

export default {
  name: 'Index',
  components: {
    DataSelect,
    SimpleDataDialog,
    DataTable,
    UserDetail
  },
  data() {
    return {
      // 搜索栏组件
      searchData: {
        emplCode: {
          label: this.$t('acc.userInfo.emplCode'),
          type: 'input',
          value: '',
          option: {
            placeholder: this.$t('common.enter') + this.$t('acc.userInfo.emplCode')
          }
        },
        userName: {
          label: this.$t('acc.userInfo.userName'),
          type: 'input',
          value: '',
          option: {
            placeholder: this.$t('common.enter') + this.$t('acc.userInfo.userName')
          }
        },
        // roleCodeList: {
        //   label: this.$t('acc.userInfo.role'),
        //   value: null, type: 'select',
        //   option: {
        //     selectOptions: [],
        //     placeholder: this.$t('common.select') + this.$t('acc.userInfo.role'),
        //     multiple: true
        //   }
        // },
        isDisable: {
          label: this.$t('common.status'),
          type: 'select',
          value: 'N',
          option: {
            selectOptions: [],
            placeholder: this.$t('common.select') + this.$t('common.status')
          }
        }
      },
      buttonData: [
        {
          permission: 'all',
          label: this.$t('common.add') + this.$t('acc.userInfo.user'),
          action: this.onAddClick
        }
        // {
        //   permission: 'userInfo:auth',
        //   label: '权限授权',
        //   action: this.onAuthClick
        // }
      ],
      // 数据表格组件
      tableData: [],
      selection: [],
      selectKeys: [],
      column: {
        // 数据列
        data: [
          { label: 'guid', prop: 'guid', show: false },
          { label: this.$t('acc.userInfo.emplCode'), prop: 'emplCode' },
          { label: this.$t('acc.userInfo.userName'), prop: 'userName', slotName: 'userName' },
          { label: this.$t('acc.userInfo.mail'), prop: 'mail' },
          { label: this.$t('acc.userInfo.phone'), prop: 'phone' },
          // { label: '部门名称', prop: 'spaceName' },
          { label: this.$t('common.status'), prop: 'isDisable', slotName: 'isDisable' }
        ]
      },
      // 分页参数
      pagination: {
        pageNum: 1,
        pageSize: this.$constant.initPaginationOption.pageSize,
        total: 0
      },
      tableOption: {
        option: {
          enableSelected: true,
          cellClickSelected: false
        },
        event: {
          selectionChange: this.onSelectionChange,
          rowClick: this.onRowClick
        }
      },
      // 弹出框组件
      dialogTitle: '',
      dialogVisible: false,
      userPermissions: {
        showUserPermissions: false,
        permissionType: ''
      },
      queryParam: {},
      saveParam: {},
      emplStatusMap: {}
    }
  },
  mounted() {
    // debugger
    this.queryParam = { isDisable: 'N' }
    this.initView()
    // this.initRoleOption()
  },
  activated() {
  },
  methods: {
    // initRoleOption() {
    //   accApi.getRoleList().then((res) => {
    //     res.data.list.forEach((item) => {
    //       this.searchData.roleCodeList.option.selectOptions.push({ label: item.roleName, value: item.roleCode })
    //     })
    //   })
    // },
    initView() {
      // accApi.codeQueryList('USER_IS_DISABLE_TYPE').then(result => {
      //   const selectOptions = []
      //   result.data.forEach(code => {
      //     this.emplStatusMap[code.code] = code.codeName
      //     selectOptions.push({ value: code.code, label: code.codeName })
      //   })
      //
      // })
      this.searchData.isDisable.option.selectOptions = [{ value: 'N', label: '启用' }, { value: 'Y', label: '禁用' }]
      this.queryUserList()
    },
    queryUserList() {
      const data = Object.assign(this.queryParam, this.pagination)
      accApi.userInfo.queryList(data).then(result => {
        this.tableData = result.data.list
        this.pagination = result.data.pagination
      })
    },
    reset() {
      this.pagination.pageNum = 1
      this.pagination.pageSize = 20
      this.queryParam = {}
      this.queryUserList()
    },
    // 搜索按钮
    onSearchClick(data) {
      this.pagination.pageNum = 1
      this.queryParam = data
      this.queryUserList()
    },
    // 新增按钮
    onAddClick() {
      this.dialogTitle = this.$t('common.add')
      this.dialogVisible = true
    },
    onAuthClick() {
      if (this.selectKeys.length > 0) {
        this.userPermissions.showUserPermissions = true
        this.userPermissions.permissionType = 'data'
      } else {
        this.$message({
          message: this.$t('acc.userInfo.checkUserRow').toString(),
          type: 'warning'
        })
      }
    },
    onExcelClick() {
      let params = {}
      if (this.$refs.dataTable.$refs.multipleTable.selection.length === 0) {
        params = Object.assign(this.queryParam)
      } else {
        params.emplCodes = this.$refs.dataTable.$refs.multipleTable.selection.map((list) => {
          return list.emplCode
        })
      }
      accApi.userInfo.exportUserExcel(params).then()
    },
    onDetailClick(row) {
      const userDetailRouter = {
        path: 'bypassAccountView',
        query: {
          emplCode: row.emplCode,
          guid: row.guid
        }
      }
      this.$router.replace(userDetailRouter)
    },
    // 保存按钮
    onSaveClick() {
      this.$refs.userDetail.$emit('submit', (result) => {
        this.$message.success(result.msg)
        this.dialogVisible = false
        this.queryUserList()
      })
    },
    onSelectionChange(selection) {
      this.selection = selection
      this.selectKeys = []
      selection.forEach(row => this.selectKeys.push(row['emplCode']))
    },
    onRowClick(row) {
      const isCheck = !this.selectKeys.includes(row['emplCode'])
      this.$refs.dataTable.toggleRowSelection([row], isCheck)
    }
  }
}
</script>

<style lang="scss" scoped>
.el-scrollbar__wrap {
  overflow-x: hidden
}

.scrollbar {
  height: calc(60vh);

  ::v-deep .scrollbar__wrap {
    overflow-x: hidden;
  }
}

::v-deep {
  .el-table tr {
    cursor: pointer;
  }
  .el-checkbox__inner {
    padding: 0;
    border-radius: 2px;

    &:after {
      padding: 0;
    }
  }
}

</style>

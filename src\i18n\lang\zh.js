export default {
  // 共通
  common: {
    homePage: '首页',
    homeMainPage: '首页',
    changePassword: '修改密码',
    initialPassword: '修改初始密码',
    login: '登录',
    logout: '注销',
    date: '日期',
    system: '欢迎登录',
    systemName: '中药行业工业互联网标识应用平台',
    query: '查 询',
    reject: '拒绝',
    refresh: '刷&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;新',
    reset: '重 置',
    create: '新 建',
    adjust: '调整',
    add: '新增',
    newConstruction: '新建',
    edit: '编辑',
    batchApprove: '批量审批',
    approve: '审批',
    view: '查看',
    detail: '详细',
    delete: '删除',
    set: '设置',
    batchSet: '批量设置',
    expand: '展开',
    hide: '收起',
    inForce: '已生效',
    notInForce: '未生效',
    invalid: '已失效',
    cancel: '取 消',
    cancelMessage: '取消',
    close: '关 闭',
    closeTitle: '关&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;闭',
    closeOthers: '关闭其他',
    closeAll: '关闭所有',
    noApprove: '无权限',
    ok: '确 定',
    okMessage: '确定',
    prompt: '提示',
    operation: '操作',
    enter: '请输入',
    select: '请选择',
    upload: '请上传',
    uploadFile: '上传文件',
    enable: '启用',
    unenable: '禁用',
    back: '返回',
    save: '保存',
    submit: '提交',
    append: '添加',
    element: '元',
    success: '成功',
    affirm: '确认',
    isConfirmSave: '是否确认保存?',
    unconfirmed: '待确认',
    confirmed: '已确认',
    toBeConfirmed: '待确认',
    operationSuccess: '操作成功',
    saveSuccess: '保存成功',
    addSuccess: '新增成功',
    copySeccess: '复制成功',
    submitSuccess: '提交成功',
    selectStore: '选择门店',
    approveSuccess: '审批成功',
    selectAllMove: '是否全部移动',
    moveAll: '移动全部',
    moveSuccess: '移动成功',
    noValueSave: '没有数据可以保存',
    cancelAddSuccess: '已取消新增',
    addFail: '新增失败',
    multiple: '的倍数',
    editSuccess: '编辑成功',
    reviseSuccess: '密码修改成功，请重新登陆',
    editFail: '编辑失败',
    delSuccess: '删除成功',
    delFail: '删除失败',
    confirm: '请确认是否',
    format: '格式不正确',
    record: '请选择记录',
    confirmDel: '请确认是否删除?',
    confirmBussionDel: '删除可能会导致业务数据出错，确认是否删除？',
    isEnable: '是否生效',
    estimatedSales: '预估营业额',
    remark: '备注',
    updateUser: '最后修改人',
    updateTime: '最后修改时间',
    imgLimit: '图片大小不能超过4M',
    imgFileType: '只能上传png,jpg,gif格式的文件',
    excelType: '只能上传xls,xlsx格式的文件',
    allSelect: '全选',
    excel: '导出',
    exportExcel: '导出Excel',
    display: '显示',
    hidden: '隐藏',
    startDate: '开始日期',
    endDate: '结束日期',
    startTime: '开始时间',
    endTime: '结束时间',
    sameDay: '当日',
    nextDay: '次日',
    yes: '是',
    no: '否',
    chooseNotExit: '选择的记录不存在',
    chooseDisplaySign: '请选择显示标识',
    long: '长度在',
    to: '到',
    charNo: '个字符',
    error: '错误',
    staging: '暂存',
    week2: '星期',
    Monday: '周一',
    Tuesday: '周二',
    Wednesday: '周三',
    Thursday: '周四',
    Friday: '周五',
    Saturday: '周六',
    Sunday: '周日',
    MondayHan: '一',
    TuesdayHan: '二',
    WednesdayHan: '三',
    ThursdayHan: '四',
    FridayHan: '五',
    SaturdayHan: '六',
    SundayHan: '日',
    month: '月',
    dayHan: '日',
    hour: '时',
    minutes: '分',
    just: '刚刚',
    minutesAgo: '分钟前',
    hoursAgo: '小时前',
    oneDayAgo: '1天前',
    rightArrow: '>',
    rightDoubleArrow: '>>',
    leftArrow: '<',
    leftDoubleArrow: '<<',
    emptyText: '暂无数据',
    emptyImg: '暂无图片',
    sumText: '合计',
    moreOperations: '更多操作',
    loading: '加载中',
    noMatchText: '无匹配数据',
    all: '全部',
    seqId: '序号',
    repeat: '重复',
    integerValues: '整数值',
    advancedFilter: '高级筛选',
    screeningConditions: '清除筛选条件',
    status: '状态',
    description: '描述',
    keyword: '关键字',
    searchMenu: '要搜索的菜单',
    search: '搜索',
    code: '编码',
    require: '必填',
    positiveNumber: '为正数',
    and: '且',
    timeRange: '时间范围',
    dateRange: '日期范围',
    uploadImg: '上传图片',
    viewImg: '查看图片',
    range: '范围',
    day: '天',
    yuan: '元',
    loginOutMessage: '你已被登出，可以取消继续留在该页面，或者重新登录',
    leastOneData: '请至少选择一条数据',
    onlyOneExport: '只能导出一条数据',
    loginOut: '确定登出',
    relogin: '重新登录',
    loginTimeOut: '请求超时，请稍后再试!',
    loginDisabledMessage: '账号已被禁用或账号不存在',
    loginForbidden: '系统繁忙请稍后重试',
    lastSixMonths: '最近六个月',
    yearToDate: '今年至今',
    thisMonth: '本月',
    lastThreeMonths: '最近三个月',
    lastOneMonth: '最近一个月',
    lastWeek: '最近一周',
    lastMonth: '上月',
    lastWeek2: '上周',
    thisWeek: '本周',
    yesterday: '昨天',
    today: '今天',
    di: '第',
    week: '周',
    notNull: '不能为空',
    notZero: '不能为0',
    seqIdValidator: '请输入数字值',
    numberLetters: '请输入数字、字母',
    codeValidate: '只能输入数字、字母或特殊字符(@._-)',
    incorrectMailbox: '邮箱不正确',
    valiScale: '限制0~100之间,最多保留两位数',
    valiCommonname: '限制1~6位汉字、英语或数字',
    valiNickname: '限制2~20位汉字、英语、数字或下划线',
    valiUsername: '限制2~10位汉字、英语、数字或下划线',
    valiName: '限制2~10位汉字',
    valiTel: '手机号不正确',
    valiSeq: '限制1~6位数字',
    langSelect: '语言选择',
    brandCodeSelect: '品牌选择',
    projectSelect: '项目选择',
    helpMessage: '这边应该显示菜单描述信息，但是后台没有把这个信息查出来，所以目前没办法显示。',
    relevanceMerchandise: '关联货品',
    associationStore: '关联门店',
    uploadText: '只能上传jpg/png文件，且不超过4M',
    pleaceSelectData: '请选择数据',
    promisingFuture: '未来可期',
    storeCode: '门店编码',
    storeName: '门店名称',
    brandName: '品牌',
    filiter: '请输入关键字',
    uploadBtn: '点击上传',
    uploadSuccess: '上传成功',
    uploadFail: '上传失败',
    downloadErrorFile: '下载错误文件',
    pleaseSelectStoreCode: '请选择科室',
    tip: '只能上传jpg/png文件，且不超过500kb',
    noSelectStoreCode: '未选择科室，不选择将退出该模块',
    fixTelAndTel: '电话号码不正确',
    selectDay: '选择日期',
    selectWeek: '选择周',
    selectMonth: '选择月',
    amount: '数量',
    one: '一',
    two: '二',
    three: '三',
    four: '四',
    five: '五',
    release: '发布',
    none: '无',
    submitAction: '执行',
    appWarning: '系统错误',
    reLogin: '用户未登录，重定向到登录页面',
    noPermisson: '权限不足，请联系管理员',
    loginFail: '账号或密码不正确',
    jwtVerifyFailed: 'JWT Token 验证失败',
    searchBeforeSave: '数据已修改，是否先保存数据？',
    selectDate: '日期类型',
    selectMerchandise: '选取货品',
    pleaseSelectMrchandise: '请选取货品',
    merchandiseType: '营运类别选择',
    searchMerchandise: '货品查询'
  },

  home: {
    account: '请输入账号/用户名',
    pwd: '请输入登录密码'
  },
  layout: {
    oldPassword: '旧密码',
    newPassword: '新密码',
    confirmPsw: '确认新密码'
  },
  acc: {
    // 用户信息配置
    userInfo: {
      basicInfo: '基础信息',
      contractInfo: '合同信息',
      otherInfo: '其他信息',
      user: '用户',
      emplCode: '用户账号',
      deptCode: '部门编号',
      deptName: '部门名称',
      userName: '姓名',
      sex: '性别',
      workdayId: 'workdayId',
      identityId: '身份证号',
      dateOfEntry: '入职日期',
      mail: '邮箱',
      phone: '电话',
      birthdate: '生日',
      dept: '所属部门',
      staffId: 'staffId',
      jobName: '职务',
      role: '角色',
      contractType: '合同类型',
      contractDateRange: '合同日期范围',
      contractStartDate: '合同开始日期',
      contractEndDate: '合同结束日期',
      certificateType: '证件类型',
      marriageOrNot: '婚否',
      resetPwd: '重置密码',
      roleAuthorization: '角色授权',
      dataPermissions: '权限授权',
      roleName: '角色名称',
      selectRole: '角色选择',
      rolePermission: '角色授权',
      dataPermission: '权限授权',
      dataPermissionType: '行数据类型',
      dataPermissionValue: '行数据权限',
      resetPasswordSuccess: '密码重置成功。重置后的密码是<span style="color: #1890ff;font-size: 20px;line-height:1.5em;padding: 0 5px">123456</span>请牢记！',
      checkUserRow: '请选择用户数据！'
    },
    // 角色权限配置
    role: {
      roleCode: '角色编码',
      roleName: '角色名称',
      setFunByRole: '按角色设置功能',
      setUserByRole: '按角色设置用户',
      codeTip: '角色编码由系统生成',
      empCode: '用户账号',
      userName: '姓名'
    }
  }
}

<template>
  <div class="app-container">
    <el-container :style="{ height: '100%' }">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item title="产品基本信息" :name="1">
            <el-form
              ref="basic"
              label-position="top"
              :model="basicFormModel"
              :rules="basicFormRules"
              :disabled="action === 'detail'"
              :inline="true"
            >
              <el-form-item
                prop="medicinalBatchCode"
                label="药材产品批号："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.medicinalBatchCode"
                  placeholder="请输入药材产品批号"
                />
              </el-form-item>

              <el-form-item
                prop="medicinalProductId"
                label="药材产品名称："
                class="el-form-item-width"
              >
                <el-select
                  v-model="basicFormModel.medicinalProductId"
                  placeholder="请选择药材产品名称"
                  @change="changeIds"
                >
                  <el-option
                    v-for="item in medicinalList"
                    :key="item.value"
                    v-bind="item"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                prop="specificationGrade1"
                label="规格："
                class="el-form-item-width"
              >
                <el-select
                  v-model="basicFormModel.specificationGrade1"
                  placeholder="请输入规格下拉框"
                  clearable
                >
                  <el-option
                    v-for="item in specificationNameList"
                    :key="item.value"
                    v-bind="item"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                prop="specificationGrade2"
                label="等级："
                class="el-form-item-width"
              >
                <el-select
                  v-model="basicFormModel.specificationGrade2"
                  clearable
                  placeholder="请输入等级下拉框"
                >
                  <el-option
                    v-for="item in specificationGradeList"
                    :key="item.value"
                    v-bind="item"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                prop="batchProductionTime"
                label="批次生产时间："
                class="el-form-item-width"
              >
                <el-date-picker
                  v-model="basicFormModel.batchProductionTime"
                  type="date"
                  style="width: 100%"
                  placeholder="请选择批次生产时间"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>

              <el-form-item
                prop="number"
                label="本批数量："
                class="el-form-item-width"
              >
                <el-input-number
                  v-model="basicFormModel.number"
                  placeholder="请输入本批数量"
                />
              </el-form-item>

              <el-form-item
                prop="expirationDate"
                label="保质期："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.expirationDate"
                  placeholder="请输入保质期"
                />
              </el-form-item>

              <el-form-item
                prop="principal"
                label="负责人："
                class="el-form-item-width"
              >
                <el-input
                  v-model="basicFormModel.principal"
                  placeholder="请输入负责人"
                />
              </el-form-item>

              <el-form-item
                prop="codingMode"
                label="赋码方式："
                class="el-form-item-width"
              >
                <el-select
                  v-model="basicFormModel.codingMode"
                  placeholder="请选择赋码方式"
                >
                  <el-option :value="0" label="自动赋码" />
                  <el-option :value="1" label="手动赋码" />
                </el-select>
              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="批次关联信息" :name="2">
            <div>
              <virtual-table
                ref="table"
                :enable-search="false"
                auto-height
                :columns="specColumns"
                :table-data="specTableData"
                :edit-rules="specRules"
              >
                <template v-slot:jobName="{ row, scope }">
                  <el-input
                    v-model="row.jobName"
                    placeholder="请输入作业名称"
                    @change="
                      $refs.table.updateStatus(scope);
                      getJobNameList();
                    "
                  />
                </template>

                <template v-slot:subsequentJobName="{ row, scope }">
                  <el-select
                    v-model="row.subsequentJobName"
                    placeholder="请输入后续作业名称"
                    @change="$refs.table.updateStatus(scope)"
                  >
                    <el-option
                      v-for="item in subsequentJobNameList"
                      :key="item.value"
                      v-bind="item"
                    />
                  </el-select>
                </template>

                <template v-slot:jobBatchNumber="{ row, scope }">
                  <el-input
                    v-model="row.jobBatchNumber"
                    placeholder="请输入作业批次号"
                    @change="$refs.table.updateStatus(scope)"
                  />
                </template>

                <template v-slot:jobPlacer="{ row, scope }">
                  <el-input
                    v-model="row.jobPlacer"
                    placeholder="请输入作业地点"
                    @change="$refs.table.updateStatus(scope)"
                  />
                </template>

                <template v-slot:jobTime="{ row, scope }">
                  <el-date-picker
                    v-model="row.jobTime"
                    type="datetime"
                    placeholder="请选择作业时间"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    @change="$refs.table.updateStatus(scope)"
                  />
                </template>

                <template v-slot:principal="{ row, scope }">
                  <el-input
                    v-model="row.principal"
                    placeholder="请输入负责人"
                    @change="$refs.table.updateStatus(scope)"
                  />
                </template>
                <template v-slot:operate="{ $index }">
                  <span class="text_button" @click="deleteSpec($index)"
                    >删除</span
                  >
                </template>
              </virtual-table>
              <demo-block
                message="添加批次"
                :icon-class="'el-icon-plus icon-class'"
                @click.native="addSpec"
              />
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-main>
      <el-footer class="button-container">
        <el-button @click="back">返回</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </el-footer>
    </el-container>
  </div>
</template>
<script>
import DemoBlock from "@/components/DemoBlock";
import VirtualTable from "@/components/VirtualTable/VirtualTable";

import accApi from "@/api/acc/acc";

export default {
  name: "ProductDetail",
  components: {
    DemoBlock,
    VirtualTable,
  },
  data() {
    return {
      action: null,
      categoryList: [],
      subsequentJobNameList: [],
      beianTableData: [],
      beianDialogVisible: false,

      collapse: [1, 2, 3],
      formRules: {
        materialName: [
          { required: true, message: "请输入物资名称", trigger: "blur" },
        ],
      },
      // 表头
      specColumns: [
        {
          type: "seq",
          title: "序号",
        },
        {
          title: "作业名称",
          field: "jobName",
          slotName: "jobName",
        },
        {
          title: "后续作业名称",
          field: "subsequentJobName",
          slotName: "subsequentJobName",
        },
        {
          title: "作业批次号",
          field: "jobBatchNumber",
          slotName: "jobBatchNumber",
        },
        {
          title: "作业地点",
          field: "jobPlacer",
          slotName: "jobPlacer",
        },
        {
          title: "作业时间",
          field: "jobTime",
          slotName: "jobTime",
        },
        {
          title: "负责人",
          field: "principal",
          slotName: "principal",
        },
        {
          title: "操作",
          field: "operate",
          slotName: "operate",
          width: "50px",
        },
      ],

      specRules: {
        // 作业名称
        jobName: [
          { required: true, message: "请输入作业名称", trigger: "blur" },
        ],

        // 后续作业名称
        subsequentJobName: [
          { required: true, message: "请输入后续作业名称", trigger: "change" },
        ],

        // 作业批次号
        jobBatchNumber: [
          { required: true, message: "请输入作业批次号", trigger: "blur" },
        ],
        // 作业时间
        jobTime: [
          { required: true, message: "请选择作业时间", trigger: "change" },
        ],
      },
      specTableData: [],
      medicinalList: [],
      specificationNameList: [],
      specificationGradeList: [],
      basicFormModel: {},
      productImgList: [],
      basicFormRules: {
        medicinalBatchCode: [{ required: true, message: "请输入药材产品批号" }],
        medicinalProductId: [
          { required: true, message: "请选择药材产品名称", trigger: "change" },
        ],
        batchProductionTime: [
          { required: true, message: "请选择批次生产时间" },
        ],
        codingMode: [
          { required: true, message: "请选择赋码方式", trigger: "change" },
        ],
      },
    };
  },
  computed: {},
  created() {
    this.getSelection();
  },
  mounted() {
    this.action = this.$route.query.action;
    if (this.action === "edit" || this.action === "detail") {
      this.$nextTick(() => {
        this.queryProduct();
      });
    }
  },
  methods: {
    getJobNameList() {
      const arrList = this.specTableData
        .slice(0, -1)
        .map((res) => {
          return {
            value: res.jobName,
            label: res.jobName,
          };
        })
        .filter(
          (item, index, self) =>
            index ===
            self.findIndex((t) => t.value === item.value && item.value)
        );
      console.log(arrList, "arrList");
      arrList.unshift({
        label: "生产(包装)",
        value: "生产(包装)",
      });
      this.subsequentJobNameList = arrList;
      console.log(this.subsequentJobNameList);
    },
    // 药材下拉框
    getSelection() {
      // 药材产品名称
      accApi.drugListAll().then((res) => {
        this.medicinalList = res.data.map((e) => {
          return {
            label: e.medicinalProductName,
            value: e.id,
            specificationName: e.specificationName,
            specificationGrade: e.specificationGrade,
          };
        });
      });
    },
    // 规格下拉框
    changeIds() {
      this.$set(this.basicFormModel, "specificationGrade1", undefined);
      this.$set(this.basicFormModel, "specificationGrade2", undefined);
      // 药材产品名称
      var arr = this.medicinalList.find(
        (e) => e.value === this.basicFormModel.medicinalProductId
      );
      this.specificationNameList = (
        JSON.parse(arr.specificationName) || []
      ).map((e) => {
        return {
          label: e.attrNameCn,
          value: e.attrNameCn,
        };
      });
      this.specificationGradeList = (
        JSON.parse(arr.specificationGrade) || []
      ).map((e) => {
        return {
          label: e.attrNameCn,
          value: e.attrNameCn,
        };
      });
    },
    // 查询详情
    queryProduct() {
      accApi.queryMedicinalBatch({ id: this.$route.query.id }).then((res) => {
        const obj = res.data[0];
        this.basicFormModel = obj;
        this.specTableData = obj.medicinalBatchDetailList || [];
      });
    },

    addSpec() {
      this.specTableData.push({
        jobName: null,
        subsequentJobName: null,
        jobBatchNumber: null,
        jobPlacer: null,
        jobTime: null,
        principal: null,
      });
      this.getJobNameList();
    },

    deleteSpec(index) {
      this.specTableData.splice(index, 1);
      this.getJobNameList();
    },
    deleteImg(index) {
      this.imgTableData.splice(index, 1);
    },
    back() {
      this.$router.replace("drugBatchNo");
    },
    submit() {
      this.$refs.basic.validate((valid) => {
        if (valid) {
          this.$refs["table"].validate((attrValid) => {
            if (attrValid) {
              const api =
                this.action === "edit"
                  ? "editMedicinalBatch"
                  : "addMedicinalBatch";
              accApi[api]({
                ...this.basicFormModel,
                id: this.$route.query.id,
                medicinalBatchDetailList: this.specTableData || [],
              }).then((res) => {
                this.$message.success(res.message);
                this.back();
              });
            }
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.el-select--small,
.el-cascader--small,
.el-input-number--small {
  width: 100%;
}

.button-container {
  line-height: 60px;
  text-align: center;
}
</style>

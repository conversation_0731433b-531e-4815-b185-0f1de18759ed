/**
 * 对 element-ui 的无限滚动在 el-table 上使用的封装
 */
import elInfiniteScroll from 'element-ui/lib/infinite-scroll'

const elScope = 'ElInfiniteScroll' // scope name
const elTableScrollWrapperClass = '.el-table__body-wrapper'

export default {
  name: 'TableInfiniteScroll',
  inserted(el, binding, vnode, oldVnode) {
    // 获取 table 中的滚动层
    const scrollElem = el.querySelector(elTableScrollWrapperClass)

    // 设置自动滚动
    scrollElem.style.overflowY = 'auto'

    // dom 渲染后
    asyncElOptions(vnode, el, scrollElem)

    // 绑定 infinite-scroll
    elInfiniteScroll.inserted(scrollElem, binding, vnode, oldVnode)

    // 将子集的引用放入 el 上，用于 unbind 中销毁事件
    el[elScope] = scrollElem[elScope]
  },
  componentUpdated(el, binding, vnode) {
    asyncElOptions(vnode, el, el.querySelector(elTableScrollWrapperClass))
  },
  unbind: elInfiniteScroll.unbind
}

/**
 * 同步 el-infinite-scroll 的配置项
 * @param sourceVNode
 * @param sourceElem
 * @param targetElem
 */
function asyncElOptions(sourceVNode, sourceElem, targetElem) {
  let value;
  ['disabled', 'delay', 'immediate'].forEach((name) => {
    name = 'infinite-scroll-' + name
    value = sourceElem.getAttribute(name)
    if (value !== null) {
      targetElem.setAttribute(name, value)
    }
  })

  const name = 'infinite-scroll-distance'
  value = sourceElem.getAttribute(name)
  targetElem.setAttribute(name, value < 1 ? 1 : value)
}

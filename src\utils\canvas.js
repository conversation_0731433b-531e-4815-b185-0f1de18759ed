
export function draw(url) {
  var title = '中国工业互联网标识管理中心统一标识' // 自定义文本内容
  var companyTitle = '江苏国钥云技术有限公司'
  var canvas = document.getElementById('canvas')
  var ctx = canvas.getContext('2d')
  var whColor = 'rgb(8, 77, 161)'
  var fontColor = 'rgb(8, 77, 161)'

  if (canvas.getContext) {
    // 开始绘制路径
    /**
         ctx.arc(x, y, radius, startAngle, endAngle, Boolean)
         圆心坐标: (x, y)
         半径: radius
         起始角度: startAngle
         结束角度: endAngle
         是否逆时针旋转: false 代表顺时针旋转
         */
    // 外圈蓝色圆形
    ctx.beginPath()
    ctx.lineWidth = 6
    ctx.strokeStyle = whColor
    // 绘制圆的路径**
    ctx.arc(130, 130, 120, 0, Math.PI * 2, false)
    // 描边路径
    ctx.stroke()

    ctx.beginPath()
    // ctx.lineWidth = 6;

    // 绘制圆的路径**
    ctx.arc(130, 130, 90, 0, Math.PI * 2, false)
    ctx.fillStyle = whColor
    ctx.fill()
    var circle = {
      x: 130,
      y: 130,
      radius: 104,
      whColor: whColor,
      fontColor: fontColor
    }
    // 平台标识
    drawCircularText(circle, title, rads(160), rads(12), ctx)

    var companyCircle = {
      x: 130,
      y: 130,
      radius: 102,
      whColor: whColor,
      fontColor: fontColor
    }
    // 企业标识
    drawCompanyCircularText(companyCircle, companyTitle, ctx)

    // 画二维码
    var img = new Image()
    img.src = url
    img.onload = function() {
      ctx.drawImage(img, 70, 70, 120, 120)
    }
  }
}

// 转换弧度
function rads(x) {
  return Math.PI * x / 180
}

function drawCompanyCircularText(s, string, ctx) {
  var radius = s.radius // 圆的半径
  // angleDecrement = (startAngle - endAngle) / (string.length - 1), //每个字母占的弧度
  var count = string.length / 2

  // 单个字符弧度
  var angleDecrement = 0.18
  // 计算初始位置
  var angle = parseFloat(rads(270)) - angleDecrement * (count - 1)
  var index = 0
  var character

  ctx.save()
  ctx.fillStyle = s.fontColor
  ctx.font = '16px 微软雅黑'
  ctx.textAlign = 'right'
  ctx.textBaseline = 'middle'
  while (index < string.length) {
    character = string.charAt(index)
    ctx.save()
    ctx.beginPath()
    ctx.translate(s.x + Math.cos(angle) * radius,
      s.y + Math.sin(angle) * radius)
    ctx.rotate(Math.PI / 2.1 + angle)
    // 文字调转180
    ctx.fillText(character, 0, 0)
    angle += angleDecrement
    index++
    ctx.restore()
  }
  ctx.restore()
}

function drawCircularText(s, string, startAngle, endAngle, ctx) {
  var radius = s.radius // 圆的半径
  var angleDecrement = (startAngle - endAngle) / (string.length - 1) // 每个字母占的弧度
  var angle = parseFloat(startAngle)
  var index = 0
  var character
  ctx.save()
  ctx.fillStyle = s.fontColor
  ctx.font = 'bold 15px 宋体'
  ctx.textAlign = 'right'
  ctx.textBaseline = 'middle'
  while (index < string.length) {
    character = string.charAt(index)
    ctx.save()
    ctx.beginPath()
    ctx.translate(s.x + Math.cos(angle) * radius,
      s.y + Math.sin(angle) * radius)
    ctx.rotate(Math.PI / 1.9 + angle)
    // 文字调转180度
    ctx.rotate(Math.PI)
    ctx.fillText(character, 0, 0)
    angle -= angleDecrement
    index++
    ctx.restore()
  }
  ctx.restore()
}

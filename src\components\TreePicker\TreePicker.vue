<template>
  <div>
    <el-popover
      ref="popover"
      v-model="visible"
      :placement="placement"
      trigger="manual"
      @click-out-side="visible=false"
      @after-enter="handleAfterEnter"
      @after-leave="handleAfterLeave"
      @show="handleShow"
    >
      <el-input
        v-if="filterable"
        ref="search"
        v-model="searchValue"
        v-bind="searchOption"
        :placeholder="$t('common.filiter')"
        suffix-icon="el-icon-search"
        clearable
      />
      <div class="tree-container" :style="{ width: minWidth, minWidth: '274px', maxWidth: maxWidth }">
        <el-scrollbar
          ref="treeScroll"
          class="jp-scrollbar"
          wrap-class="jp-scrollbar__wrap"
        >
          <el-tree
            ref="tree"
            v-bind="treeOption"
            :default-expanded-keys="defaultExpandedKeys"
            :load="loadNode"
            :filter-node-method="filterNode"
            @node-click="onTreeNodeClick"
            @check="onTreeCheck"
            @check-change="onTreeCheckChange"
          >
            <template v-slot:default="{ node, data }">
              <span
                class="el-tree-node__label"
                :class="{'el-tree-node__disabled': data.disabled}"
              >{{ node.label }}</span>
            </template>
          </el-tree>
        </el-scrollbar>
      </div>
    </el-popover>
    <div v-popover:popover @click="togglePopover">
      <div
        v-if="multiple && !useWrapper"
        ref="tags"
        class="el-select__tags"
        :style="{ 'max-width': minWidth - 32 + 'px', width: '100%' }"
      >
        <span v-if="choiceLabels.length">
          <el-tag
            size="mini"
            type="info"
          >
            <span class="el-select__tags-text">{{ choiceLabels[0] }}</span>
          </el-tag>
          <el-tag
            v-if="choiceLabels.length > 1"
            size="mini"
            type="info"
          >
            <span class="el-select__tags-text">+ {{ choiceLabels.length - 1 }}</span>
          </el-tag>
        </span>
      </div>
      <el-input
        v-show="!useWrapper"
        ref="input"
        v-model="choiceLabel"
        class="picker-input"
        :class="{ 'is-focus': visible }"
        :disabled="disabled"
        :readonly="readonly"
        :placeholder="inputPlaceholder"
      />
      <slot v-show="useWrapper" name="wrapper" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'TreePicker',
  props: {
    value: {
      type: [String, Array],
      default: ''
    },
    valueLabel: {
      type: [String, Array],
      default: ''
    },
    placement: {
      type: String,
      default: 'bottom-start'
    },
    width: {
      type: [String, Number],
      default: 'auto'
    },
    maxWidth: {
      type: [String, Number],
      default: 'undefined'
    },
    searchOption: {
      type: Object,
      default: () => {
      }
    },
    treeOption: {
      type: Object,
      default: () => {
      }
    },
    filterable: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    leafOnly: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: ''
    },
    useWrapper: {
      type: Boolean,
      default: false
    },
    expandLevel: {
      type: Number,
      default: undefined
    }
  },
  data() {
    return {
      visible: false,
      searchValue: '',
      choiceLabels: [],
      choiceLabel: '',
      minWidth: '',
      defaultExpandedKeys: [],
      checkKeys: [],
      visibleKeys: []
    }
  },
  computed: {
    multiple() {
      return this.treeOption['showCheckbox'] === true
    },
    lazy() {
      return !!this.treeOption['lazy']
    },
    labelKey() {
      return this.treeOption?.props?.label || 'name'
    },
    idKey() {
      return this.treeOption?.nodeKey || 'id'
    },
    inputPlaceholder() {
      let placeholder
      if (this.choiceLabels && this.choiceLabels.length > 0) {
        placeholder = ''
      } else {
        placeholder = this.placeholder
      }
      return placeholder
    }
  },
  watch: {
    expandLevel(val) {
      this.setExpandLevel(val)
    },
    value(val) {
      this.setChoiceLabels(val)
    },
    visible(val) {
      if (val) {
        if (!this.useWrapper) {
          this.minWidth = this.$refs.input.$el.getBoundingClientRect().width - 24 + 'px'
        } else {
          this.minWidth = this.width + 'px'
        }
        if (this.filterable) {
          this.visibleKeys = []
          this.$refs.search.focus()
        }
      }
    },
    searchValue(val) {
      this.filterText(val)
    }
  },
  methods: {
    handleAfterEnter() {
      const treeScroll = this.$refs.treeScroll
      if (treeScroll) {
        treeScroll.wrap.scrollTop = 0
        treeScroll.moveY = 0
        // treeScroll.sizeHeight = '0'
        // treeScroll.handleScroll()
      }
      this.$emit('after-enter')
    },
    handleAfterLeave() {
      this.$emit('after-leave')
    },
    handleShow() {
      this.$emit('show')
    },
    setExpandLevel(expandLevel = 0) {
      if (this.$refs.tree) {
        const allNode = this.$refs.tree.store._getAllNodes()
        const expandNodes = []
        allNode.forEach(node => {
          const level = node.level
          if (level < expandLevel) {
            expandNodes.push(node)
          }
          if (level >= expandLevel) {
            node.expanded = false
          }
        })
        this.defaultExpandedKeys = expandNodes
      }
    },
    setChoiceLabels(nodeKey) {
      if (this.$refs.tree) {
        if (!nodeKey) {
          nodeKey = []
        }
        this.$nextTick(() => {
          if (this.multiple) {
            this.checkKeys = nodeKey
            this.$refs.tree.setCheckedKeys(nodeKey, this.leafOnly, false)
            const result = []
            nodeKey.forEach(key => {
              if (key != null) {
                const node = this.$refs.tree.getNode(key)
                if (node != null) {
                  result.push(node['label'])
                }
              }
            })
            this.choiceLabels = result
          } else {
            this.$refs.tree.setChecked(nodeKey, true, false)
            const node = this.$refs.tree.getNode(nodeKey)
            if (node != null) {
              this.choiceLabel = node['label']
            }
          }
        })
      } else {
        this.choiceLabel = ''
        this.choiceLabels = []
      }
    },
    filterText(val) {
      const tree = this.$refs.tree
      tree.filter(val)
      this.$nextTick(() => {
        this.visibleKeys = this.getVisibleKeys(this.leafOnly)
        this.checkKeys.forEach(key => {
          const node = tree.getNode(key)
          tree.setChecked(key, true, true)
          this.onTreeCheckChange(node.parent.data, false, true)
        })
      })
    },
    filterNode(value, data) {
      if (!value) {
        return true
      }
      return data[this.labelKey].toUpperCase().indexOf(value.toUpperCase()) !== -1
    },
    loadNode(node, resolve) {
      if (this.lazy) {
        const treeData = this.treeOption['lazyData']
        // const label = this.treeOption?.props?.label || 'name'
        // const children = this.treeOption?.props?.children || 'children'
        // const nodeKey = this.treeOption?.nodeKey || 'id'
        const levelData = []
        if (node.level === 0) {
          treeData.forEach(item => {
            levelData.push({
              [this.idKey]: item[this.idKey],
              [this.labelKey]: item[this.labelKey]
            })
          })
          return resolve(levelData)
        } else {
          return resolve(levelData)
        }
      }
    },
    onTreeCheckChange(nodeData, checked, childChecked) {
      if (this.multiple) {
        const currentNode = this.getNode(nodeData)
        if (currentNode) {
          if (currentNode.visible && currentNode.isLeaf && checked) {
            this.onTreeCheckChange(currentNode.parent.data, false, true)
          }
        }
        this.$nextTick(() => {
          if (currentNode) {
            if (childChecked) {
              let allCheck = true
              for (const childNode of currentNode.childNodes) {
                if (childNode.visible) {
                  allCheck = childNode.checked
                  if (!allCheck) {
                    break
                  }
                }
              }
              if (allCheck) {
                if (!currentNode.checked) {
                  this.$refs.tree.setChecked(currentNode, true, false)
                }
                if (currentNode.parent) {
                  this.onTreeCheckChange(currentNode.parent.data, false, true)
                }
              }
            }
          }
        })
      }
    },
    onTreeCheck(nodeData) {
      this.handleCheck(nodeData)
    },
    getCheckedKeys() {
      const tree = this.$refs.tree
      const checkKeys = tree.getCheckedKeys(this.leafOnly)
      if (this.filterable) {
        this.visibleKeys.forEach(key => {
          const isChecked = checkKeys.includes(key)
          const i = this.checkKeys.indexOf(key)
          if (isChecked && i === -1) {
            this.checkKeys.push(key)
          }
          if (!isChecked && i !== -1) {
            this.checkKeys.splice(i, 1)
          }
        })
      }
      if ((this.visibleKeys.length === 0 && this.checkKeys.length === 0) || this.searchValue === '') {
        this.checkKeys = checkKeys
      }
    },
    onTreeNodeClick(data, node) {
      if (!node?.disabled) {
        if (this.multiple) {
          this.$refs.tree.setChecked(node, !node.checked, true)
        }
        this.handleCheck(node)
      }
    },
    handleCheck(nodeData) {
      if (this.multiple) {
        this.getCheckedKeys()
        const labels = []
        const keys = []
        this.checkKeys.forEach(key => {
          const node = this.$refs.tree.getNode(key)
          labels.push(node.data[this.labelKey])
          keys.push(node.data[this.idKey])
        })
        this.choiceLabels = labels
        this.$emit('input', keys)
        this.$emit('update:valueLabel', labels)
      } else {
        this.choiceLabel = nodeData['label']
        this.$emit('input', nodeData['key'])
        this.$emit('update:valueLabel', nodeData['label'])
        this.visible = false
      }
    },
    togglePopover() {
      if (!this.disabled) {
        this.visible = !this.visible
        if (this.visible) {
          (this.$refs.input || this.$refs.search).focus()
          this.searchValue = ''
        }
      }
    },
    deleteTag(event, tag) {
      const index = this.choiceLabels.indexOf(tag)
      if (index > -1 && !this.disabled) {
        const value = this.value.slice()
        value.splice(index, 1)
        this.$emit('input', value)
      }
      event.stopPropagation()
    },
    getNode(data) {
      return this.$refs.tree.getNode(data)
    },
    getVisibleKeys(leafOnly = false) {
      const visibleKeys = []
      this.$refs.tree.store._getAllNodes().forEach(node => {
        if (node.visible && leafOnly) {
          visibleKeys.push(node.key)
        }
      })
      return visibleKeys
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~element-ui/packages/theme-chalk/src/common/var.scss";

.tree-container {
  margin-top: 10px;
}

::v-deep {
  .jp-scrollbar {
    .jp-scrollbar__wrap {
      max-height: $--select-dropdown-max-height;
    }
  }

  .el-input {
    &.is-focus .el-input__inner {
      border-color: $--color-primary;
    }

    &.picker-input .el-input__inner {
      cursor: pointer !important;
    }
  }

  .el-select__tags .el-tag {
    margin: 2px 0 2px 6px;
  }

  .el-tree {
    .el-tree-node {
      &.is-expanded {
        .el-tree-node__children {
          display: inline;
        }
      }

      .el-checkbox__inner {
        padding: 0;
        border-radius: 0;

        &:after {
          padding: 0;
        }
      }

      .el-icon-caret-right:before {
        content: "\e791";
        font-size: 12px;
        background-color: unset;
        color: $--color-primary;
        transform: unset;
        font-weight: normal;
        padding: 0;
      }

      .is-leaf.el-icon-caret-right:before {
        color: transparent;
      }

      .el-tree-node__expand-icon {
        color: $--color-primary;

        &.expanded {
          transform: rotate(90deg);
        }

        &.is-leaf {
          color: transparent;
          cursor: default;
        }
      }

      .el-tree-node__content {
        height: 26px;
        border: 0;

        &:hover {
          background-color: #eaf5ff
        }
      }

      .el-tree-node__disabled {
        color: $--color-text-placeholder;
        cursor: not-allowed;
      }
    }
  }
}
</style>

<template>
  <div class="design-center">
    <div class="template-render-wrapper">
      <template-render :render-list="renderList" :product-id="productId" :idis-code="idisCode" />
    </div>
  </div>

</template>
<script>

import Template<PERSON>ender from './template-render.vue'
import accApi from '@/api/acc/acc'
import AMapLoader from '@amap/amap-jsapi-loader'
import RemoGeoLocation from '@/utils/remogeo'

export default {
  name: 'MobilePhone',
  components: { TemplateRender },

  data() {
    return {
      renderList: [],
      id: '',
      productId: '',
      idisCode: ''
    }
  },
  mounted() {
    this.id = this.$route.query.id
    accApi.detailTagDetailApi({ id: this.id, paramCheck: true }).then((res) => {
      this.productId = res.data.productId || ''
      this.idisCode = res.data.idisCode || ''
      accApi.getProductTemplate({ id: res.data.templateId, paramCheck: true }).then(res => {
        this.setConfig(res.data)
      })
      this.saveScanRecord({ templateId: res.data.templateId })
    })
  },
  methods: {
    setConfig(design) {
      const { config } = design
      const _config = JSON.parse(config)
      _config.forEach(item => {
        this.renderList.push(item)
      })
    },
    saveScanRecord({ templateId }) {
      const isWechat = /MicroMessenger/i.test(navigator.userAgent)
      if (isWechat) {
        accApi.getApiTicket({ url: window.location.href, paramCheck: true }).then(res => {
          const config = res.data
          config.jsApiList = ['getLocation']
          wx.config(config)
          wx.ready(() => {
            wx.getLocation({
              type: 'gcj02',
              success: (wData) => {
                AMapLoader.load({
                  key: 'd06ebbe2f13ce56c6d9eb9fab249290b',
                  version: '2.0'
                })
                  .then((AMap) => {
                    AMap.plugin('AMap.Geocoder', () => {
                      const geocoder = new AMap.Geocoder()
                      const loc = [wData.longitude, wData.latitude]
                      geocoder.getAddress(loc, (status, data) => {
                        // console.log('位置信息', data);
                        if (status === 'complete' && data.regeocode) {
                          const address = data.regeocode
                          const comp = address.addressComponent
                          accApi.saveScanRecord({
                            longitude: wData.longitude,
                            latitude: wData.latitude,
                            accessAddress: address.formattedAddress,
                            province: comp.province,
                            city: comp.city || comp.province,
                            district: comp.district,
                            accessTerminal: this.$util.getBrowserType(),
                            codeId: this.id,
                            templateId,
                            paramCheck: true
                          })
                        } else {
                          alert('获取地名信息失败')
                        }
                      })
                    })
                  })
              }
            })
          })
        })
      } else {
        AMapLoader.load({
          key: 'd06ebbe2f13ce56c6d9eb9fab249290b',
          version: '2.0'
        })
          .then((AMap) => {
            AMap.plugin('AMap.Geolocation', () => {
              const geoLocationApi = new AMap.Geolocation({
                enableHighAccuracy: true,
                needAddress: true,
                timeout: 10000
              })
              geoLocationApi.getCurrentPosition((status, result) => {
                console.log('位置信息', result)
                if (status === 'complete' && result.addressComponent) {
                  const comp = result.addressComponent
                  accApi.saveScanRecord({
                    longitude: result.position.lng,
                    latitude: result.position.lat,
                    accessAddress: result.formattedAddress,
                    province: comp.province,
                    city: comp.city || comp.province,
                    district: comp.district,
                    accessTerminal: this.$util.getBrowserType(),
                    codeId: this.id,
                    templateId,
                    paramCheck: true
                  })
                } else {
                  alert('地图定位失败')
                }
              })
            })
          })
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .template-render{
  padding: 0 !important;
  height: auto !important;
  width: 100%;
}::v-deep .phone{
   border-radius: 0px !important;
 }::v-deep .el-dialog__wrapper{
   padding-left: 0 !important;
 }

.template-render-wrapper {

  flex-shrink: 0;
  background-size: contain;
  //height: 750px;
  //width: 360px;
  //padding: 40px 13px 20px 13px
}
</style>
<style  lang="scss">

body {
  padding-right: 0 !important;
}

</style>


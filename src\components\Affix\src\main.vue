<template>
  <div>
    <div ref="point" :class="classes" :style="styles">
      <slot />
    </div>
    <div v-show="slot" :style="slotStyle" />
  </div>
</template>

<script>
import { on, off } from '@/utils/dom'
const prefixCls = 'ivu-affix'

function getScroll(target, top) {
  const prop = top ? 'pageYOffset' : 'pageXOffset'
  const method = top ? 'scrollTop' : 'scrollLeft'

  let ret = target[prop]
  if (typeof ret !== 'number') {
    ret = target[method]
  }

  return ret
}

function getOffset(element, wrapper) {
  const rect = element.getBoundingClientRect()
  const scrollTop = getScroll(wrapper, true)
  const scrollLeft = getScroll(wrapper)

  // const docEl = window.document.body
  const clientTop = wrapper.clientTop || 0
  const clientLeft = wrapper.clientLeft || 0

  return {
    top: rect.top + scrollTop - clientTop,
    left: rect.left + scrollLeft - clientLeft
  }
}
export default {
  name: 'ElAffix',
  props: {
    offsetTop: {
      type: Number,
      default: 0
    },
    offsetBottom: {
      type: Number,
      default: undefined
    },
    useCapture: {
      type: Boolean,
      default: false
    },
    wrapperClass: {
      type: String,
      default: 'body'
    },
    mainWrapperClass: {
      type: String,
      default: 'app-main'
    },
    minTop: {
      type: Number,
      default: 0
    },
    fixLeft: {
      type: Number,
      default: 0
    },
    fixWidth: {
      type: Number,
      default: 0
    },
    fixStyle: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      affix: false,
      styles: {},
      slot: false,
      slotStyle: {}
    }
  },
  computed: {
    offsetType() {
      let type = 'top'
      if (this.offsetBottom >= 0) {
        type = 'bottom'
      }

      return type
    },
    classes() {
      return [
        {
          [`${prefixCls}`]: this.affix
        }
      ]
    }
  },
  mounted() {
    // window.addEventListener('scroll', this.handleScroll, false);
    // window.addEventListener('resize', this.handleScroll, false);
    const wrapper = this.getWrapper()
    on(wrapper, 'scroll', this.handleScroll, this.useCapture)
    // on(wrapper, 'resize', this.handleScroll, this.useCapture)
    // addResizeListener(wrapper, this.debouncedHandleScroll)

    const mainWrapper = this.getMainWrapper()
    on(mainWrapper, 'scroll', this.handleScroll, this.useCapture)
    this.$nextTick(() => {
      this.handleScroll()
    })
  },
  beforeDestroy() {
    // window.removeEventListener('scroll', this.handleScroll, false);
    // window.removeEventListener('resize', this.handleScroll, false);
    const wrapper = this.getWrapper()
    off(wrapper, 'scroll', this.handleScroll, this.useCapture)
    // off(wrapper, 'resize', this.handleScroll, this.useCapture)
    // removeResizeListener(wrapper, this.debouncedHandleScroll)
    const mainWrapper = this.getMainWrapper()
    off(mainWrapper, 'scroll', this.handleScroll, this.useCapture)
  },
  methods: {
    getWrapper() {
      return document.querySelector(`.${this.wrapperClass}`) || window
    },
    getMainWrapper() {
      return document.querySelector(`.${this.mainWrapperClass}`) || window
    },
    handleScroll() {
      const affix = this.affix
      const wrapper = this.getWrapper()
      const mainWrapper = this.getMainWrapper()
      const mainScrollTop = getScroll(mainWrapper, true)
      const scrollTop = getScroll(wrapper, true)
      const elOffset = getOffset(this.$el, wrapper)
      const windowHeight = wrapper.innerHeight
      const elHeight = this.$el.getElementsByTagName('div')[0].offsetHeight
      // Fixed Top
      // console.log(`top:${(elOffset.top - this.offsetTop)}, scrollTope:${scrollTop}`)
      const top = this.offsetTop - mainScrollTop >= this.minTop ? this.offsetTop - mainScrollTop : this.minTop
      if ((elOffset.top - top) < scrollTop && scrollTop > 0 && this.offsetType === 'top') {
        this.affix = true
        this.slotStyle = {
          width: this.$refs.point.clientWidth + 'px',
          height: this.$refs.point.clientHeight + 'px'
        }
        this.slot = true
        const baseStyles = {
          top: `${top}px`,
          left: `${elOffset.left + this.fixLeft}px`,
          width: `${this.$el.offsetWidth + this.fixWidth}px`,
          maxWidth: `${wrapper.clientWidth}px`
        }
        this.styles = Object.assign(baseStyles, this.fixStyle)

        this.$emit('on-change', true)
      } else if ((elOffset.top - top) > scrollTop && this.offsetType === 'top') {
        this.slot = false
        this.slotStyle = {}
        this.affix = false
        this.styles = null

        this.$emit('on-change', false)
      }

      // Fixed Bottom
      if ((elOffset.top + this.offsetBottom + elHeight) > (scrollTop + windowHeight) && this.offsetType === 'bottom' && !affix) {
        this.affix = true
        this.styles = {
          bottom: `${this.offsetBottom}px`,
          left: `${elOffset.left}px`,
          width: `${this.$el.offsetWidth}px`
        }

        this.$emit('on-change', true)
      } else if ((elOffset.top + this.offsetBottom + elHeight) < (scrollTop + windowHeight) && this.offsetType === 'bottom' && affix) {
        this.affix = false
        this.styles = null

        this.$emit('on-change', false)
      }
    }
  }
}
</script>

<style scoped>
.ivu-affix {
  position: fixed;
  z-index: 10;
  transition-property: left, width, max-width;
  transition-duration: 0.28s, 0.28s, 0.28s;
}
</style>


const callback = (el, binding) => {
  const { value, modifiers } = binding
  if (value === true || value === undefined) {
    const mask = document.createElement('div')
    el.style.position = 'relative'
    mask.style.width = '100%'
    mask.style.height = '100%'
    mask.style.position = 'absolute'
    mask.style.top = 0
    mask.style.left = 0
    mask.style.zIndex = modifiers.top ? 9999 : 1
    el.appendChild(mask)
  }
}

export default callback

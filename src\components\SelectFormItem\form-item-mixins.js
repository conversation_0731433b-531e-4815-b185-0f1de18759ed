export default {
  name: 'form-item-mixins',
  props: {
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    auto: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    isSelect: {
      type: <PERSON>olean,
      default: () => {
        return false
      }
    },
    isShowLable: {
      type: <PERSON>olean,
      default: () => {
        return true
      }
    },
    showSpecialSlot: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    selectMultLineCheckBox: {
      type: <PERSON>ole<PERSON>,
      default: () => {
        return false
      }
    }
  },
  data() {
    return {
      initSlotOption: this.$constant.initSlotOption,
      initInputOption: this.$constant.initInputOption,
      initNumberOption: this.$constant.initNumberOption,
      initSelectOption: this.$constant.initSelectOption,
      initDatePickerOption: this.$constant.initDatePickerOption,
      initTimePickerOption: this.$constant.initTimePickerOption,
      initRadioOption: this.$constant.initRadioOption,
      initSwitchOption: this.$constant.initSwitchOption,
      initCascaderOption: this.$constant.initCascaderOption,
      initTimeSelectOption: this.$constant.initTimeSelectOption,
      defaultKeys: this.$constant.defaultKeys
    }
  },
  computed: {
    allSelected() {
      return (options, value) => {
        let selected = false
        if (options.length > 0) {
          selected = !options.some(option => !value?.includes(option.value))
        }
        return selected
      }
    }
  },
  watch: {
    formData: {
      handler(val) {
        const that = this
        Object.keys(val).forEach((key, index, array) => {
          if (val[key].type === 'slot' && that.showSpecialSlot) {
            const cloneItem = that.$util.cloneDeep(val[key])
            const dom = document.querySelector(`#${cloneItem.inputId}`)
            if (dom || dom?.length > 0) {
              dom.value = cloneItem.selectLabels(cloneItem) || ''
            }
            // this.$nextTick(() => {
            //   const validateProp = this.$refs[`elFormItem${key}`]
            //   if (validateProp?.length > 0) {
            //     validateProp.forEach(n => {
            //       n.validate('', undefined, false)
            //     })
            //   } else {
            //     validateProp.validate('', undefined, false)
            //   }
            // })
          }
        })
        this.$emit('get-data', this.getAllParams())
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      const data = this.getDate()
      this.$emit('update:form-data', data)
      this.$emit('update-form-data-init', data)
      this.$emit('get-data', this.getAllParams())
    })
  },
  created() {
  },
  methods: {
    setFormData(val) {
      const data = this.getDate(val)
      this.$emit('update:form-data', data)
      this.$emit('update-form-data-init', data)
      this.$emit('get-data', this.getAllParams())
    },
    setSlotHidden(refName) {
      if (this.$refs[refName][0]) this.$refs[refName][0].visible = false
      else if (this.$refs[refName]) this.$refs[refName].visible = false
    },
    getDate(val) {
      const that = this
      const innerData = {}
      const formData = val || this.formData
      Object.keys(formData).forEach((key, index, array) => {
        const item = formData[key]
        if (typeof item !== 'object') {
          return
        }
        innerData[key] = {}
        const newItem = innerData[key]
        newItem.value = item.value
        newItem.defaultValue = item.value
        if (typeof item.value === 'function' && item.valueKey?.length > 0) {
          item.valueKey.forEach(value => {
            newItem[this.tf(`default-${value}`)] = item[value]
          })
        }
        newItem.show = item.show === null || typeof item.show === 'undefined' ? true : item.show
        newItem.key = key
        newItem.fullWidth = item.fullWidth
        newItem.label = item.label
        newItem.type = item.type
        newItem.rules = item.rules
        Object.keys(item).forEach((key, index, array) => {
          if (that.defaultKeys.indexOf(key) > -1) return
          newItem[key] = item[key]
        })
        if (item.type === 'slot' && !that.showSpecialSlot) {
          newItem.option = Object.assign({}, that.initSlotOption.option, item.option)
          newItem.slotName = item.slotName
          return
        }
        if (item.type === 'input') {
          newItem.option = Object.assign({}, that.initInputOption.option, item.option)
          newItem.event = Object.assign({}, that.initInputOption.event, item.event)
        }
        if (item.type === 'select') {
          newItem.option = Object.assign({}, that.initSelectOption.option, item.option)
          newItem.event = Object.assign({}, that.initSelectOption.event, item.event)
        }
        if (item.type === 'number') {
          newItem.option = Object.assign({}, that.initNumberOption.option, item.option)
          newItem.event = Object.assign({}, that.initNumberOption.event, item.event)
          if (!item.value) newItem.defaultValue = undefined
        }
        if (item.type === 'chosen') {
          newItem.option = Object.assign({}, that.initSelectOption.option, item.option)
          newItem.event = Object.assign({}, that.initSelectOption.event, item.event)
        }
        if (item.type === 'tree') {
          newItem.option = Object.assign({}, item.option)
          newItem.event = Object.assign({}, item.event)
          newItem.treeOption = Object.assign({}, item.treeOption)
        }
        if (item.type === 'slot' && that.showSpecialSlot) {
          newItem.option = Object.assign({}, that.initInputOption.option, item.option)
          newItem.event = Object.assign({}, that.initInputOption.event, item.event)
          newItem.selectLabels = item.selectLabels
          newItem.inputId = that.guid()
          // const cloneItem = that.$util.cloneDeep(newItem)
          // const dom = document.querySelector(`#${newItem.inputId}`)
          // if (dom || dom?.length > 0) dom.value = cloneItem.selectLabels(cloneItem) || ''
        }
        if (item.type === 'date' || item.type === 'j-date') {
          newItem.option = Object.assign({}, that.initDatePickerOption.option, item.option)
          newItem.event = Object.assign({}, that.initDatePickerOption.event, item.event)
        }
        if (item.type === 'switch') {
          newItem.option = Object.assign({}, that.initSwitchOption.option, item.option)
          newItem.event = Object.assign({}, that.initSwitchOption.event, item.event)
        }
        if (item.type === 'time') {
          newItem.option = Object.assign({}, that.initTimePickerOption.option, item.option)
          newItem.event = Object.assign({}, that.initTimePickerOption.event, item.event)
        }
        if (item.type === 'time-select') {
          newItem.option = Object.assign({}, that.initTimeSelectOption.option, item.option)
          newItem.event = Object.assign({}, that.initTimeSelectOption.event, item.event)
        }
        if (item.type === 'cascader' || item.type === 'j-cascader') {
          newItem.option = Object.assign({}, that.initCascaderOption.option, item.option)
          newItem.event = Object.assign({}, that.initCascaderOption.event, item.event)
        }
        if (item.type === 'radio') {
          newItem.option = Object.assign({}, that.initRadioOption.option, item.option)
          newItem.event = Object.assign({}, that.initRadioOption.event, item.event)
          if (newItem.option.radioList) newItem.option.radioList.forEach((value, index, array) => { array[index] = Object.assign({}, that.initRadioOption.initItemRadio, value) })
        }
        if (!item?.option?.placeholder && !item?.option?.disabled) {
          newItem.option.placeholder = `${(newItem.option.placeholder || that.$t('common.enter'))}${newItem.label}`
        }
      })
      return innerData
    },
    guid() {
      return 'zxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0; var v = c === 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
      }).replace(/-/g, '')
    },
    getAllRefs() {
      return this.$refs
    },
    // 查询
    getAllParams() {
      const params = {}
      for (const key in this.formData) {
        const value = this.formData[key].value
        params[key] = typeof value === 'function' ? value(this.formData[key]) : value
      }
      return params
    },
    changeSelect(value, item) {
      if (value.includes('all')) {
        item.value = item.option.selectOptions.map(option => option.value)
        this.$forceUpdate()
      }
      if (typeof item.event.change === 'function') item.event.change(value)
    },
    changeAll(item, allSelected) {
      item.value = allSelected ? [] : item.option.selectOptions.map(option => option.value)
      if (typeof item.event.selectAll === 'function') item.event.selectAll(item.value)
    },
    focus(name) {
      this.$refs[name].focus()
    },
    blur(name) {
      this.$refs[name].focus()
    },
    select(name) {
      this.$refs[name].focus()
    },
    resetAllField() {
      const that = this
      Object.keys(this.formData).forEach((key, index, array) => {
        that.formData[key].value = that.formData[key].defaultValue
        if (typeof that.formData[key].value === 'function' && that.formData[key].valueKey?.length > 0) {
          that.formData[key].valueKey.forEach(value => {
            that.formData[key][value] = that.formData[key][this.tf(`default-${value}`)]
          })
        }
      })
    },
    tf(str) {
      var re = /-(\w)/g
      str = str.replace(re, function($0, $1) {
        return $1.toUpperCase()
      })
      return str
    },
    show() {
      this.$emit('show')
    },
    afterEnter() {
      this.$emit('after-enter')
    },
    afterLeave() {
      this.$emit('after-leave')
    },
    doClose(name) {
      for (const index in this.formData) {
        const item = this.formData[index]
        if (name && name !== ('fadeInLinearFormItemRef' + index)) continue
        if (item.type === 'slot' && (this.showSpecialSlot && !item.notPop)) {
          const slot = this.$refs['fadeInLinearFormItemRef' + index]
          if (slot?.length > 0) { this.$refs['fadeInLinearFormItemRef' + index][0].doClose() } else if (slot) this.$refs['fadeInLinearFormItemRef' + index].doClose()
        }
      }
    },
    valiFormItem(name) {
      const vali = this.$refs[`elFormItem${name}`]
      if (vali?.length > 0) vali.forEach(n => { n.validate('', undefined, true) })
      else vali.validate('', undefined, true)
    },
    initFormItemData() {
      for (const key in this.formData) {
        const item = this.formData[key]
        if (item.type === 'j-cascader') {
          if (item.option?.props?.lazy && item.option?.props?.multiple) {
            this.$refs[key][0].initCascaderOptions()
          }
        }
      }
    }
  }
}

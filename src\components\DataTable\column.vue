<template>
  <el-table-column
    v-if="item.show"
    :key="item.key?item.key:index"
    v-bind="item"
  >
    <template slot="header" slot-scope="scope">
      <span class="el-table-column-header" :title="scope.column.label" v-html="scope.column.label" />
    </template>
    <template v-if="item.children &&item.children.length>0">
      <data-column
        v-for="(itemColumn,$index) in item.children"
        :key="$index"
        :index="$index"
        :item="itemColumn"
      >
        <template slot-scope="scope">
          <slot
            :data="scopeDate(itemColumn,scope.data.row,scope.data.$index)"
          />
        </template>
      </data-column>
    </template>
    <slot slot-scope="scope" :data="scopeDate(item,scope.row,scope.$index)" />
  </el-table-column>
</template>

<script>
export default {
  name: 'DataColumn',
  props: {
    item: {
      type: Object,
      default: () => {
        return {}
      }
    },
    index: {
      type: Number,
      default: 0
    }
  },
  computed: {
    scopeDate() {
      return (item, row, $index) => {
        return { item: item, row: row, $index: $index }
      }
    }
  }
}
</script>

<style>
.is-sortable .el-table-column-header{
  display: inline-flex;
  max-width: calc(100% - 25px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
.el-table-column-header{
  display: inline-flex;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
</style>

import store from '../store'
/**
 * 简单深拷贝
 * @param target {object | array | null}  深拷贝的原对象
 * @retrun result {object | array | null} 深拷贝之后的对象
 */
export const deepClone = (target) => {
  // 定义一个变量
  let result
  // 如果当前需要深拷贝的是一个对象的话
  if (typeof target === `object`) {
    // 如果是一个数组的话
    if (Array.isArray(target)) {
      result = [] // 将result赋值为一个数组，并且执行遍历
      for (const i in target) {
        // 递归克隆数组中的每一项
        result.push(deepClone(target[i]))
      }
      // 判断如果当前的值是null的话；直接赋值为null
    } else if (target === null) {
      result = null
      // 判断如果当前的值是一个RegExp对象的话，直接赋值
    } else if (target.constructor === RegExp) {
      result = target
    } else {
      // 否则是普通对象，直接for in循环，递归赋值对象的所有值
      result = {}
      for (const i in target) {
        result[i] = deepClone(target[i])
      }
    }
    // 如果不是对象的话，就是基本数据类型，那么直接赋值
  } else {
    result = target
  }
  // 返回最终结果
  return result
}
// this.getCodeName('approval_progress', '123')
export const getCodeName = (codeTyeName, code) => {
  if (codeTyeName && code) {
    const list = store.getters.dictionaryList[codeTyeName] || []
    const name = list.find(e => e.value === code)?.label
    return name || code
  }
}
export const downloadFile = (id, name, url) => {
  if (!id) return
  const link = document.createElement(`a`) // 创建a标签
  link.style.display = `none` // 使其隐藏
  link.href = url || `/api/inner/acc/file/downloadFile/${id}` // 赋予文件下载地址
  console.log(111, link.href)
  link.setAttribute(`download`, name) // 设置下载属性 以及文件名
  document.body.appendChild(link) // a标签插至页面中
  link.click() // 强制触发a标签事件
  document.body.removeChild(link)
}

<template>
  <div class="login-container">
    <div class="login-box">
      <div  style="font-size: 30px;line-height:3vh;position: absolute;top: -70px;color: #fff;text-align: center;width: 100%">
        <div >中药行业工业互联网标识应用平台</div>
      </div>
      <div class="login-left">

        <div class="title-container">
          <div style="font-size: 26px;line-height:3vh">{{ $t("common.system") }}</div>
        </div>

        <el-form ref="loginForm" size="large" :model="loginForm">
          <el-form-item
            prop="username"
            :rules="[
              { required: true, trigger: 'blur', message: '请输入账号' }
            ]"
          >
            <el-input v-model="loginForm.username" placeholder="请输入账号/用户名">
              <span slot="prefix"   class="el-icon-user" />
            </el-input>
          </el-form-item>
          <el-form-item
            style="margin-top: 20px"
            prop="password"
            :rules="[
              { required: true, trigger: 'blur', message:'请输入密码' }
            ]"
          >
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="请输入密码"
              name="password"
              auto-complete="on"
              @keyup.enter.native="handleLogin"
            >
              <span slot="prefix"   :class="passwordType?'el-icon-lock':'el-icon-unlock'" @click="showPwd()" />
              <span slot="suffix"> <img
                  class="svg-img"
                  @click="showPwd()"
                  :src="require(`@/assets/icon/${passwordType ? 'openclose.png' : 'eyeopen.png'}`)"
                  alt
              ></span>
            </el-input>

          </el-form-item>
        </el-form>
        <div class="login-btn" @click="handleLogin">
          <span>{{ $t("common.login") }}</span>
        </div>
        <div class="register-btn" @click="handleRegister">
          <span>立即注册</span>
        </div>

        <div id="login_container" style="margin:auto;width: 65%" />

      </div>
    </div>
  </div>
</template>

<script>
import { getLang, setLang } from '@/utils/auth'
import { mapActions } from 'vuex'

export default {
  name: 'Login',
  data() {
    return {
      isQr: false,
      lang: getLang() || 'zh',
      loginForm: {
        username: '',
        password: ''
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined
    }
  },
  computed: {
  },
  watch: {
  },
  async mounted() {
  },
  methods: {
    ...mapActions([`getAllDictList`]),
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          const password = this.$util.MD5(this.loginForm.password)
          const data = {
            username: this.loginForm.username,
            password: password
          }
          // return
          this.$store
            .dispatch('LoginByUsername', data)
            .then(() => {
              this.loading = false
              this.$destroy()
              this.$store.dispatch('app/setCurrentMenuPath', '/accPortal')
              if (this.$store.getters.token) {
                // this.getAllDictList()
              }
              this.$router.push({ path: '/dashboard/dashboard' })
            })
            .catch(() => {
              this.loading = false
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    switchLang(val) {
      this.$refs['loginForm'].clearValidate()
      this.$i18n.locale = val // 此处val为 zh 或者 en
      setLang(val)
    },
    handleRegister() {
      this.$router.push('/register')
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

// @supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
//   .login-container .el-input input {

//   }
// }

/* reset element-ui css */
.login-container {

  .el-input {
   border: 0px;
    input {

      height: 50px;
      background: #ffffff;

      -webkit-appearance: none;
      border-radius: 0px;
      padding: 0px 5px 0px 10px;
      color: #313131;
    }
    :-webkit-autofill,
    :-webkit-autofill:hover,
    :-webkit-autofill:focus,
    :-webkit-autofill:active {
      // 回填文字颜色设置
      -webkit-text-fill-color: #313131 !important;
      transition: background-color 5000s ease-in-out 0s;
    }
  }
 .el-input__inner {
    border: none;
    border-bottom: 1px solid #DCDFE6;
    border-radius: 0;}
  .el-input__prefix{
   top: 6px;
    left: -2px;
    font-size: 25px;color: black;
  }
  .el-input__suffix{
   top: 6px;
  }
  .el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding: 0;
    top: auto;
    position: absolute;
    left: 10px !important;
    bottom: -20px !important;
  }
}
</style>

<style lang="scss" scoped>
@import "~element-ui/packages/theme-chalk/src/common/var.scss";
@import "../../styles/variables";
.el-carousel {
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
}
.login-container {
  min-height: 100%;
  width: 100%;
  overflow: hidden;
  background: #f5f5f5 url("../../assets/404_images/login-bg.png")  no-repeat fixed center center / cover;
  .login-box {
    width: 452px;
    height: 408px;
    position: absolute;
    right: 8%;
    border-radius: 25px;
    top: 50%;
    transform: translate(0%, -50%);
    box-shadow: -2px 2px 5px rgba(0, 0, 0, 0.13),
      2px 2px 5px rgba(0, 0, 0, 0.13);
  }

  .login-left {
    width: 100%;
    height: 100%;
    float: left;
    padding:0 50px;
    display: inline-block;
    background-color: #fff;
    position: relative;
    border-radius: 25px;

    .qr-login-switch {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 0;
      width: 80px;
      height: 80px;
      opacity: .6;
      transition: all .3s cubic-bezier(.645,.045,.355,1);
      border-color: $primary-color $primary-color rgba(0,0,0,0) rgba(0,0,0,0);
      border-style: solid;
      border-width: 40px;
      background-size: cover;
      border-top-right-radius: 25px;
      box-sizing: border-box;
      &:hover {
        opacity: 1;
      }
      & .anticon{
        position: absolute;
        font-size: 48px;
        top:-24px;
        right:-24px;
        color: white;
      }
    }
    .svg-img {
      width: 24px;
      height: 24px;
      vertical-align: middle;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .title-container {
      width: 65%;
      height: 90px;
      text-align: center;
      color: #1770B5;
      padding-top: 10%;
      line-height: 5vh;
      font-size: 3vh;
      background-size: 30px;
      // text-indent: 20%;
      margin: 0px auto;
      margin-bottom: 20px;
    }
    .el-form-item {
      margin-bottom: 0;
    }
    .input-box {
     border: 1px solid #dcdcdc;
    background: #ffffff;
    border-radius: 5px;
    color: #313131;
  margin: 0 0 12px 17%;
    width: 65%;
    }
  }

  .tips {
    font-size: 12px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding-left: 10px;
    vertical-align: middle;
    width: 30px;
    display: inline-block;

  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    cursor: pointer;
    user-select: none;
  }

  .login-btn {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translate(-50%,-50%);
    width: calc(100% - 100px);
    height: 40px;
    line-height: 40px;
    font-size: 20px;
    color: #fff;
    cursor: pointer;
    background: linear-gradient(90deg, #62F8FF 0%, #0149A0 100%);
    text-align: center;
    border-radius: 5px;
  }
  .register-btn {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translate(-50%,-50%);
    text-decoration: underline;
    cursor: pointer;
    font-size: 14px;
    color: #004DA1;
  }
  .lang-box {
    margin: 20px 0 0 20%;
  }
}
.el-form-item + .el-form-item {
  margin-top: 10px;
}
.touchKey {
  display: inline-block;
  width: calc(100% - 30px);
  position: relative;
}
.touchKetchIcon {
  position: absolute;
  bottom: 3px;
  cursor: pointer;
  right: 6%;
  i {
    font-size: 25px;
    color: #8a8989;
  }
}

</style>

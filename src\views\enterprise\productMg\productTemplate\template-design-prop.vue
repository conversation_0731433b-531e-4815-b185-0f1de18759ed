<template>
  <div class="template-design-prop">
    <el-tabs value="first">
      <el-tab-pane label="属性配置" name="first" />
    </el-tabs>
    <div class="config-wrapper">
      <el-form label-position="left" label-width="80px">
        <el-alert v-if="Object.keys(propConfig).length === 0" type="warning" :closable="false"> 没有属性需要设置</el-alert>
        <template v-else>
          <div v-for="(config, propName) in propConfig" :key="propName">
            <el-form-item :label="config.label" prop="propName">
              <el-input v-if="config.type === 'input'" v-model="active.renderProps[propName]" v-bind="config.bind" />
              <upload-img v-else-if="config.type === 'upload-img'" :img-list.sync="active.renderProps[propName]" v-bind="config.bind" />
              <el-color-picker v-else-if="config.type === 'color-picker'" v-model="active.renderProps[propName]" v-bind="config.bind" />
              <el-switch v-else-if="config.type === 'switch'" v-model="active.renderProps[propName]" v-bind="config.bind" />
              <el-select v-else-if="config.type === 'select'" v-model="active.renderProps[propName]" v-bind="config.bind">
                <el-option v-for="item in config.bind.options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
              <div v-else-if="config.type === 'grid'">
                <el-button-group>
                  <el-button :type="getStyleName(propName) === 0 ? 'primary' : 'default'" @click="changeStyle(0, active.renderProps[propName])">样式1</el-button>
                  <el-button :type="getStyleName(propName) === 1 ? 'primary' : 'default'" @click="changeStyle(1, active.renderProps[propName])">样式2</el-button>
                  <el-button :type="getStyleName(propName) === 2 ? 'primary' : 'default'" @click="changeStyle(2, active.renderProps[propName])">样式3</el-button>
                  <el-button :type="getStyleName(propName) === 3 ? 'primary' : 'default'" @click="changeStyle(3, active.renderProps[propName])">样式4</el-button>
                </el-button-group>
                <template v-for="(prop, index) in active.renderProps[propName]">
                  <div :key="`${index}${prop.size}`" class="grid-col-wrapper">
                    <h3 class="grid-col-title">
                      <span>栏目{{ index+1 }}</span>
                      <span>尺寸:{{ prop.size }}</span>
                    </h3>
                    <el-form-item prop="title" label="标题"><el-input v-model="prop.title" /></el-form-item>
                    <el-form-item prop="url" label="链接"><el-input v-model="prop.url" /></el-form-item>
                    <el-form-item prop="isQrCode" label="是否以二维码展示链接">
                      <el-switch
                        v-model="prop.isQrCode"
                        active-text="是"
                        inactive-text="否"
                      />
                    </el-form-item>
                    <el-form-item prop="textColor" label="文字颜色"><el-color-picker v-model="prop.textColor" /></el-form-item>
                    <el-form-item prop="icon" label="图标">
                      <icon-picker v-model="prop.icon" />
                    </el-form-item>
                    <el-form-item prop="bgType" label="背景类型">
                      <el-switch
                        v-model="prop.bgType"
                        active-text="颜色"
                        inactive-text="图片"
                      />
                    </el-form-item>
                    <el-form-item v-if="prop.bgType" prop="bgColor" label="背景颜色">
                      <el-color-picker v-model="prop.bgColor" />
                    </el-form-item>
                    <el-form-item v-else prop="bgImg" label="背景图片">
                      <upload-img v-bind="config.bind" :img-list.sync="prop.bgImg" />
                    </el-form-item>
                  </div>
                </template>
              </div>
            </el-form-item></div>
        </template>
      </el-form>
    </div>
  </div>
</template>

<script>
import { containerConfigBinds, genGridStyleProps } from './template-design'
import UploadImg from '@/components/DataDialog/uploadImg.vue'
import IconPicker from '@/components/IconPicker/icon-picker.vue'
export default {
  name: 'TemplateDesignProp',
  components: { IconPicker, UploadImg },
  inject: ['templateDesign'],
  data() {
    return {
      active: this.templateDesign.active
    }
  },
  computed: {
    propConfig() {
      const { type } = this.active
      if (!type) {
        console.log(1)
        return {}
      }
      console.log(2)
      return containerConfigBinds[type]
    }
  },
  mounted() {
  },
  methods: {
    getStyleName(propName) {
      const cols = this.active.renderProps[propName]
      if (cols[0].size === 3) {
        return 3
      } else if (cols[0].size === 2) {
        return 2
      } else if (cols[0].size === 1 && cols.length === 2) {
        return 1
      } else {
        return 0
      }
    },
    changeStyle(styleIndex, data) {
      const dataLength = data.length
      if (styleIndex === 0) {
        for (let i = 0; i < 3; i++) {
          if (dataLength <= i) {
            data.push(genGridStyleProps(1))
          } else {
            data[i].size = 1
          }
        }
      } else if (styleIndex === 1) {
        for (let i = 0; i < 2; i++) {
          if (dataLength <= i) {
            data.push(genGridStyleProps(1))
          }
          data[i].size = i + 1
        }
        data.splice(2, dataLength - 2)
      } else if (styleIndex === 2) {
        for (let i = 0; i < 2; i++) {
          if (dataLength <= i) {
            data.push(genGridStyleProps(1))
          }
          data[i].size = 2 - i
        }
        data.splice(2, dataLength - 2)
      } else {
        data[0].size = 3
        data.splice(1, dataLength - 1)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.config-wrapper {
  padding: 20px;
}
.grid-col-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
  padding: 0 12.5px;
  background: #f4f7fd;
  font-size: 14px;
  font-weight: 700;
}
</style>

export default {
  // 共通
  common: {
    login: 'Log In',
    logout: 'Log Out',
    changePassword: 'change password',
    system: 'System',
    welcome: 'Thanks for using System',
    query: 'Query',
    reset: 'Reset',
    create: 'Create',
    add: 'Add',
    edit: 'Edit',
    view: 'View',
    delete: 'Delete',
    expand: 'Expand',
    hide: 'Hide',
    cancel: 'Cancel',
    close: 'Close',
    ok: 'OK',
    prompt: 'Prompt',
    operation: 'Operation',
    enter: 'Please Enter ',
    select: 'Please Select ',
    upload: 'Please Upload ',
    enable: '启用',
    unenable: '禁用',
    back: 'Back',
    save: 'Save',
    append: '添加',
    operationSuccess: '操作成功',
    saveSuccess: '保存成功',
    addSuccess: 'Adding Successful',
    addFail: 'Adding Failed',
    editSuccess: 'Editing Successful',
    editFail: 'Editing Failed',
    delSuccess: 'Deletion Successful',
    delFail: 'Deletion Failed',
    confirm: '请确认是否',
    format: '格式不正确',
    record: 'Please Select a Record',
    confirmDel: 'Do you want to delete this record?',
    isEnable: '是否生效',
    remark: '备注',
    updateUser: '最后修改人',
    updateTime: '最后修改时间',
    imgLimit: '图片大小不能超过4M',
    imgFileType: '只能上传png,jpg,mp4格式的文件',
    allSelect: '全选',
    exportExcel: '导出Excel',
    excel: '导出',
    display: '显示',
    hidden: '隐藏',
    startDate: '开始日期',
    endDate: '结束日期',
    startTime: '开始时间',
    endTime: '结束时间',
    yes: '是',
    no: '否',
    chooseNotExit: '选择的记录不存在',
    chooseDisplaySign: '请选择显示标识',
    long: '长度在',
    to: '到',
    charNo: '个字符',
    error: '错误',
    staging: '暂存',
    Monday: '周一',
    Tuesday: '周二',
    Wednesday: '周三',
    Thursday: '周四',
    Friday: '周五',
    Saturday: '周六',
    Sunday: '周日',
    emptyText: '暂无数据',
    sumText: '合计',
    moreOperations: '更多操作',
    loading: '加载中',
    noMatchText: '无匹配数据',
    all: '全部',
    advancedFilter: '高级筛选',
    screeningConditions: '清除筛选条件'
  },

  home: {
    account: '请输入账号/用户名',
    pwd: '请输入登录密码'
  },
  acc: {
    basicMaterial: {
      materialCode: '原物料编码',
      materialTypeCode: '类别',
      recipeUnitCode: 'Recipe单位',
      materialName: '原物料名称',
      checkUnitCode: '盘点单位',
      minPrice: '最小价格',
      maxPrice: '最大价格',
      price: '价格'
    }
  },
  // common
  portal: {
    // 商户中心
    merchant: {
      merchantCode: '商户号',
      fullName: '商户全称',
      simpleName: '商户简称',
      categoryFlg: '商户类别',
      tenantcode: '数据库连接编码',
      registerAddress: '商户注册地址',
      licenseNumber: '商户营业执照号码',
      businessTerm: '商户营业期限',
      businessScope: '商户营业范围',
      status: '商户状态',
      filePath: '营业执照图片',
      admin: '管理员账户',
      user: '商户用户',
      auth: '产品授权',
      view: '费用查看',
      resetPwd: '重置密码',
      adminName: '管理员姓名',
      userAccount: '管理员账号',
      userPassword: '管理员密码',
      telephone: '管理员手机号码',
      email: '管理员邮箱',
      cardId: '管理员身份证号码',
      idCardExpiresEnd: '管理员身份证有效期止',
      idCardPhotoFront: '管理员身份证照片正',
      idCardPhotoBack: '管理员身份证照片反',
      userName: '用户名称',
      userTelephone: '手机号码',
      userStatus: '商户状态',
      confirmResetPwd: '请确认是否重置密码',
      resetPwdSuccess: '重置密码成功',
      opened: '已开通',
      noOpened: '未开通',
      no: '无',
      noDesc: '暂无简介',
      noOpenDesc: '未开通此应用，无法显示简介',
      version: '版本号',
      versionDesc: '简介',
      open: '开通',
      cancelOpen: '取消开通',
      noVersionWarning: '该产品目前没有版本号',
      authSuccess: '产品授权成功',
      costList: '费用记录',
      transactionDate: '日期',
      transactionType: '变动',
      transactionAmount: '数值',
      recharge: '充值：',
      other: '其他金额',
      selectPay: '请选择支付方式：',
      unionpay: '银联',
      wechat: '微信',
      alipay: '支付宝',
      pay: '立即充值',
      payConfirm: '支付成功',
      selectPayErro: '支付失败请联系管理员'
    },
    // 应用管理
    application: {
      index: '序号',
      applicationName: '应用名称',
      applicationCode: '应用编码',
      sort: '应用排序',
      applicationUrl: '应用图标',
      remark: '应用描述'
    },
    // 菜单元素管理
    menuElement: {
      menuName: '菜单名称',
      menuCode: '菜单编码',
      menuIcon: '菜单图标',
      sort: '排序',
      elementProperty: '菜单标识',
      menuUrl: '路径URL',
      menuType: '菜单元素类型',
      menuDesc: '菜单描述',
      parentMenuCode: '上级菜单',
      isShow: '菜单列是否展示',
      confirm: '该菜单有子级元素，请从子级开始删除',
      btn: '按钮',
      menu: '菜单'
    },
    // 菜单分组管理
    menuGroup: {
      menuGroupCode: '分组编码',
      menuGroupName: '分组名称',
      applicationName: '所属应用',
      updateTime: '更新时间',
      selectList: '选择列表',
      index: '序号',
      menuName: '菜单元素'
    },
    // 产品数据维护
    productData: {
      productName: '产品名称',
      status: '是否生效',
      version: '版本号',
      productDec: '产品描述',
      productMenuGroup: '菜单组',
      paramName: '参数名称',
      paramKey: '参数Key',
      paramValue: '参数Value'
    }
  },

  // 商户中心
  tenant: {
    userManage: {
      index: '序号',
      userName: '用户名称',
      telephone: '手机号码',
      email: '电子邮箱',
      sex: '性别',
      userType: '用户类型',
      areaCode: '区域',
      store: '门店',
      status: '用户状态',
      role: '分配角色',
      resetPwd: '重置密码',
      confirmResetPwd: '请确认是否重置密码',
      resetPwdSuccess: '重置密码成功',
      roleCode: '角色编码',
      roleName: '角色名称',
      roleSuccess: '分配角色成功',
      storeCodeOrName: '门店编码/名称',
      storeCode: '门店编码',
      storeName: '门店名称'
    },

    roleManage: {
      index: '序号',
      roleCode: '角色编码',
      roleName: '角色名称',
      updateTime: '更新时间',
      authMenu: '授权',
      remark: '备注',
      selectList: '选择列表',
      menuName: '菜单元素'

    }
  }
}

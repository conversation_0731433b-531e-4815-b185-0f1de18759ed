<template>
  <div>
    <div class="flex-between transfer-select">
      <el-row :gutter="20">
        <el-col :span="11">
          <data-select
            ref="leftSelectData"
            :is-data-transfer="true"
            :search-data="leftSearchData"
            :rules="leftRules"
            @update:search-data="updateleftSearchData"
            @return-search="leftReturnSearch"
            @return-reset="leftReturnReset"
          >
            <template slot-scope="scope">
              <slot :name="scope.data.slotName" :item="scope.data" />
            </template>
          </data-select>
        </el-col>
        <el-col :span="2"><span v-html="nbsp" /></el-col>
        <el-col :span="11">
          <data-select
            ref="rightSelectData"
            :is-data-transfer="true"
            :search-data="rightSearchData"
            :rules="rightRules"
            @update:search-data="updateRightSearchData"
            @return-search="rightReturnSearch"
            @return-reset="rightReturnReset"
          >
            <template slot-scope="scope">
              <slot :name="scope.data.slotName" :item="scope.data" />
            </template>
          </data-select>
        </el-col>
      </el-row>
    </div>
    <div class="flex-between">
      <el-row :gutter="20" type="flex" justify="center" align="middle">
        <el-col :span="11">
          <data-table
            :table-option="leftInnerTableOption"
            :column="leftInnerColumn"
            :table-data="leftInnerTableData"
            :pagination.sync="leftInnerPagination"
            :auto-reset-pag-num="autoResetPagNum"
            @search-event="leftSearchEvent"
          >
            <template
              v-for="(_, slot) of $scopedSlots"
              v-slot:[slot]="scope"
            >
              <slot
                :name="slot"
                v-bind="scope"
              /></template>
          </data-table>
        </el-col>
        <el-col
          :span="2"
        ><div class="transfer-button">
          <el-button
            icon="el-icon-arrow-right"
            :permission="rightPermission"
            type="primary"
            @click="rightBtnClick"
          /><el-button
            type="primary"
            :permission="leftPermission"
            icon="el-icon-arrow-left"
            @click="leftBtnClick"
          /></div></el-col>
        <el-col
          :span="11"
        ><data-table
          :table-option="rightInnerTableOption"
          :column="rightInnerColumn"
          :table-data="rightInnerTableData"
          :pagination.sync="rightInnerPagination"
          :auto-reset-pag-num="autoResetPagNum"
          @search-event="rightSearchEvent"
        >
          <template
            v-for="(_, slot) of $scopedSlots"
            v-slot:[slot]="scope"
          >
            <slot
              :name="slot"
              v-bind="scope"
            /></template>
        </data-table>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import DataTable from '@/components/DataTable'
import DataSelect from '@/components/DataSelect'
import VueEvent from '@/utils/vue-event'
export default {
  name: 'DataTransfer',
  components: {
    DataTable,
    DataSelect
  },
  props: {
    leftPermission: {
      type: String,
      default: null
    },
    rightPermission: {
      type: String,
      default: null
    },
    leftSearchData: {
      // 左侧搜索数据
      type: Object,
      default: () => {
        return {}
      }
    },
    leftRules: {
      // 左侧校验信息
      type: Object,
      default: null
    },
    rightSearchData: {
      // 右侧搜索数据
      type: Object,
      default: () => {
        return {}
      }
    },
    rightRules: {
      // 右侧校验信息
      type: Object,
      default: null
    },
    leftTableOption: {
      // 左侧表格设置
      type: Object,
      default: () => {
        return { option: {}, event: {}}
      }
    },
    leftColumn: {
      // 左侧表格列数据
      type: Array,
      default: () => {
        return []
      }
    },
    // 数据
    leftTableData: {
      // 左侧表格数据
      type: Array,
      default: () => {
        return []
      }
    },
    // 分页
    leftPagination: {
      // 左侧分页
      type: Object,
      default: null
    },
    rightTableOption: {
      // 右侧表格设置
      type: Object,
      default: () => {
        return { option: {}, event: {}}
      }
    },
    rightColumn: {
      // 右侧表格列数据
      type: Array,
      default: () => {
        return []
      }
    },
    // 数据
    rightTableData: {
      // 右侧表格数据
      type: Array,
      default: () => {
        return []
      }
    },
    // 分页
    rightPagination: {
      // 右侧分页
      type: Object,
      default: null
    },
    autoResetPagNum: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      leftInnerPagination: {},
      rightInnerPagination: {},
      leftSelection: [],
      rightSelection: [],
      leftInnerTableData: [],
      rightInnerTableData: [],
      nbsp: this.$constant.nbsp
    }
  },
  computed: {
    leftInnerColumn() {
      const column = []
      this.leftColumn.forEach(value => {
        value.sortable = false
        column.push(value)
      })
      return { data: column }
    },
    rightInnerColumn() {
      const column = []
      this.rightColumn.forEach(value => {
        value.sortable = false
        column.push(value)
      })
      return { data: column }
    },
    leftInnerTableOption() {
      const that = this
      const innerOption = {}
      innerOption.option = Object.assign({}, this.leftTableOption.option, {
        enableSelected: true
      })
      innerOption.event = Object.assign({}, this.leftTableOption.event, {
        selectionChange(selection) {
          that.leftSelection = selection
          if (
            that.leftTableOption?.event &&
            typeof that.leftTableOption.event.selectionChange === 'function'
          ) {
            that.leftTableOption.event.selectionChange(selection)
          }
        },
        selectAll(selection) {
          this.leftSelection = selection
          if (
            that.leftTableOption?.event &&
            typeof that.leftTableOption.event.selectAll === 'function'
          ) {
            that.leftTableOption.event.selectAll(selection)
          }
        }
      })
      return innerOption
    },
    rightInnerTableOption() {
      const that = this
      const innerOption = {}
      innerOption.option = Object.assign({}, this.rightTableOption.option, {
        enableSelected: true
      })
      innerOption.event = Object.assign({}, this.rightTableOption.event, {
        selectionChange(selection) {
          that.rightSelection = selection
          if (
            that.rightTableOption?.event &&
            typeof that.rightTableOption.event.selectionChange === 'function'
          ) {
            that.rightTableOption.event.selectionChange(selection)
          }
        },
        selectAll(selection) {
          this.rightSelection = selection
          if (
            that.rightTableOption?.event &&
            typeof that.rightTableOption.event.selectAll === 'function'
          ) {
            that.rightTableOption.event.selectAll(selection)
          }
        }
      })
      return innerOption
    }
  },
  watch: {
    leftPagination: {
      handler(val) {
        this.leftInnerPagination = val
      },
      deep: true
    },
    rightPagination: {
      handler(val) {
        this.rightInnerPagination = val
      },
      deep: true
    },
    leftTableData: {
      handler(val) {
        this.leftInnerTableData = val
      },
      deep: true
    },
    rightTableData: {
      handler(val) {
        this.rightInnerTableData = val
      },
      deep: true
    }
  },
  created() {
    VueEvent.$on('data.transfer.resize', this.resizeTableHeight)
  },
  beforeDestroy() {
    VueEvent.$off('data.transfer.resize')
  },
  mounted() {
    this.leftInnerPagination = this.leftPagination
    this.rightInnerPagination = this.rightPagination
    this.leftInnerTableData = this.leftTableData
    this.rightInnerTableData = this.rightTableData
  },
  methods: {
    updateleftSearchData(val) {
      this.$emit('update:left-search-data', val)
    },
    updateRightSearchData(val) {
      this.$emit('update:right-search-data', val)
    },
    resizeTableHeight() {
      let height = 0
      if (document.getElementsByClassName('transfer-select')[0]) {
        height = document.getElementsByClassName('transfer-select')[0]
          .offsetHeight
      }
      VueEvent.$emit('data.table.resize', height)
    },
    rightBtnClick() {
      this.$emit(
        'move-to-right',
        this.leftSelection,
        this.$refs.leftSelectData.getAllParams()
      )
    },
    leftBtnClick() {
      this.$emit(
        'move-to-left',
        this.rightSelection,
        this.$refs.rightSelectData.getAllParams()
      )
    },
    leftReturnSearch(params) {
      this.leftInnerPagination.pageNum = 1
      this.$emit('update:leftPagination', this.leftInnerPagination)
      this.$emit('left-return-search', params)
    },
    rightReturnSearch(params) {
      this.rightInnerPagination.pageNum = 1
      this.$emit('update:rightPagination', this.rightInnerPagination)
      this.$emit('right-return-search', params)
    },
    leftReturnReset() {
      this.$emit('left-return-reset')
    },
    rightReturnReset() {
      this.$emit('right-return-reset')
    },
    leftSearchEvent() {
      this.$emit('update:leftPagination', this.leftInnerPagination)
      this.$emit(
        'left-return-search',
        this.$refs.leftSelectData.getAllParams()
      )
    },
    rightSearchEvent() {
      this.$emit('update:rightPagination', this.rightInnerPagination)
      this.$emit(
        'right-return-search',
        this.$refs.rightSelectData.getAllParams()
      )
    }
  }
}
</script>

<style scoped>
.flex-between {
  position: relative;
}
.el-row {
  width: 100%;
}
.el-button + .el-button {
  margin-left: 0;
  margin-top: 10px;
}
.el-button {
  width: 60%;
  padding: 0 10px;
}
</style>
<style>
.transfer-button {
  /*display: flex;*/
  /*align-items: center; !*定义body的元素垂直居中*!*/
  /*justify-content: center; !*定义body的里的元素水平居中*!*/
}
</style>

<template>
  <div class="app-container">
    <el-container :style="{ height: '100%' }">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item title="基本信息" :name="1">
            <el-form
              ref="basic"
              label-position="top"
              :model="basicFormModel"
              :rules="basicFormRules"
              :disabled="action === 'detail'"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="materialCode" label="物料编码：">
                    <el-input
                      v-model="basicFormModel.materialCode"
                      placeholder="请输入物料编码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="materialName" label="物料名称：">
                    <el-input
                      v-model="basicFormModel.materialName"
                      placeholder="请输入物料名称"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="materialType" label="物料类型：">
                    <el-select
                      v-model="basicFormModel.materialType"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in materialTypeList"
                        :key="item.value"
                        v-bind="item"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="materialGroup" label="物料组：">
                    <el-select
                      v-model="basicFormModel.materialGroup"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in materialGroupList"
                        :key="item.value"
                        v-bind="item"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="unit" label="单位：">
                    <el-select
                      v-model="basicFormModel.unit"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in unitList"
                        :key="item.value"
                        v-bind="item"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="specification" label="规格：">
                    <el-input
                      v-model="basicFormModel.specification"
                      :placeholder="action === 'detail' ? '' : '请输入'"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="packageSpecification" label="包装规格：">
                    <el-input
                      v-model="basicFormModel.packageSpecification"
                      :placeholder="action === 'detail' ? '' : '请输入包装规格'"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="inspectionStandard" label="检验标准：">
                    <el-input
                      v-model="basicFormModel.inspectionStandard"
                      :placeholder="action === 'detail' ? '' : '请输入检验标准'"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-form-item
                  prop="materialDescription"
                  label="物料描述："
                  style="width: 98%"
                >
                  <el-input
                    v-model="basicFormModel.materialDescription"
                    :maxlength="200"
                    type="textarea"
                    :rows="3"
                    :placeholder="action === 'detail' ? '' : '请输入物料描述'"
                  />
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item prop="remark" label="备注：" style="width: 98%">
                  <el-input
                    v-model="basicFormModel.remark"
                    :maxlength="200"
                    type="textarea"
                    :rows="3"
                    :placeholder="action === 'detail' ? '' : '请输入备注'"
                  />
                </el-form-item>
              </el-row>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="属性信息" :name="2">
            <virtual-table
              v-if="action === 'detail'"
              :enable-search="false"
              auto-height
              :columns="specColumnsShow"
              :table-data="specTableData"
            >
              <template v-slot:attrNameCn="{ row }">
                {{ row.attrNameCn }}
              </template>
              <template v-slot:attrNameEn="{ row }">
                {{ row.attrNameEn }}
              </template>
              <template v-slot:attrIndex="{ row }">
                {{ row.attrIndex }}
              </template>
              <template v-slot:attrValue="{ row }">
                {{ row.attrValue }}
              </template>
              <template v-slot:attrType="{ row }">
                {{ attrTypeObj[row.attrType] }}
              </template>
            </virtual-table>

            <div v-else>
              <virtual-table
                ref="attr-table"
                :enable-search="false"
                auto-height
                :columns="specColumns"
                :table-data="specTableData"
                :edit-rules="specRules"
              >
                <template v-slot:attrNameCn="{ row, scope }">
                  <el-input
                    v-model="row.attrNameCn"
                    :placeholder="action === 'detail' ? '' : '请输入属性名'"
                    @change="updateTableStatus(scope)"
                  />
                </template>
                <template v-slot:attrNameEn="{ row, scope }">
                  <el-input
                    v-model="row.attrNameEn"
                    :placeholder="action === 'detail' ? '' : '请输入属性英文名'"
                    @change="updateTableStatus(scope)"
                  />
                </template>
                <template v-slot:attrIndex="{ row, scope }">
                  <el-input
                    v-model="row.attrIndex"
                    :placeholder="action === 'detail' ? '' : '请输入属性排序'"
                    @change="updateTableStatus(scope)"
                  />
                </template>
                <template v-slot:attrValue="{ row, scope }">
                  <el-input
                    v-model="row.attrValue"
                    :placeholder="action === 'detail' ? '' : '请输入属性值'"
                    @change="updateTableStatus(scope)"
                  />
                </template>
                <template v-slot:attrType="{ row, $index, scope }">
                  <el-select
                    v-model="row.attrType"
                    :placeholder="action === 'detail' ? '' : '请选择属性类型'"
                    @change="updateTableStatus(scope)"
                  >
                    <el-option :value="10" label="公有" />
                    <el-option :value="20" label="私有" />
                  </el-select>
                </template>
                <template v-slot:operate="{ $index }">
                  <span class="text_button" @click="deleteSpec($index)"
                    >删除</span
                  >
                </template>
              </virtual-table>
              <demo-block
                message="添加属性"
                :icon-class="'el-icon-plus icon-class'"
                @click.native="addSpec"
              />
            </div>
          </el-collapse-item>
          <el-collapse-item title="产品图片" :name="3">
            <virtual-table
              ref="table"
              :enable-search="false"
              auto-height
              :columns="imgColumns"
              :table-data="imgTableData"
              :row-height="100"
            >
              <template v-slot:imgPath="{ row }">
                <upload-img :img-list="[{ url: row.imgPath }]" readonly />
              </template>
              <template v-slot:operate="{ $index }">
                <span
                  v-if="action != 'detail'"
                  class="text_button"
                  @click="deleteImg($index)"
                  >删除</span
                >
              </template>
            </virtual-table>
            <demo-block
              v-if="action !== 'detail'"
              message="添加图片"
              :icon-class="'el-icon-plus icon-class'"
              @click.native="addImg"
            />
          </el-collapse-item>
        </el-collapse>
      </el-main>
      <el-footer class="button-container">
        <el-button @click="back">返回</el-button>
        <el-button type="primary" v-if="action != 'detail'" @click="submit"
          >提交</el-button
        >
      </el-footer>
    </el-container>
    <simple-data-dialog
      v-if="imgDialogVisible"
      title="添加产品图片"
      :visible="true"
      size="small"
    >
      <el-form
        ref="imgForm"
        label-position="top"
        :model="imgFormModel"
        :rules="imgFormRules"
        :inline="true"
      >
        <el-form-item
          prop="fileList"
          label="产品图片："
          class="el-form-item-width"
        >
          <upload-img :limit-count="1" :img-list.sync="imgFormModel.fileList" />
        </el-form-item>
        <el-form-item prop="remark" label="说明：" style="width: 100%">
          <el-input
            v-model="imgFormModel.remark"
            type="textarea"
            placeholder="请输入图片说明"
            maxlength="200"
          />
        </el-form-item>
      </el-form>
      <el-footer class="button-container">
        <el-button @click="imgDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveImage">保存</el-button>
      </el-footer>
    </simple-data-dialog>
  </div>
</template>

<script>
import VirtualTable from "@/components/VirtualTable";
import DemoBlock from "@/components/DemoBlock/index.vue";
import UploadImg from "@/components/DataDialog/uploadImg.vue";
import SimpleDataDialog from "@/components/SimpleDataDialog/index.vue";
import accApi from "@/api/acc/acc";

export default {
  name: "MaterialDetail",
  components: { SimpleDataDialog, UploadImg, DemoBlock, VirtualTable },
  data() {
    return {
      attrTypeObj: {
        10: "公有",
        20: "私有",
      },
      materialTypeList: [],
      materialGroupList: [],
      unitList: [],
      action: null,
      imgDialogVisible: false,
      collapse: [1, 2, 3],
      basicFormModel: {
        materialCode: "",
        materialName: "",
        materialType: "",
        materialGroup: "",
        unit: "",
        specification: "",
        packageSpecification: "",
        inspectionStandard: "",
        materialDescription: "",
        remark: "",
      },
      basicFormRules: {
        materialCode: [
          { required: true, message: "请输入物料编码", trigger: "blur" },
        ],
        materialName: [
          { required: true, message: "请输入物料名称", trigger: "blur" },
        ],
        materialType: [
          { required: true, message: "请选择物料类型", trigger: "change" },
        ],
        materialGroup: [
          { required: true, message: "请选择物料组", trigger: "change" },
        ],
        unit: [{ required: true, message: "请选择单位", trigger: "change" }],
      },
      specTableData: [],
      // 表头
      specColumns: [
        {
          type: "seq",
          title: "序号",
        },
        {
          title: "属性名",
          field: "attrNameCn",
          slotName: "attrNameCn",
        },
        {
          title: "属性英文名",
          field: "attrNameEn",
          slotName: "attrNameEn",
        },
        {
          title: "属性排序",
          field: "attrIndex",
          slotName: "attrIndex",
        },
        {
          title: "属性值",
          field: "attrValue",
          slotName: "attrValue",
        },
        {
          title: "属性类型",
          field: "attrType",
          slotName: "attrType",
        },
        {
          title: "操作",
          field: "operate",
          slotName: "operate",
          width: "50px",
        },
      ],
      specColumnsShow: [
        {
          type: "seq",
          title: "序号",
        },
        {
          title: "属性名",
          field: "attrNameCn",
          slotName: "attrNameCn",
        },
        {
          title: "属性英文名",
          field: "attrNameEn",
          slotName: "attrNameEn",
        },
        {
          title: "属性排序",
          field: "attrIndex",
        },
        {
          title: "属性值",
          field: "attrValue",
        },
        {
          title: "属性类型",
          field: "attrType",
          slotName: "attrType",
        },
      ],
      specRules: {
        attrNameCn: [{ required: true, message: "请输入属性名" }],
        attrNameEn: [{ required: true, message: "请输入属性英文名" }],
        attrIndex: [{ required: true, message: "请输入属性排序号" }],
        attrValue: [{ required: true, message: "请输入属性值" }],
        attrType: [{ required: true, message: "请输入属性类型" }],
      },
      imgColumns: [
        {
          type: "seq",
          title: "序号",
        },
        {
          title: "产品图片",
          field: "imgPath",
          slotName: "imgPath",
        },
        {
          title: "说明",
          field: "remark",
        },
        {
          title: "操作",
          field: "operate",
          slotName: "operate",
          width: "50px",
        },
      ],
      imgTableData: [],
      imgFormModel: {
        fileList: [],
        remark: "",
      },
      imgFormRules: {
        fileList: { required: true, message: "请上传图片" },
        remark: { required: true, message: "请输入图片说明" },
      },
    };
  },
  created() {
    this.action = this.$route.query.action;
    this.getAllDictList();
    if (this.action === "edit" || this.action === "detail") {
      this.queryProduct();
    }
  },
  methods: {
    // 查询详情
    queryProduct() {
      accApi.queryMaterialDetail({ id: this.$route.query.id }).then((res) => {
        const obj = res.data;
        this.basicFormModel = obj;
        this.specTableData = obj.attrList || [];
        this.imgTableData = obj.imgList || [];
      });
    },
    getAllDictList() {
      // 物料类型
      accApi.dictionaryApi({ dictTypeId: "3" }).then((res) => {
        this.materialTypeList = res.data.map((item) => {
          return {
            label: item.name,
            value: item.code,
          };
        });
      });
      // 物料组
      accApi.dictionaryApi({ dictTypeId: "4" }).then((res) => {
        this.materialGroupList = res.data.map((item) => {
          return {
            label: item.name,
            value: item.code,
          };
        });
      });
      // 单位
      accApi.dictionaryApi({ dictTypeId: "1" }).then((res) => {
        this.unitList = res.data.map((item) => {
          return {
            label: item.name,
            value: item.code,
          };
        });
      });
    },
    addSpec() {
      this.specTableData.push({
        attrType: null,
        attrValue: null,
        attrIndex: null,
        attrNameEn: null,
        attrNameCn: null,
      });
    },
    deleteSpec(index) {
      this.specTableData.splice(index, 1);
    },
    updateTableStatus(scope) {
      // 如果 VirtualTable 组件有 updateStatus 方法，可以调用它
      if (this.$refs["attr-table"] && this.$refs["attr-table"].updateStatus) {
        this.$refs["attr-table"].updateStatus(scope);
      }
      // 或者在这里处理表格状态更新逻辑
      console.log("Table status updated for scope:", scope);
    },
    addImg() {
      this.imgDialogVisible = true;
      this.imgFormModel = {
        fileList: [],
        remark: "",
      };
    },
    deleteImg(index) {
      this.imgTableData.splice(index, 1);
    },
    saveImage() {
      this.$refs["imgForm"].validate((valid) => {
        if (valid) {
          this.imgTableData.push({
            imgPath: this.imgFormModel.fileList[0].url,
            remark: this.imgFormModel.remark,
          });
          this.imgDialogVisible = false;
        }
      });
    },
    back() {
      this.$router.replace("materialList");
    },
    submit() {
      this.$refs.basic.validate((valid) => {
        if (valid) {
          this.$refs["table"].validate((attrValid) => {
            if (attrValid) {
              const api =
                this.action === "edit" ? "editMaterial" : "addMaterial";
              accApi[api]({
                ...this.basicFormModel,
                id: this.$route.query.id,
                attrList: this.specTableData || [],
                imgList: this.imgTableData || [],
              }).then((res) => {
                this.$message.success(res.message);
                this.back();
              });
            }
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.button-container {
  line-height: 60px;
  text-align: center;
}
</style>

<template>
  <div class="hw-upload">
    <el-upload
      ref="hwUpload"
      v-bind="propsObj"
      :file-list="currentFileList"
      :on-change="handleFileChange"
      v-on="$listeners"
    >
      <slot v-if="fileUploadType === `file`" name="fileUpload">
        <el-button size="small" type="primary">{{ btnText }}</el-button>
      </slot>
      <slot v-else-if="fileUploadType === `image`" name="imageUpload">
        <el-button size="small" type="primary">{{ btnText }}</el-button>
      </slot>
      <template #trigger>
        <slot name="trigger" />
      </template>
      <template #tip>
        <slot name="tip" />
      </template>
    </el-upload>
  </div>
</template>
<script>
export default {
  name: `uploadFileList`,
  props: {
    // 文件上传按钮默认文字
    btnText: {
      type: String,
      default: `点击上传`
    },
    // 文件大小限制
    limitSize: {
      type: Number,
      default: 20
    },
    // 文件列表
    fileList: {
      type: Array,
      default: () => []
    },
    // 是否校验文件大小
    verifyFileSize: {
      type: <PERSON><PERSON>an,
      default: true
    },
    fileUploadType: {
      type: String,
      default: `file`
    },
    // 文件状态改变时的钩子
    onChange: {
      type: Function,
      required: true
    }

  },
  data() {
    return {}
  },
  computed: {
    currentFileList: {
      get() {
        return this.fileList
      },
      set(val) {
        this.$emit(`update:fileList`, val)
      }
    },
    propsObj() {
      return {
        'auto-upload': false,
        action: ``,
        'before-remove': this.beforeRemove,
        'on-remove': this.handleRemove,
        ...this.$attrs
      }
    }
  },
  created() {},
  methods: {
    // 校验文件大小
    handleVerifyFileSize(file, fileList) {
      if (file.size > this.limitSize * 1024 * 1024) {
        this.$message({
          type: 'error',
          message: `文件大小不得超过${this.limitSize}M，请重新上传！`
        })
        fileList.pop()
        return false
      }
      return true
    },
    // 文件状态改变时的钩子
    handleFileChange(file, fileList) {
      if (this.verifyFileSize) {
        this.handleVerifyFileSize(file, fileList) &&
          this.onChange(file, fileList)
      } else {
        this.onChange(file, fileList)
      }
    },
    // 文件移除
    handleRemove(file, fileList) {
      this.currentFileList = fileList
    },
    // 文件移除之前
    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`)
    }
  }
}
</script>
<style lang="scss" scoped>
.hw-upload {
  .img-list {
    display: flex;
    flex-wrap: wrap;
    li {
      display: flex;
      flex-direction: column;
      position: relative;
      margin-top: 10px;
    }
  }
}
</style>

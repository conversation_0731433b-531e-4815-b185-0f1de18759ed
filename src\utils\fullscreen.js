import screenfull from 'screenfull'
import $util from '@/utils/util'
let callbackChangeFun
export function defaultfullScreen(element) {
  screenfull.request()
  screenfull.on('change', change)
}
export function toggleFullscreen(element, callback) {
  if (!screenfull.isEnabled) {
    this.$message({
      message: 'you browser can not work',
      type: 'warning'
    })
    return false
  }
  screenfull.toggle(element)
  setTimeout(() => {
    typeof (callback) && typeof (callback) === 'function' && callback(screenfull.isFullscreen)
  }, 100)
}

function change() {
  typeof (callbackChangeFun) && typeof (callbackChangeFun) === 'function' && callbackChangeFun(screenfull.isFullscreen)
}
// 添加监听全屏事件
export function fullscreenChange(callback) {
  callbackChangeFun = callback
  if (screenfull.isEnabled) {
    screenfull.on('change', change)
  }
}
// 移除监听全屏事件
export function removeFullscreenChange() {
  if (screenfull.isEnabled) {
    screenfull.off('change', change)
  }
}

export function toggleHalfFullscreen(element, callback) {
  if (!element) {
    return false
  }
  $util[$util.hasClass(element, 'fullscreenHalf') ? 'removeClass' : 'addClass'](element, 'fullscreenHalf')
  setTimeout(() => {
    typeof (callback) && typeof (callback) === 'function' && callback($util.hasClass(element, 'fullscreenHalf'))
  }, 20)
}

